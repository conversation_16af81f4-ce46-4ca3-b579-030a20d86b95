package com.hightop.benyin.order.domain.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hightop.benyin.order.infrastructure.entity.DeliveryOrder;
import com.hightop.benyin.order.infrastructure.mapper.DeliveryOrderMapper;
import org.springframework.stereotype.Service;

/**
 * 履约单领域服务
 * <AUTHOR>
 * @date 2023-11-10 14:50:20
 */
@Service
public class DeliverOrderDomainService extends ServiceImpl<DeliveryOrderMapper, DeliveryOrder> {
    /**
     * 根据订单id获取履约单主单
     * @param orderId 订单id
     * @return {@link DeliveryOrder}
     */
    public DeliveryOrder getByOrderId(Long orderId) {
        return super.lambdaQuery().eq(DeliveryOrder::getTradeOrderId, orderId).eq(DeliveryOrder::getDeleted, false).one();
    }
}
