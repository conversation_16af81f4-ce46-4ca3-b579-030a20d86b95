package com.hightop.benyin.logdata.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.hightop.benyin.logdata.domain.infrastructure.entity.JobLogData;
import com.hightop.benyin.logdata.domain.infrastructure.mapper.JobLogDataMapper;
import org.springframework.stereotype.Service;

@Service
public class JobLogDataDomainService extends MPJBaseServiceImpl<JobLogDataMapper, JobLogData> {
}
