package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 配置分配情况DTO
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@ApiModel("配置分配情况")
public class ConfigAssignmentDto {

    @ApiModelProperty("分发关系ID")
    private Long distributionId;

    @ApiModelProperty("配置ID")
    private Long configId;

    @ApiModelProperty("目标类型")
    private String targetType;

    @ApiModelProperty("目标ID")
    private String targetId;

    @ApiModelProperty("目标名称")
    private String targetName;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("分配的配置版本")
    private String assignedVersion;

    @ApiModelProperty("设备当前版本")
    private String currentVersion;

    @ApiModelProperty("分发状态")
    private String distributionStatus;

    @ApiModelProperty("分配时间")
    private LocalDateTime assignTime;

    @ApiModelProperty("激活状态")
    private Boolean isActive;

    // 保留原有字段以兼容
    @ApiModelProperty("配置版本（兼容字段）")
    private String configVersion;

    @ApiModelProperty("日志级别（兼容字段）")
    private String logLevel;

    @ApiModelProperty("最后使用时间（兼容字段）")
    private LocalDateTime lastUsedTime;
}
