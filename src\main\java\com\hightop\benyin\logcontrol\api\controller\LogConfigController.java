package com.hightop.benyin.logcontrol.api.controller;


import com.hightop.fario.base.web.RestResponse;
import com.hightop.benyin.logcontrol.application.service.LogConfigService;
import com.hightop.benyin.logcontrol.application.service.DeviceInfoService;
import com.hightop.benyin.logcontrol.application.service.ConfigDistributionService;
import com.hightop.benyin.logcontrol.dto.LogConfigDto;
import com.hightop.benyin.logcontrol.dto.ConfigTemplateDto;
import com.hightop.benyin.logcontrol.dto.BatchAssignRequest;
import com.hightop.benyin.logcontrol.dto.BatchAssignResult;
import com.hightop.benyin.logcontrol.dto.ConfigAssignmentDto;
import com.hightop.benyin.logcontrol.dto.CreateFromTemplateRequest;
import com.hightop.benyin.logcontrol.dto.ConfigUpdateInfo;
import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 日志配置控制器
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@RestController
@RequestMapping("/logcontrol/config")
@Api(tags = "日志配置管理")
@Validated
public class LogConfigController {

    @Autowired
    private LogConfigService logConfigService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private ConfigDistributionService configDistributionService;

    @GetMapping("/test")
    @ApiOperation("测试接口")
    public RestResponse<String> test() {
        return RestResponse.ok("LogControl模块正常工作");
    }

    @GetMapping("/get")
    @ApiOperation("获取激活的日志配置")
    public RestResponse<LogConfigDto> getLogConfig(
            @RequestHeader(value = "X-Device-Id", required = false) @ApiParam("设备ID") String deviceId,
            @RequestHeader(value = "X-App-Version", required = false) @ApiParam("应用版本") String appVersion,
            @RequestHeader(value = "X-User-Id", required = false) @ApiParam("用户ID") String userId) {

        try {
            LogConfigDto config = getConfigWithPriority(userId, deviceId);

            // 异步更新设备配置信息（不影响主流程）
            if (StringUtils.hasText(deviceId)) {
                updateDeviceConfigAsync(deviceId, config);
            }

            return RestResponse.ok(config);
        } catch (Exception e) {
            log.error("获取日志配置失败，用户ID: {}, 设备ID: {}, 应用版本: {}", userId, deviceId, appVersion, e);
            return RestResponse.ok(logConfigService.getActiveConfig()); // 降级到默认配置
        }
    }

    /**
     * 按优先级获取配置：用户配置 > 设备配置 > 默认配置
     * 重构：从分发关系表查询配置
     */
    private LogConfigDto getConfigWithPriority(String userId, String deviceId) {
        try {
            // 1. 优先查询分发关系表中的用户配置
            LogConfigDto config = null;
            if (StringUtils.hasText(userId)) {
                config = getConfigFromDistribution("USER", userId);
                if (config != null) {
                    log.info("为用户 {} 返回分发配置，配置ID: {}", userId, config.getId());
                    return config;
                }
            }

            // 2. 查询分发关系表中的设备配置
            if (StringUtils.hasText(deviceId)) {
                config = getConfigFromDistribution("DEVICE", deviceId);
                if (config != null) {
                    log.info("为设备 {} 返回分发配置，配置ID: {}", deviceId, config.getId());
                    return config;
                }
            }

            // 3. 返回默认激活配置
            log.info("返回默认激活配置");
            return logConfigService.getActiveConfig();

        } catch (Exception e) {
            log.error("获取配置失败，返回默认配置", e);
            return logConfigService.getActiveConfig();
        }
    }

    /**
     * 从分发关系表获取配置（优先返回分发关系中的配置，即使配置被停用）
     */
    private LogConfigDto getConfigFromDistribution(String targetType, String targetId) {
        // 查询分发关系，按分配时间排序
        List<ConfigDistribution> distributions = configDistributionService.getActiveDistributionsByTarget(targetType, targetId);

        if (!distributions.isEmpty()) {
            // 获取最新分配的配置
            ConfigDistribution topDistribution = distributions.get(0);
            // 优先返回分发关系中的配置，不检查配置的激活状态
            return logConfigService.getConfigByIdIgnoreStatus(topDistribution.getConfigId());
        }

        return null;
    }

    @GetMapping("/get-by-name")
    @ApiOperation("根据配置名称获取配置")
    public RestResponse<LogConfigDto> getConfigByName(
            @RequestParam @NotBlank(message = "配置名称不能为空") @ApiParam("配置名称") String configName) {
        
        try {
            LogConfigDto config = logConfigService.getConfigByName(configName);
            if (config == null) {
                return RestResponse.ok(null);
            }
            return RestResponse.ok(config);
        } catch (Exception e) {
            log.error("根据配置名称获取配置失败，配置名称: {}", configName, e);
            return RestResponse.ok(null);
        }
    }

    @GetMapping("/get-by-version")
    @ApiOperation("根据配置版本获取配置")
    public RestResponse<LogConfigDto> getConfigByVersion(
            @RequestParam @NotBlank(message = "配置版本不能为空") @ApiParam("配置版本") String configVersion) {
        
        try {
            LogConfigDto config = logConfigService.getConfigByVersion(configVersion);
            if (config == null) {
                return RestResponse.ok(null);
            }
            return RestResponse.ok(config);
        } catch (Exception e) {
            log.error("根据配置版本获取配置失败，配置版本: {}", configVersion, e);
            return RestResponse.ok(null);
        }
    }

    @GetMapping("/list")
    @ApiOperation("获取所有配置列表")
    public RestResponse<List<LogConfigDto>> getAllConfigs() {
        try {
            List<LogConfigDto> configs = logConfigService.getAllConfigs();
            return RestResponse.ok(configs);
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/update")
    @ApiOperation("更新日志配置")
    public RestResponse<Void> updateConfig(@RequestBody @Valid @ApiParam("配置信息") LogConfigDto configDto) {
        try {
            boolean success = logConfigService.updateConfig(configDto);
            if (success) {
                return RestResponse.ok(null);
            } else {
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("更新日志配置失败", e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/activate/{id}")
    @ApiOperation("激活指定配置")
    public RestResponse<Void> activateConfig(
            @PathVariable @NotNull(message = "配置ID不能为空") @ApiParam("配置ID") Long id) {
        
        try {
            boolean success = logConfigService.activateConfig(id);
            if (success) {
                return RestResponse.ok(null);
            } else {
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("激活配置失败，配置ID: {}", id, e);
            return RestResponse.ok(null);
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除配置")
    public RestResponse<Void> deleteConfig(
            @PathVariable @NotNull(message = "配置ID不能为空") @ApiParam("配置ID") Long id) {

        try {
            boolean success = logConfigService.deleteConfig(id);
            if (success) {
                return RestResponse.ok(null);
            } else {
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("删除配置失败，配置ID: {}", id, e);
            return RestResponse.ok(null);
        }
    }

    @GetMapping("/templates")
    @ApiOperation("获取配置模板列表")
    public RestResponse<List<ConfigTemplateDto>> getConfigTemplates() {
        try {
            List<ConfigTemplateDto> templates = logConfigService.getConfigTemplates();
            return RestResponse.ok(templates);
        } catch (Exception e) {
            log.error("获取配置模板列表失败", e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/assign-to-user")
    @ApiOperation("为用户分配配置")
    public RestResponse<Void> assignConfigToUser(
            @RequestParam @NotBlank String userId,
            @RequestParam @NotNull Long configId) {

        try {
            // 验证配置是否存在
            LogConfigDto config = logConfigService.getConfigById(configId);
            if (config == null) {
                return RestResponse.ok(null);
            }

            // 创建目标对象
            BatchAssignRequest.AssignTarget target = new BatchAssignRequest.AssignTarget();
            target.setTargetType("USER");
            target.setTargetId(userId);
            target.setTargetName("用户-" + userId);

            // 使用统一的创建或更新方法（默认覆盖现有分配）
            boolean success = configDistributionService.createOrUpdateDistribution(configId, target, true);

            if (success) {
                log.info("用户 {} 配置分配成功，配置ID: {}", userId, configId);
                return RestResponse.ok(null);
            } else {
                return RestResponse.ok(null);
            }

        } catch (Exception e) {
            log.error("为用户分配配置失败，用户ID: {}, 配置ID: {}", userId, configId, e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/assign-to-device")
    @ApiOperation("为设备分配配置")
    public RestResponse<Void> assignConfigToDevice(
            @RequestParam @NotBlank String deviceId,
            @RequestParam @NotNull Long configId) {

        try {
            // 验证配置是否存在
            LogConfigDto config = logConfigService.getConfigById(configId);
            if (config == null) {
                return RestResponse.ok(null);
            }

            // 创建目标对象
            BatchAssignRequest.AssignTarget target = new BatchAssignRequest.AssignTarget();
            target.setTargetType("DEVICE");
            target.setTargetId(deviceId);
            target.setTargetName("设备-" + deviceId);

            // 使用统一的创建或更新方法（默认覆盖现有分配）
            boolean success = configDistributionService.createOrUpdateDistribution(configId, target, true);

            if (success) {
                log.info("设备 {} 配置分配成功，配置ID: {}", deviceId, configId);
                return RestResponse.ok(null);
            } else {
                return RestResponse.ok(null);
            }

        } catch (Exception e) {
            log.error("为设备分配配置失败，设备ID: {}, 配置ID: {}", deviceId, configId, e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/assign-batch")
    @ApiOperation("批量分配配置")
    public RestResponse<BatchAssignResult> batchAssignConfig(@RequestBody BatchAssignRequest request) {
        try {
            // 验证配置是否存在
            Long configId = Long.parseLong(request.getConfigSource());
            LogConfigDto config = logConfigService.getConfigById(configId);
            if (config == null) {
                return RestResponse.ok(null);
            }

            // 批量创建分发关系
            BatchAssignResult result = configDistributionService.batchCreateDistributions(
                configId, request.getTargets(), request.getOverrideExisting());

            return RestResponse.ok(result);
        } catch (Exception e) {
            log.error("批量分配配置失败", e);
            return RestResponse.ok(null);
        }
    }

    @PostMapping("/create-from-template")
    @ApiOperation("从模板创建配置")
    public RestResponse<LogConfigDto> createConfigFromTemplate(@RequestBody CreateFromTemplateRequest request) {
        try {
            LogConfigDto config = logConfigService.createConfigFromTemplate(request);
            return RestResponse.ok(config);
        } catch (Exception e) {
            log.error("从模板创建配置失败", e);
            return RestResponse.ok(null);
        }
    }

    @GetMapping("/assignments")
    @ApiOperation("获取配置分配情况")
    public RestResponse<List<ConfigAssignmentDto>> getConfigAssignments(
            @RequestParam(required = false) String targetType,
            @RequestParam(required = false) String keyword) {
        try {
            // 从分发关系表查询分配情况
            List<ConfigAssignmentDto> assignments = configDistributionService.getConfigAssignments(targetType, keyword);
            return RestResponse.ok(assignments);
        } catch (Exception e) {
            log.error("获取配置分配情况失败", e);
            return RestResponse.ok(null);
        }
    }

    @DeleteMapping("/assignment/{targetType}/{targetId}")
    @ApiOperation("移除配置分配")
    public RestResponse<Void> removeConfigAssignment(
            @PathVariable String targetType,
            @PathVariable String targetId) {
        try {
            // 这里需要根据targetType和targetId查找分发关系ID，然后删除
            // 暂时保留原有逻辑，后续可以优化
            boolean success = logConfigService.removeConfigAssignment(targetType, targetId);
            return RestResponse.ok(null);
        } catch (Exception e) {
            log.error("移除配置分配失败", e);
            return RestResponse.ok(null);
        }
    }

    @GetMapping("/check-updates")
    @ApiOperation("检查配置更新")
    public RestResponse<ConfigUpdateInfo> checkConfigUpdates(
            @RequestHeader(value = "X-Device-Id", required = false) String deviceId,
            @RequestHeader(value = "X-User-Id", required = false) String userId,
            @RequestParam(required = false) String currentVersion) {

        try {
            ConfigUpdateInfo updateInfo = new ConfigUpdateInfo();
            updateInfo.setHasUpdate(false);

            // 检查用户配置更新
            if (StringUtils.hasText(userId)) {
                List<ConfigDistribution> userDistributions = configDistributionService.getActiveDistributionsByTarget("USER", userId);
                if (!userDistributions.isEmpty()) {
                    ConfigDistribution distribution = userDistributions.get(0);
                    String assignedVersion = distribution.getAssignedVersion();
                    if (assignedVersion != null && !Objects.equals(currentVersion, assignedVersion)) {
                        updateInfo.setHasUpdate(true);
                        updateInfo.setLatestVersion(assignedVersion);
                        updateInfo.setCurrentVersion(currentVersion);
                        updateInfo.setConfigSource("USER_SPECIFIC");
                        updateInfo.setAssignTime(distribution.getAssignTime());
                        return RestResponse.ok(updateInfo);
                    }
                }
            }

            // 如果没有用户配置更新，检查设备配置
            if (StringUtils.hasText(deviceId)) {
                List<ConfigDistribution> deviceDistributions = configDistributionService.getActiveDistributionsByTarget("DEVICE", deviceId);
                if (!deviceDistributions.isEmpty()) {
                    ConfigDistribution distribution = deviceDistributions.get(0);
                    String assignedVersion = distribution.getAssignedVersion();
                    if (assignedVersion != null && !Objects.equals(currentVersion, assignedVersion)) {
                        updateInfo.setHasUpdate(true);
                        updateInfo.setLatestVersion(assignedVersion);
                        updateInfo.setCurrentVersion(currentVersion);
                        updateInfo.setConfigSource("DEVICE_SPECIFIC");
                        updateInfo.setAssignTime(distribution.getAssignTime());
                        return RestResponse.ok(updateInfo);
                    }
                }
            }

            return RestResponse.ok(updateInfo);
        } catch (Exception e) {
            log.error("检查配置更新失败", e);
            return RestResponse.ok(null);
        }
    }

    private String generateVersion() {
        // 委托给 Service 层的版本生成逻辑
        return "1.0." + System.currentTimeMillis() / 1000000; // 简化版本，实际应该调用 Service
    }

    /**
     * 异步更新设备配置信息
     */
    @Async
    private void updateDeviceConfigAsync(String deviceId, LogConfigDto config) {
        try {
            deviceInfoService.updateDeviceConfig(deviceId, config);
        } catch (Exception e) {
            log.warn("更新设备配置信息失败，设备ID: {}, 配置版本: {}, 错误: {}",
                deviceId, config.getConfigVersion(), e.getMessage());
        }
    }
}
