package com.hightop.benyin.logcontrol.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 配置分发关系实体类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("b_config_distribution")
public class ConfigDistribution {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("config_id")
    private Long configId;

    @TableField("target_type")
    private String targetType;

    @TableField("target_id")
    private String targetId;

    @TableField("target_name")
    private String targetName;

    @TableField("assign_time")
    private LocalDateTime assignTime;

    @TableField("is_active")
    private Boolean isActive;

    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    // 以下字段用于查询时关联获取，不对应数据库字段
    @TableField(exist = false)
    private String assignedVersion;  // 分配的配置版本
    
    @TableField(exist = false)
    private String currentVersion;   // 设备当前版本
    
    @TableField(exist = false)
    private String distributionStatus; // 计算得出的分发状态
    
    @TableField(exist = false)
    private String configName;      // 关联的配置名称
}
