package com.hightop.benyin.storage.domain.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.storage.infrastructure.entity.ArticlePrice;
import com.hightop.benyin.storage.infrastructure.entity.AssemblyArticleConfig;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.mapper.AssemblyArticleConfigMapper;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.core.exception.MaginaException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 组合商品配置领域服务
 *
 * <AUTHOR>
 * @date 2023-11-28 10:06:27
 */
@Service
public class AssemblyArticleConfigServiceDomain extends MPJBaseServiceImpl<AssemblyArticleConfigMapper, AssemblyArticleConfig> {

    /**
     * 保存数据
     *
     * @param assemblyArticleConfigs
     */
    public boolean saveAssemblyArticleConfig(List<AssemblyArticleConfig> assemblyArticleConfigs, StorageArticle storageArticle) {
        if (storageArticle.getArticleType().equals(0)) {
            return true;
        }
        this.remove(Wrappers.<AssemblyArticleConfig>lambdaQuery()
                .eq(AssemblyArticleConfig::getParentArticleId, storageArticle.getId()));
        assemblyArticleConfigs.forEach(assemblyArticleConfig -> {
            assemblyArticleConfig.setParentArticleId(storageArticle.getId());
            assemblyArticleConfig.setParentArticleCode(storageArticle.getCode());
            assemblyArticleConfig.setParentArticleName(storageArticle.getName());
            if(Objects.nonNull(assemblyArticleConfig.getNum())){
                assemblyArticleConfig.setNum(assemblyArticleConfig.getNum());
            }else{
                assemblyArticleConfig.setNum(1);
            }
            assemblyArticleConfig.setPrice(assemblyArticleConfig.getPrice());
            assemblyArticleConfig.setPurchasePrice(assemblyArticleConfig.getPurchasePrice());
        });

        return this.saveBatch(assemblyArticleConfigs);
    }

    public List<AssemblyArticleConfig> getAssemblyArticleConfigByParentArticleId(Long parentArticleId) {
        List<AssemblyArticleConfig> assemblyArticleConfigs = this.list(Wrappers.<AssemblyArticleConfig>lambdaQuery().eq(AssemblyArticleConfig::getParentArticleId, parentArticleId));
        if (CollectionUtils.isEmpty(assemblyArticleConfigs)) {
            throw new MaginaException("组合商品价格配置不存在");
        }
        return assemblyArticleConfigs;
    }

    public List<AssemblyArticleConfig> getAssemblyArticleConfigByParentArticleCode(String parentArticleCode) {
        List<AssemblyArticleConfig> assemblyArticleConfigs = this.list(Wrappers.<AssemblyArticleConfig>lambdaQuery().eq(AssemblyArticleConfig::getParentArticleCode, parentArticleCode));
        if (CollectionUtils.isEmpty(assemblyArticleConfigs)) {
            throw new MaginaException("组合商品价格配置不存在");
        }
        return assemblyArticleConfigs;
    }

    public List<String> getArticleCodesByParentArticleCode(String parentArticleCode) {
        return this.getAssemblyArticleConfigByParentArticleCode(parentArticleCode).stream().map(AssemblyArticleConfig::getArticleCode).collect(Collectors.toList());
    }

    public void removeByParentArticleId(Long parentArticleId) {
        this.remove(Wrappers.<AssemblyArticleConfig>lambdaQuery()
                .eq(AssemblyArticleConfig::getParentArticleId, parentArticleId));
    }


    public void updateAssemblyArticleConfig(List<ArticlePrice> articlePrices, Long parentArticleId) {
        List<AssemblyArticleConfig> assemblyArticleConfigs = this.getAssemblyArticleConfigByParentArticleId(parentArticleId);
        Map<String, Long> priceMap = articlePrices.stream().collect(Collectors.toMap(ArticlePrice::getArticleCode, ArticlePrice::getPrice));
        for (AssemblyArticleConfig assemblyArticleConfig : assemblyArticleConfigs) {
            if (priceMap.containsKey(assemblyArticleConfig.getArticleCode())) {
                assemblyArticleConfig.setPrice(priceMap.get(assemblyArticleConfig.getArticleCode()));
                this.updateById(assemblyArticleConfig);
            }
        }
    }
}
