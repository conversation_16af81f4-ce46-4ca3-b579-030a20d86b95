package com.hightop.benyin.logcontrol.domain.repository;

import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import java.util.List;

/**
 * 配置分发关系Repository接口
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface ConfigDistributionRepository {
    
    /**
     * 保存分发关系
     */
    boolean save(ConfigDistribution distribution);
    
    /**
     * 更新分发关系
     */
    boolean update(ConfigDistribution distribution);
    
    /**
     * 根据目标类型和ID查找激活的分发关系
     */
    List<ConfigDistribution> findActiveByTarget(String targetType, String targetId);
    
    /**
     * 查找所有分发关系（带状态计算）
     */
    List<ConfigDistribution> findDistributionsWithStatus(String targetType, String keyword);
    
    /**
     * 检查配置分发关系是否存在
     */
    boolean existsByConfigAndTarget(Long configId, String targetType, String targetId);
    
    /**
     * 根据目标类型查找分发关系
     */
    List<ConfigDistribution> findByTargetTypeAndDeleted(String targetType, Boolean deleted);
    
    /**
     * 查找所有分发关系
     */
    List<ConfigDistribution> findByDeleted(Boolean deleted);
    
    /**
     * 根据ID删除分发关系
     */
    boolean deleteById(Long id);
}
