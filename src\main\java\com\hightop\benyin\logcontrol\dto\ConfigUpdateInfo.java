package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 配置更新信息DTO
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@ApiModel("配置更新信息")
public class ConfigUpdateInfo {
    
    @ApiModelProperty("是否有更新")
    private Boolean hasUpdate = false;
    
    @ApiModelProperty("最新版本")
    private String latestVersion;
    
    @ApiModelProperty("当前版本")
    private String currentVersion;
    
    @ApiModelProperty("配置来源")
    private String configSource;
    
    @ApiModelProperty("分配时间")
    private LocalDateTime assignTime;
}
