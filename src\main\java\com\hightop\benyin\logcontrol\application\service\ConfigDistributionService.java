package com.hightop.benyin.logcontrol.application.service;

import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import com.hightop.benyin.logcontrol.domain.repository.ConfigDistributionRepository;
import com.hightop.benyin.logcontrol.dto.BatchAssignRequest;
import com.hightop.benyin.logcontrol.dto.BatchAssignResult;
import com.hightop.benyin.logcontrol.dto.ConfigAssignmentDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 配置分发关系服务类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
public class ConfigDistributionService {

    @Autowired
    private ConfigDistributionRepository configDistributionRepository;

    /**
     * 根据目标类型和ID获取激活的分发关系
     */
    public List<ConfigDistribution> getActiveDistributionsByTarget(String targetType, String targetId) {
        return configDistributionRepository.findActiveByTarget(targetType, targetId);
    }

    /**
     * 保存分发关系
     */
    @Transactional
    public boolean saveDistribution(ConfigDistribution distribution) {
        try {
            return configDistributionRepository.save(distribution);
        } catch (Exception e) {
            log.error("保存配置分发关系失败", e);
            return false;
        }
    }

    /**
     * 批量创建分发关系（支持更新现有关系）
     */
    @Transactional
    public BatchAssignResult batchCreateDistributions(Long configId, List<BatchAssignRequest.AssignTarget> targets, Boolean overrideExisting) {
        BatchAssignResult result = new BatchAssignResult();
        result.setTotal(targets.size());
        result.setSuccess(0);
        result.setFailed(0);
        result.setDetails(new ArrayList<>());

        for (BatchAssignRequest.AssignTarget target : targets) {
            BatchAssignResult.AssignResultDetail detail = new BatchAssignResult.AssignResultDetail();
            detail.setTargetType(target.getTargetType());
            detail.setTargetId(target.getTargetId());
            detail.setTargetName(target.getTargetName());

            try {
                boolean success = createOrUpdateDistribution(configId, target, overrideExisting);

                if (success) {
                    detail.setSuccess(true);
                    detail.setMessage("分配成功");
                    result.setSuccess(result.getSuccess() + 1);
                } else {
                    detail.setSuccess(false);
                    detail.setMessage("目标已有配置分发，跳过");
                    result.setFailed(result.getFailed() + 1);
                }

            } catch (Exception e) {
                detail.setSuccess(false);
                detail.setMessage("分配失败: " + e.getMessage());
                result.setFailed(result.getFailed() + 1);
                log.error("创建配置分发关系失败", e);
            }

            result.getDetails().add(detail);
        }

        return result;
    }

    /**
     * 创建或更新分发关系
     */
    @Transactional
    public boolean createOrUpdateDistribution(Long configId, BatchAssignRequest.AssignTarget target, Boolean overrideExisting) {
        // 查找该目标是否已有分发关系
        List<ConfigDistribution> existingDistributions = configDistributionRepository
            .findByTargetTypeAndDeleted(target.getTargetType(), false)
            .stream()
            .filter(d -> d.getTargetId().equals(target.getTargetId()))
            .collect(Collectors.toList());

        if (!existingDistributions.isEmpty()) {
            if (!overrideExisting) {
                log.info("目标 {}:{} 已有配置分发关系，跳过", target.getTargetType(), target.getTargetId());
                return false;
            }

            // 更新现有的分发关系
            ConfigDistribution existingDistribution = existingDistributions.get(0);
            existingDistribution.setConfigId(configId);
            existingDistribution.setTargetName(target.getTargetName());
            existingDistribution.setAssignTime(LocalDateTime.now());
            existingDistribution.setIsActive(true);

            boolean updated = configDistributionRepository.update(existingDistribution);
            if (updated) {
                log.info("更新分发关系成功: {} -> 配置ID: {}", target.getTargetId(), configId);
            }
            return updated;
        } else {
            // 创建新的分发关系
            ConfigDistribution distribution = new ConfigDistribution();
            distribution.setConfigId(configId);
            distribution.setTargetType(target.getTargetType());
            distribution.setTargetId(target.getTargetId());
            distribution.setTargetName(target.getTargetName());
            distribution.setIsActive(true);
            distribution.setAssignTime(LocalDateTime.now());

            boolean created = configDistributionRepository.save(distribution);
            if (created) {
                log.info("创建分发关系成功: {} -> 配置ID: {}", target.getTargetId(), configId);
            }
            return created;
        }
    }

    /**
     * 检查配置更新（基于版本比较）
     */
    public boolean hasConfigUpdate(String targetType, String targetId, String currentVersion) {
        try {
            List<ConfigDistribution> distributions = getActiveDistributionsByTarget(targetType, targetId);
            if (distributions.isEmpty()) {
                return false;
            }
            
            // 获取最高优先级的配置
            ConfigDistribution topDistribution = distributions.get(0);
            String assignedVersion = topDistribution.getAssignedVersion();
            
            // 比较版本号判断是否有更新
            return !Objects.equals(currentVersion, assignedVersion);
        } catch (Exception e) {
            log.error("检查配置更新失败", e);
            return false;
        }
    }

    /**
     * 获取配置分发统计
     */
    public List<ConfigAssignmentDto> getConfigAssignments(String targetType, String keyword) {
        List<ConfigDistribution> distributions = configDistributionRepository
            .findDistributionsWithStatus(targetType, keyword);

        return distributions.stream()
            .map(this::convertToAssignmentDto)
            .collect(Collectors.toList());
    }

    /**
     * 转换为分配DTO
     */
    private ConfigAssignmentDto convertToAssignmentDto(ConfigDistribution distribution) {
        ConfigAssignmentDto dto = new ConfigAssignmentDto();
        dto.setDistributionId(distribution.getId());
        dto.setTargetType(distribution.getTargetType());
        dto.setTargetId(distribution.getTargetId());
        dto.setTargetName(distribution.getTargetName());
        dto.setConfigId(distribution.getConfigId());
        dto.setConfigName(distribution.getConfigName());
        dto.setAssignedVersion(distribution.getAssignedVersion());
        dto.setCurrentVersion(distribution.getCurrentVersion());
        dto.setDistributionStatus(distribution.getDistributionStatus());
        dto.setAssignTime(distribution.getAssignTime());
        dto.setIsActive(distribution.getIsActive());
        return dto;
    }

    /**
     * 删除分发关系
     */
    @Transactional
    public boolean removeDistribution(Long distributionId) {
        try {
            return configDistributionRepository.deleteById(distributionId);
        } catch (Exception e) {
            log.error("删除配置分发关系失败，ID: {}", distributionId, e);
            return false;
        }
    }

    /**
     * 更新分发关系的激活状态
     */
    @Transactional
    public boolean updateActiveStatus(Long distributionId, Boolean isActive) {
        try {
            ConfigDistribution distribution = new ConfigDistribution();
            distribution.setId(distributionId);
            distribution.setIsActive(isActive);

            boolean success = configDistributionRepository.update(distribution);
            if (success) {
                log.info("更新分发关系激活状态成功，ID: {}, 状态: {}", distributionId, isActive);
            }
            return success;
        } catch (Exception e) {
            log.error("更新分发关系激活状态失败，ID: {}", distributionId, e);
            return false;
        }
    }

    /**
     * 更新分发关系的配置ID
     */
    @Transactional
    public boolean updateConfigId(Long distributionId, Long configId) {
        try {
            ConfigDistribution distribution = new ConfigDistribution();
            distribution.setId(distributionId);
            distribution.setConfigId(configId);

            boolean success = configDistributionRepository.update(distribution);
            if (success) {
                log.info("更新分发关系配置成功，ID: {}, 新配置ID: {}", distributionId, configId);
            }
            return success;
        } catch (Exception e) {
            log.error("更新分发关系配置失败，ID: {}", distributionId, e);
            return false;
        }
    }
}
