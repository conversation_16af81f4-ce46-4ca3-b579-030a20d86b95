package com.hightop.benyin.logcontrol.application.service;

import com.hightop.benyin.logcontrol.domain.entity.LogConfig;
import com.hightop.benyin.logcontrol.domain.repository.LogConfigRepository;
import com.hightop.benyin.logcontrol.dto.LogConfigDto;
import com.hightop.benyin.logcontrol.dto.BatchAssignRequest;
import com.hightop.benyin.logcontrol.dto.BatchAssignResult;
import com.hightop.benyin.logcontrol.dto.ConfigAssignmentDto;
import com.hightop.benyin.logcontrol.dto.ConfigTemplateDto;
import com.hightop.benyin.logcontrol.dto.CreateFromTemplateRequest;
import com.hightop.benyin.logcontrol.infrastructure.config.LogControlConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志配置服务
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Service
public class LogConfigService {

    @Autowired
    private LogConfigRepository logConfigRepository;

    @Autowired
    private LogControlConfig logControlConfig;

    /**
     * 获取激活的配置
     * @return 激活的配置
     */
    @Cacheable(value = "logconfig", key = "'active'")
    public LogConfigDto getActiveConfig() {
        LogConfig config = logConfigRepository.findActiveConfig();
        if (config == null) {
            // 如果没有激活配置，创建默认配置
            config = createDefaultConfig();
        }
        return convertToDto(config);
    }

    /**
     * 根据配置名称获取配置
     * @param configName 配置名称
     * @return 配置信息
     */
    public LogConfigDto getConfigByName(String configName) {
        LogConfig config = logConfigRepository.findByConfigName(configName);
        return config != null ? convertToDto(config) : null;
    }

    /**
     * 根据版本获取配置
     * @param configVersion 配置版本
     * @return 配置信息
     */
    public LogConfigDto getConfigByVersion(String configVersion) {
        LogConfig config = logConfigRepository.findByConfigVersion(configVersion);
        return config != null ? convertToDto(config) : null;
    }

    /**
     * 获取所有配置
     * @return 配置列表
     */
    public List<LogConfigDto> getAllConfigs() {
        List<LogConfig> configs = logConfigRepository.findAll();
        return configs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取配置模板列表（从数据库读取）
     * @return 配置模板列表
     */
    public List<ConfigTemplateDto> getConfigTemplates() {
        List<LogConfigDto> configs = getAllConfigs();
        return configs.stream()
                .map(this::convertToTemplateDto)
                .collect(Collectors.toList());
    }

    /**
     * 更新配置
     * @param configDto 配置DTO
     * @return 更新结果
     */
    @Transactional
    @CacheEvict(value = "logconfig", allEntries = true)
    public boolean updateConfig(LogConfigDto configDto) {
        try {
            LogConfig config = convertToEntity(configDto);
            
            if (config.getId() != null) {
                // 更新现有配置
                // 如果用户没有提供版本号或版本号为空，则自动生成
                if (StringUtils.isEmpty(config.getConfigVersion())) {
                    config.setConfigVersion(generateNewVersion());
                } else {
                    // 验证用户提供的版本号格式和唯一性
                    validateConfigVersion(config.getConfigVersion(), config.getId());
                }
                return logConfigRepository.update(config);
            } else {
                // 创建新配置
                // 如果用户没有提供版本号或版本号为空，则自动生成
                if (StringUtils.isEmpty(config.getConfigVersion())) {
                    config.setConfigVersion(generateNewVersion());
                } else {
                    // 验证用户提供的版本号格式和唯一性
                    validateConfigVersion(config.getConfigVersion(), null);
                }

                // 如果设置为激活，先停用所有配置
                if (Boolean.TRUE.equals(config.getIsActive())) {
                    logConfigRepository.deactivateAllConfigs();
                }

                return logConfigRepository.save(config);
            }
        } catch (Exception e) {
            log.error("更新日志配置失败", e);
            return false;
        }
    }

    /**
     * 激活配置
     * @param id 配置ID
     * @return 激活结果
     */
    @Transactional
    @CacheEvict(value = "logconfig", allEntries = true)
    public boolean activateConfig(Long id) {
        try {
            // 先停用所有配置
            logConfigRepository.deactivateAllConfigs();
            // 激活指定配置
            return logConfigRepository.activateConfig(id) > 0;
        } catch (Exception e) {
            log.error("激活配置失败，配置ID: {}", id, e);
            return false;
        }
    }

    /**
     * 删除配置
     * @param id 配置ID
     * @return 删除结果
     */
    @CacheEvict(value = "logconfig", allEntries = true)
    public boolean deleteConfig(Long id) {
        try {
            return logConfigRepository.deleteById(id);
        } catch (Exception e) {
            log.error("删除配置失败，配置ID: {}", id, e);
            return false;
        }
    }

    /**
     * 创建默认配置
     * @return 默认配置
     */
    private LogConfig createDefaultConfig() {
        LogConfig defaultConfig = new LogConfig();
        defaultConfig.setConfigName("default");
        defaultConfig.setLogLevel(logControlConfig.getDefaultLogLevel());
        defaultConfig.setEnableLocationLog(true);
        defaultConfig.setLocationLogInterval(logControlConfig.getDefaultLocationLogInterval());
        defaultConfig.setLogUploadInterval(logControlConfig.getDefaultLogUploadInterval());
        defaultConfig.setMaxLogFiles(logControlConfig.getDefaultMaxLogFiles());
        defaultConfig.setConfigVersion("1.0.0");
        defaultConfig.setIsActive(true);
        
        logConfigRepository.save(defaultConfig);
        return defaultConfig;
    }

    /**
     * 生成新版本号（递增格式）
     * @return 版本号
     */
    private String generateNewVersion() {
        try {
            String lastVersion = getLastVersionNumber();
            int nextVersion = parseVersionNumber(lastVersion) + 1;
            return "1.0." + nextVersion;
        } catch (Exception e) {
            log.warn("生成版本号失败，使用默认版本", e);
            return "1.0.1";
        }
    }

    /**
     * 获取最新版本号
     * @return 最新版本号
     */
    private String getLastVersionNumber() {
        try {
            List<LogConfig> configs = logConfigRepository.findAll();
            return configs.stream()
                    .map(LogConfig::getConfigVersion)
                    .filter(version -> version != null && version.matches("1\\.0\\.\\d+"))
                    .max((v1, v2) -> Integer.compare(parseVersionNumber(v1), parseVersionNumber(v2)))
                    .orElse("1.0.0");
        } catch (Exception e) {
            log.warn("获取最新版本号失败", e);
            return "1.0.0";
        }
    }

    /**
     * 解析版本号中的数字部分
     * @param version 版本号
     * @return 数字部分
     */
    private int parseVersionNumber(String version) {
        try {
            if (version == null || !version.startsWith("1.0.")) {
                return 0;
            }
            String[] parts = version.split("\\.");
            return parts.length >= 3 ? Integer.parseInt(parts[2]) : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 验证配置版本号的格式和唯一性
     * @param configVersion 版本号
     * @param excludeId 排除的配置ID（更新时使用）
     */
    private void validateConfigVersion(String configVersion, Long excludeId) {
        // 验证版本号格式（可以是任意格式，不强制要求特定格式）
        if (StringUtils.isEmpty(configVersion)) {
            throw new IllegalArgumentException("配置版本号不能为空");
        }

        // 验证版本号唯一性
        try {
            List<LogConfig> existingConfigs = logConfigRepository.findAll();
            boolean exists = existingConfigs.stream()
                    .filter(config -> excludeId == null || !config.getId().equals(excludeId))
                    .anyMatch(config -> configVersion.equals(config.getConfigVersion()));

            if (exists) {
                throw new IllegalArgumentException("配置版本号 '" + configVersion + "' 已存在，请使用其他版本号");
            }
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            log.warn("验证版本号唯一性时出错", e);
        }
    }

    /**
     * 实体转DTO
     * @param config 实体
     * @return DTO
     */
    private LogConfigDto convertToDto(LogConfig config) {
        if (config == null) {
            return null;
        }
        LogConfigDto dto = new LogConfigDto();
        BeanUtils.copyProperties(config, dto);
        return dto;
    }

    /**
     * 根据ID获取配置
     * @param id 配置ID
     * @return 配置信息
     */
    public LogConfigDto getConfigById(Long id) {
        LogConfig config = logConfigRepository.findById(id);
        return config != null ? convertToDto(config) : null;
    }

    /**
     * 根据ID获取配置（忽略激活状态，优先返回分发关系中的配置）
     * @param id 配置ID
     * @return 配置信息
     */
    public LogConfigDto getConfigByIdIgnoreStatus(Long id) {
        LogConfig config = logConfigRepository.findByIdIgnoreStatus(id);
        return config != null ? convertToDto(config) : null;
    }

    /**
     * 批量分配配置
     * @param request 批量分配请求
     * @return 分配结果
     */
    @Transactional
    public BatchAssignResult batchAssignConfig(BatchAssignRequest request) {
        BatchAssignResult result = new BatchAssignResult();
        result.setTotal(request.getTargets().size());
        result.setSuccess(0);
        result.setFailed(0);
        result.setDetails(new ArrayList<>());

        // 获取源配置
        LogConfigDto sourceConfig = getSourceConfig(request);
        if (sourceConfig == null) {
            throw new RuntimeException("源配置不存在");
        }

        for (BatchAssignRequest.AssignTarget target : request.getTargets()) {
            BatchAssignResult.AssignResultDetail detail = new BatchAssignResult.AssignResultDetail();
            detail.setTargetType(target.getTargetType());
            detail.setTargetId(target.getTargetId());
            detail.setTargetName(target.getTargetName());

            try {
                boolean success = assignConfigToTarget(sourceConfig, target, request.getOverrideExisting());
                if (success) {
                    result.setSuccess(result.getSuccess() + 1);
                    detail.setSuccess(true);
                    detail.setMessage("分配成功");
                } else {
                    result.setFailed(result.getFailed() + 1);
                    detail.setSuccess(false);
                    detail.setMessage("分配失败");
                }
            } catch (Exception e) {
                result.setFailed(result.getFailed() + 1);
                detail.setSuccess(false);
                detail.setMessage("分配失败: " + e.getMessage());
            }

            result.getDetails().add(detail);
        }

        return result;
    }

    /**
     * 从模板创建配置
     */
    public LogConfigDto createConfigFromTemplate(CreateFromTemplateRequest request) {
        ConfigTemplateDto template = getTemplateByName(request.getTemplateName());
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        LogConfigDto config = new LogConfigDto();
        config.setConfigName(request.getConfigName());
        config.setLogLevel(template.getLogLevel());
        config.setEnableLocationLog(template.getEnableLocationLog());
        config.setLocationLogInterval(template.getLocationLogInterval());
        config.setLogUploadInterval(template.getLogUploadInterval());
        config.setMaxLogFiles(template.getMaxLogFiles());
        config.setConfigVersion(generateNewVersion());
        config.setIsActive(true);

        boolean success = updateConfig(config);
        return success ? config : null;
    }

    /**
     * 获取配置分配情况
     */
    public List<ConfigAssignmentDto> getConfigAssignments(String targetType, String keyword) {
        List<LogConfig> configs = logConfigRepository.findAll();
        List<ConfigAssignmentDto> assignments = new ArrayList<>();

        for (LogConfig config : configs) {
            String configName = config.getConfigName();

            // 解析配置名称，提取目标信息
            if (configName.startsWith("user_")) {
                String userId = configName.substring(5);
                if (matchesFilter(targetType, keyword, "USER", userId)) {
                    assignments.add(createAssignmentDto("USER", userId, config));
                }
            } else if (configName.startsWith("device_")) {
                String deviceId = configName.substring(7);
                if (matchesFilter(targetType, keyword, "DEVICE", deviceId)) {
                    assignments.add(createAssignmentDto("DEVICE", deviceId, config));
                }
            } else if (configName.startsWith("group_")) {
                String groupName = configName.substring(6);
                if (matchesFilter(targetType, keyword, "GROUP", groupName)) {
                    assignments.add(createAssignmentDto("GROUP", groupName, config));
                }
            }
        }

        return assignments;
    }

    /**
     * 移除配置分配
     */
    public boolean removeConfigAssignment(String targetType, String targetId) {
        String configName = targetType.toLowerCase() + "_" + targetId;
        LogConfig config = logConfigRepository.findByConfigName(configName);

        if (config != null) {
            return logConfigRepository.deleteById(config.getId());
        }

        return false;
    }

    /**
     * 获取源配置
     */
    private LogConfigDto getSourceConfig(BatchAssignRequest request) {
        if ("TEMPLATE".equals(request.getSourceType())) {
            ConfigTemplateDto template = getTemplateByName(request.getConfigSource());
            if (template != null) {
                LogConfigDto config = new LogConfigDto();
                config.setLogLevel(template.getLogLevel());
                config.setEnableLocationLog(template.getEnableLocationLog());
                config.setLocationLogInterval(template.getLocationLogInterval());
                config.setLogUploadInterval(template.getLogUploadInterval());
                config.setMaxLogFiles(template.getMaxLogFiles());
                return config;
            }
        } else if ("CONFIG_ID".equals(request.getSourceType())) {
            try {
                Long configId = Long.parseLong(request.getConfigSource());
                return getConfigById(configId);
            } catch (NumberFormatException e) {
                log.error("配置ID格式错误: {}", request.getConfigSource());
            }
        }
        return null;
    }

    /**
     * 为目标分配配置
     */
    private boolean assignConfigToTarget(LogConfigDto sourceConfig, BatchAssignRequest.AssignTarget target, Boolean overrideExisting) {
        String configName = target.getTargetType().toLowerCase() + "_" + target.getTargetId();

        // 检查是否已存在
        if (!overrideExisting && getConfigByName(configName) != null) {
            return false;
        }

        LogConfigDto targetConfig = new LogConfigDto();
        BeanUtils.copyProperties(sourceConfig, targetConfig);
        targetConfig.setId(null);
        targetConfig.setConfigName(configName);
        targetConfig.setConfigVersion(generateNewVersion());
        targetConfig.setIsActive(true);

        return updateConfig(targetConfig);
    }

    /**
     * 根据模板名称获取模板
     */
    private ConfigTemplateDto getTemplateByName(String templateName) {
        // 从数据库中查找对应名称的配置
        LogConfig config = logConfigRepository.findByConfigName(templateName);
        if (config != null) {
            return convertToTemplateDto(convertToDto(config));
        }

        // 如果数据库中没有找到，使用默认模板
        switch (templateName) {
            case "default":
                return new ConfigTemplateDto("default", "默认配置", "INFO", true, 3000, 3600, 5, "适用于生产环境");
            case "debug":
                return new ConfigTemplateDto("debug", "调试配置", "DEBUG", true, 1000, 1800, 10, "适用于开发调试");
            case "performance":
                return new ConfigTemplateDto("performance", "性能配置", "WARN", false, 5000, 7200, 3, "适用于性能测试");
            case "minimal":
                return new ConfigTemplateDto("minimal", "最小配置", "ERROR", false, 10000, 14400, 1, "适用于资源受限环境");
            default:
                return null;
        }
    }

    /**
     * 将LogConfigDto转换为ConfigTemplateDto
     * @param configDto 配置DTO
     * @return 配置模板DTO
     */
    private ConfigTemplateDto convertToTemplateDto(LogConfigDto configDto) {
        if (configDto == null) {
            return null;
        }

        String displayName = configDto.getConfigName();
        String description = "配置版本: " + configDto.getConfigVersion();

        if (displayName.contains("_")) {
            // 如果是用户或设备特定配置，添加说明
            String[] parts = displayName.split("_", 2);
            if ("user".equalsIgnoreCase(parts[0])) {
                description = "用户专属配置 - " + description;
            } else if ("device".equalsIgnoreCase(parts[0])) {
                description = "设备专属配置 - " + description;
            }
        }

        return new ConfigTemplateDto(
            configDto.getConfigName(),
            displayName,
            configDto.getLogLevel(),
            configDto.getEnableLocationLog(),
            configDto.getLocationLogInterval(),
            configDto.getLogUploadInterval(),
            configDto.getMaxLogFiles(),
            description
        );
    }

    /**
     * 检查是否匹配过滤条件
     */
    private boolean matchesFilter(String targetType, String keyword, String actualType, String actualId) {
        if (StringUtils.hasText(targetType) && !targetType.equals(actualType)) {
            return false;
        }

        if (StringUtils.hasText(keyword) && !actualId.contains(keyword)) {
            return false;
        }

        return true;
    }

    /**
     * 创建分配DTO
     */
    private ConfigAssignmentDto createAssignmentDto(String targetType, String targetId, LogConfig config) {
        ConfigAssignmentDto dto = new ConfigAssignmentDto();
        dto.setTargetType(targetType);
        dto.setTargetId(targetId);
        dto.setTargetName(targetId); // 可以从其他地方获取更友好的名称
        dto.setConfigName(config.getConfigName());
        dto.setConfigVersion(config.getConfigVersion());
        dto.setLogLevel(config.getLogLevel());
        dto.setAssignTime(config.getCreatedAt());
        dto.setLastUsedTime(config.getUpdatedAt());
        return dto;
    }

    /**
     * DTO转实体
     * @param dto DTO
     * @return 实体
     */
    private LogConfig convertToEntity(LogConfigDto dto) {
        if (dto == null) {
            return null;
        }
        LogConfig config = new LogConfig();
        BeanUtils.copyProperties(dto, config);
        return config;
    }
}
