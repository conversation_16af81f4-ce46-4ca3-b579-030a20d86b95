package com.hightop.benyin.appupdate.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 应用版本实体
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("b_app_version")
@ApiModel("应用版本")
public class AppVersion {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    Long id;
    
    @TableField("version_name")
    @ApiModelProperty("版本名称")
    @NotBlank(message = "版本名称不能为空")
    String versionName;
    
    @TableField("version_code")
    @ApiModelProperty("版本号")
    @NotNull(message = "版本号不能为空")
    Integer versionCode;
    
    @TableField("apk_file_name")
    @ApiModelProperty("APK文件名")
    @NotBlank(message = "APK文件名不能为空")
    String apkFileName;
    
    @TableField("cos_key")
    @ApiModelProperty("COS存储key")
    @NotBlank(message = "COS存储key不能为空")
    String cosKey;
    
    @TableField("cos_url")
    @ApiModelProperty("COS访问URL")
    @NotBlank(message = "COS访问URL不能为空")
    String cosUrl;
    
    @TableField("file_size")
    @ApiModelProperty("文件大小")
    Long fileSize;
    
    @TableField("file_md5")
    @ApiModelProperty("文件MD5")
    String fileMd5;
    
    @TableField("update_log")
    @ApiModelProperty("更新说明")
    String updateLog;
    
    @TableField("is_force")
    @ApiModelProperty("是否强制更新")
    Boolean isForce;
    
    @TableField("admin_force")
    @ApiModelProperty("管理员强制标志")
    Boolean adminForce;
    
    @TableField("is_active")
    @ApiModelProperty("是否启用")
    Boolean isActive;

    @TableField("release_type")
    @ApiModelProperty("发布类型：GLOBAL-全局发布，TARGETED-定向发布")
    String releaseType;

    @TableField("download_count")
    @ApiModelProperty("下载次数")
    Integer downloadCount;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;
    
    @TableField("created_by")
    @ApiModelProperty("创建人")
    Long createdBy;
    
    @TableField("updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;
    
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("逻辑删除标志")
    Integer deleted;
}
