package com.hightop.benyin.logdata.application.service;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.hightop.benyin.appeal.infrastructure.entity.Appeal;
import com.hightop.benyin.logdata.domain.infrastructure.entity.JobLogData;
import com.hightop.benyin.logdata.domain.service.JobLogDataDomainService;
import com.hightop.magina.standard.task.log.JobLogDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@Service
public class JobLogDataService {

    JobLogDataDomainService jobLogDataDomainService;
    JobLogDomainService jobLogDomainService;

    public boolean everyDaySyncJobLogDataTask(String day) {
        Long maxId = null;
        Integer limit = 50000;
        try {
            while ( true){
                Integer  pageSize = 5000;
                Integer  pageNum = 1;
                Integer num = jobLogDomainService.selectJoinCount(
                        new MPJLambdaWrapper<>().select(JobLogData::getId).le(JobLogData::getTriggeredAt, day)
                );
                if (num == 0){
                    break;
                }
                List<JobLogData> list = jobLogDomainService.selectJoinList(
                        JobLogData.class,
                        new MPJLambdaWrapper<>()
                                .selectAll(JobLogData.class)
                                .le(Objects.nonNull(maxId), JobLogData::getId, maxId)
                                .le(JobLogData::getScheduledAt, day)
                                .last(true,"ORDER BY t.id DESC limit "+limit)
                );
                maxId = list.get(list.size() - 1).getId();
                if(list.size() < limit){
                    limit = list.size();
                }
                Integer totalPage = limit % pageSize == 0 ? limit / pageSize : limit / pageSize + 1;
                while ( pageNum <= totalPage){
                    List<JobLogData> saveList = list.stream()
                            .skip((pageNum - 1) * pageSize)
                            .limit(pageSize)
                            .collect(Collectors.toList());
                    jobLogDataDomainService.saveBatch(saveList);
                    List<Long> ids = saveList.stream().map(JobLogData::getId).collect(Collectors.toList());
                    jobLogDomainService.removeByIds(ids);
                    pageNum ++;
                }
                log.info("maxId:{}", maxId);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("同步前15天的定时任务数据到数据表任务失败------,{0}", e);
            return false;
        }
    }

}
