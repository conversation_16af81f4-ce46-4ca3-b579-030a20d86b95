package com.hightop.benyin.storage.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓储管理-库存盘点明细
 *
 * <AUTHOR>
 * @date 2024/07/29 10:29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_take_detail")
@ApiModel
public class TakeDetail {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id ")
    Long id;

    @TableField("take_stock_id")
    @ApiModelProperty("盘点单号")
    Long takeStockId;

    @TableField(exist = false)
    @ApiModelProperty("盘点单号")
    @Excel(name = "盘点单号", width = 25, orderNum = "0")
    String takeStockCode;

    @TableField("article_code")
    @ApiModelProperty("物品编码")
    @Excel(name = "机器编码", width = 25, orderNum = "1")
    String articleCode;

    @TableField("article_name")
    @ApiModelProperty("物品名称")
    @Excel(name = "机器名称", width = 25, orderNum = "2")
    String articleName;

    @TableField("product_id")
    @ApiModelProperty("机器id")
    Long productId;

    @TableField("full_id_path")
    @ApiModelProperty("全路径 ")
    String fullIdPath;


    @TableField("number_oem")
    @ApiModelProperty("原厂零件编号（OEM）")
    String numberOem;

    @TableField("manufacturer_channel")
    @ApiModelProperty("制造商渠道(字典项码)")
    @DictItemBind(StorageArticle.CHANNEL)
    DictItemEntry manufacturerChannel;

    @TableField("host_type")
    @ApiModelProperty("主机类型")
    @DictItemBind(Machine.HOST_TYPE)
    @Excel(name = "主机类型", width = 25, orderNum = "3",enumExportField = "label")
    DictItemEntry hostType;

    @TableField("device_on")
    @ApiModelProperty("设备新旧(字典项码)")
    @DictItemBind(Machine.DEVICE_ON)
    @Excel(name = "设备新旧", width = 25, orderNum = "4",enumExportField = "label")
    DictItemEntry deviceOn;

    @TableField("device_status")
    @ApiModelProperty("设备状态 ")
    @DictItemBind(Machine.DEVICE_STATUS)
    @Excel(name = "设备状态", width = 30, orderNum = "5", enumExportField = "label" )
    DictItemEntry deviceStatus;


    @TableField("machine_status")
    @ApiModelProperty("库存状态")
    @Excel(name = "库存状态", width = 25, orderNum = "5",enumExportField = "name")
    MachineStatus machineStatus;

    @TableField(value = "origin_code")
    @ApiModelProperty("原机器编号")
    @Excel(name = "原机器编号", width = 25, orderNum = "6")
    String originCode;



    @TableField("tag_name")
    @ApiModelProperty("标签型号")
    @Excel(name = "标签型号", width = 25, orderNum = "7")
    String tagName;


    @Excel(name = "供应商编码", width = 30, orderNum = "7")
    @ApiModelProperty("供应商编码")
    @TableField(exist = false)
    String manufacturerCode;

    @TableField(exist = false)
    @Excel(name = "供应商名称", width = 30, orderNum = "7")
    @ApiModelProperty("供应商名称")
    String manufacturerName;

    @TableField("location")
    @ApiModelProperty("储位")
    @Excel(name = "储位", width = 25, orderNum = "8")
    String location;


    @TableField("inventory_amount")
    @JsonAmount
    @ApiModelProperty("库存金额")
    Long inventoryAmount;

    @TableField("inventory_num")
    @ApiModelProperty("库存数量")
    Integer inventoryNum;

    @TableField("price")
    @JsonAmount
    @ApiModelProperty("参考价格")
    Long price;

    @TableField(exist = false)
    @Excel(name = "参考价格", width = 30, orderNum = "10")
    BigDecimal priceDecimal;

    @TableField("stock_amount")
    @JsonAmount
    @ApiModelProperty("盘点金额")
    Long stockAmount;

    @TableField("stock_num")
    @ApiModelProperty("盘点数量")
    @Excel(name = "是否在库", width = 30, orderNum = "9", replace = {"是_1", "否_0"})
    Integer stockNum;

    @TableField( value = "batch_info",typeHandler = FastjsonTypeHandler.class)
    @ApiModelProperty("批次信息")
    JSONArray batchInfo;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "盘点时间", width = 30, orderNum = "10", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("品牌/产品树/系列/机型")
    String productInfo;

    @TableField(exist=false)
    @ApiModelProperty("上传图片")
    CosObjectList imageFiles;

    @TableField(exist=false)
    @ApiModelProperty("制造商物品编号")
    String manufacturerGoodsCode;

    @TableField(exist = false)
    @ApiModelProperty("制造商物品名称")
    String manufacturerGoodsName;

    @TableField(exist = false)
    @JsonAmount
    @ApiModelProperty("入库价格")
    Long inPrice;

    @TableField("batch_code")
    @ApiModelProperty("入库价格")
    String batchCode;

    @TableField(exist = false)
    @ApiModelProperty("剩余库存数量")
    Integer num;

    public BigDecimal getPriceDecimal() {
        return price == null? BigDecimal.ZERO : new BigDecimal(price).divide(DownloadResponseUtil.HUNDRED, 2, BigDecimal.ROUND_HALF_UP);
    }
}
