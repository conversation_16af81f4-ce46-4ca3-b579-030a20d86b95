package com.hightop.benyin.storage.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import com.hightop.benyin.storage.infrastructure.enums.OutStatusEnum;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 仓储管理-出库管理-商品信息实体
 *
 * <AUTHOR>
 * @date 2023-11-16 11:55:33
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_storage_out_warehouse_goods")
@ApiModel
public class StorageOutWarehouseGoods {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("id")
    Long id;

    @TableField("out_warehouse_id")
    @ApiModelProperty("出库单编号[[CKID（出库）+年份后两位+月份+日期+六位序列数字（不重复）)]]")
    String outWarehouseId;

    @TableField("warehouse_id")
    @ApiModelProperty("仓库id")
    Long warehouseId;


    @TableField("out_warehouse_number")
    @ApiModelProperty("应出库(出库单总量)")
    Integer outWarehouseNumber;

    @TableField("auidt_out_warehouse_number")
    @ApiModelProperty("出库数量（审核出库数量）")
    @Min(value = 1, message = "出库数量最小为1")
    Integer auidtOutWarehouseNumber;

    @TableField("cancel_out_warehouse_number")
    @ApiModelProperty("取消出库数量（申请售后数量）")
    Integer cancelOutWarehouseNumber;

    @TableField("inventory_id")
    @ApiModelProperty("库品id(库存SKU id)")
    Long inventoryId;


    @TableField("article_code")
    @ApiModelProperty("物品编码")
    String articleCode;

    @TableField("article_type")
    @ApiModelProperty("物品类型1组合0单品")
    Integer articleType;

    @TableField("operator_id")
    @ApiModelProperty("操作人ID")
    Long operatorId;

    @TableField("batch_code")
    @ApiModelProperty("报损出库批次号")
    //@NotBlank(message = "批次号不能为空")
    String batchCode;

    @TableField(exist = false)
    @ApiModelProperty("批次id")
    @NotNull(message = "批次id不能为空")
    Long batchId;

    @ApiModelProperty("储位")
    @TableField(exist = false)
    String location;

    @TableField(exist = false)
    @ApiModelProperty("制造商物品编号")
    String manufacturerGoodsCode;

    @TableField(exist = false)
    @ApiModelProperty("制造商物品名称")
    String manufacturerGoodsName;

    @TableField(exist = false)
    @ApiModelProperty("库品名称")
    String inventoryName;

    @TableField(exist = false)
    @ApiModelProperty("库品code")
    String inventoryCode;

    @TableField(exist = false)
    @ApiModelProperty("物品")
    StorageArticle storageArticle;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("out_status")
    @ApiModelProperty("出库状态")
    OutStatusEnum outStatus;

    @TableField("remarks")
    @ApiModelProperty("备注信息")
    String remarks;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    /**
     * 商品快照
     */
    @TableField(exist = false)
    @ApiModelProperty("上传图片")
    CosObjectList imageFiles;

    @TableField(exist = false)
    @ApiModelProperty("原厂零件编号（OEM）")
    String numberOem;

    @TableField(exist = false)
    @ApiModelProperty("制造商渠道(字典项码)")
    @DictItemBind(StorageArticle.CHANNEL)
    DictItemEntry manufacturerChannel;

}
