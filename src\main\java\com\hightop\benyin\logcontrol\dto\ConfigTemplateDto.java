package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配置模板DTO
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@ApiModel("配置模板")
public class ConfigTemplateDto {
    
    @ApiModelProperty("模板名称")
    private String templateName;
    
    @ApiModelProperty("显示名称")
    private String displayName;
    
    @ApiModelProperty("日志级别")
    private String logLevel;
    
    @ApiModelProperty("是否启用位置日志")
    private Boolean enableLocationLog;
    
    @ApiModelProperty("位置日志间隔(秒)")
    private Integer locationLogInterval;

    @ApiModelProperty("上传间隔(秒)")
    private Integer logUploadInterval;
    
    @ApiModelProperty("最大日志文件数")
    private Integer maxLogFiles;
    
    @ApiModelProperty("描述")
    private String description;
    
    public ConfigTemplateDto() {}
    
    public ConfigTemplateDto(String templateName, String displayName, String logLevel, 
                           Boolean enableLocationLog, Integer locationLogInterval, 
                           Integer logUploadInterval, Integer maxLogFiles, String description) {
        this.templateName = templateName;
        this.displayName = displayName;
        this.logLevel = logLevel;
        this.enableLocationLog = enableLocationLog;
        this.locationLogInterval = locationLogInterval;
        this.logUploadInterval = logUploadInterval;
        this.maxLogFiles = maxLogFiles;
        this.description = description;
    }
}
