package com.hightop.benyin.appupdate.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 更新检查响应DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel("更新检查响应")
public class UpdateCheckResponse {
    
    @ApiModelProperty("是否有更新")
    Boolean hasUpdate;
    
    @ApiModelProperty("版本名称")
    String versionName;
    
    @ApiModelProperty("版本号")
    Integer versionCode;
    
    @ApiModelProperty("下载URL")
    String downloadUrl;
    
    @ApiModelProperty("更新说明")
    String updateLog;
    
    @ApiModelProperty("是否强制更新")
    Boolean isForce;
    
    @ApiModelProperty("文件大小")
    Long fileSize;
    
    @ApiModelProperty("文件MD5")
    String fileMd5;
}
