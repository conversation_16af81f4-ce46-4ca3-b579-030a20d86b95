package com.hightop.benyin.logcontrol.domain.repository;

import com.hightop.benyin.logcontrol.domain.entity.LogConfig;

import java.util.List;

/**
 * 日志配置Repository接口
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface LogConfigRepository {

    /**
     * 保存日志配置
     * @param logConfig 日志配置
     * @return 保存结果
     */
    boolean save(LogConfig logConfig);

    /**
     * 更新日志配置
     * @param logConfig 日志配置
     * @return 更新结果
     */
    boolean update(LogConfig logConfig);

    /**
     * 根据ID查找日志配置
     * @param id 配置ID
     * @return 日志配置
     */
    LogConfig findById(Long id);

    /**
     * 根据ID查找日志配置（忽略激活状态）
     * @param id 配置ID
     * @return 日志配置
     */
    LogConfig findByIdIgnoreStatus(Long id);

    /**
     * 查找激活的配置
     * @return 激活的配置
     */
    LogConfig findActiveConfig();

    /**
     * 根据配置名称查找配置
     * @param configName 配置名称
     * @return 配置信息
     */
    LogConfig findByConfigName(String configName);

    /**
     * 根据版本查找配置
     * @param configVersion 配置版本
     * @return 配置信息
     */
    LogConfig findByConfigVersion(String configVersion);

    /**
     * 查找所有配置
     * @return 配置列表
     */
    List<LogConfig> findAll();

    /**
     * 停用所有配置
     * @return 更新数量
     */
    int deactivateAllConfigs();

    /**
     * 激活指定配置
     * @param id 配置ID
     * @return 更新数量
     */
    int activateConfig(Long id);

    /**
     * 根据ID删除配置
     * @param id 配置ID
     * @return 删除结果
     */
    boolean deleteById(Long id);
}
