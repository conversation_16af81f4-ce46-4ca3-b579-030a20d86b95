package com.hightop.benyin.appupdate.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import com.hightop.benyin.appupdate.infrastructure.mapper.AppVersionMapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应用版本领域服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionDomainService extends MPJBaseServiceImpl<AppVersionMapper, AppVersion> {
    
    /**
     * 获取最新的活跃版本
     * @return 最新版本
     */
    public AppVersion getLatestActiveVersion() {
        return this.lambdaQuery()
                .eq(AppVersion::getIsActive, true)
                .orderByDesc(AppVersion::getVersionCode)
                .last("LIMIT 1")
                .one();
    }

    /**
     * 获取最新的活跃全局版本（新增方法）
     * @return 最新全局版本
     */
    public AppVersion getLatestActiveGlobalVersion() {
        return this.lambdaQuery()
                .eq(AppVersion::getIsActive, true)
                .eq(AppVersion::getReleaseType, "GLOBAL")
                .orderByDesc(AppVersion::getVersionCode)
                .last("LIMIT 1")
                .one();
    }
    
    /**
     * 根据版本名称获取版本
     * @param versionName 版本名称
     * @return 版本信息
     */
    public AppVersion getByVersionName(String versionName) {
        return this.lambdaQuery()
                .eq(AppVersion::getVersionName, versionName)
                .one();
    }
    
    /**
     * 根据版本号获取版本
     * @param versionCode 版本号
     * @return 版本信息
     */
    public AppVersion getByVersionCode(Integer versionCode) {
        return this.lambdaQuery()
                .eq(AppVersion::getVersionCode, versionCode)
                .one();
    }
    
    /**
     * 获取所有活跃版本
     * @return 活跃版本列表
     */
    public List<AppVersion> getActiveVersions() {
        return this.lambdaQuery()
                .eq(AppVersion::getIsActive, true)
                .orderByDesc(AppVersion::getVersionCode)
                .list();
    }
    
    /**
     * 增加下载次数
     * @param versionId 版本ID
     * @return 是否成功
     */
    public boolean incrementDownloadCount(Long versionId) {
        return this.baseMapper.incrementDownloadCount(versionId) > 0;
    }
    
    /**
     * 检查版本名称是否存在
     * @param versionName 版本名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    public boolean existsByVersionName(String versionName, Long excludeId) {
        LambdaQueryWrapper<AppVersion> wrapper = new LambdaQueryWrapper<AppVersion>()
                .eq(AppVersion::getVersionName, versionName);
        
        if (excludeId != null) {
            wrapper.ne(AppVersion::getId, excludeId);
        }
        
        return this.count(wrapper) > 0;
    }
    
    /**
     * 检查版本号是否存在
     * @param versionCode 版本号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    public boolean existsByVersionCode(Integer versionCode, Long excludeId) {
        LambdaQueryWrapper<AppVersion> wrapper = new LambdaQueryWrapper<AppVersion>()
                .eq(AppVersion::getVersionCode, versionCode);
        
        if (excludeId != null) {
            wrapper.ne(AppVersion::getId, excludeId);
        }
        
        return this.count(wrapper) > 0;
    }
    
    /**
     * 设置强制更新标志
     * @param versionId 版本ID
     * @param force 是否强制
     * @return 是否成功
     */
    public boolean setForceUpdate(Long versionId, Boolean force) {
        return this.lambdaUpdate()
                .set(AppVersion::getAdminForce, force)
                .eq(AppVersion::getId, versionId)
                .update();
    }
    
    /**
     * 暂停所有更新
     * @return 是否成功
     */
    public boolean pauseAllUpdates() {
        return this.lambdaUpdate()
                .set(AppVersion::getIsActive, false)
                .update();
    }
    
    /**
     * 恢复所有更新
     * @return 是否成功
     */
    public boolean resumeAllUpdates() {
        return this.lambdaUpdate()
                .set(AppVersion::getIsActive, true)
                .set(AppVersion::getAdminForce, false)
                .update();
    }
}
