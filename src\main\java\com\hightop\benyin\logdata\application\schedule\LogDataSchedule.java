package com.hightop.benyin.logdata.application.schedule;

import com.hightop.benyin.logdata.application.service.JobLogDataService;
import com.hightop.magina.standard.task.job.ScheduleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2024/05/19 19:42
 */
@Slf4j
@Component
public class LogDataSchedule {

    JobLogDataService jobLogDataService;
    /**
     * 统计当天已完成的任务(解决跨月)
     *
     * @throws Exception
     */
    //@ScheduleJob(id = 20018L, cron = "0 4 0 ? * * *", description = "同步前15天的定时任务数据到数据表任务")
    public void everyDaySyncJobLogDataTask() throws Exception {
        log.info("同步前15天的定时任务数据到数据表任务开始------");
        LocalDateTime now = LocalDateTime.now().plusDays(-15);
        String day = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if ( jobLogDataService.everyDaySyncJobLogDataTask(day) ) {
            log.info("同步前15天的定时任务数据到数据表任务成功------");
        }else{
            log.info("同步前15天的定时任务数据到数据表任务失败------");
        }
    }
}