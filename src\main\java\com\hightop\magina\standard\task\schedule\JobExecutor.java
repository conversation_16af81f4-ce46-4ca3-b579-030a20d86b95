package com.hightop.magina.standard.task.schedule;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hightop.fario.base.util.FarioThreadFactory;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.task.job.Job;
import com.hightop.magina.standard.task.log.JobLog;
import com.hightop.magina.standard.task.log.JobState;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class JobExecutor {
    private static final Logger log = LoggerFactory.getLogger(JobExecutor.class);
    private final ScheduleJobDomainService scheduleJobDomainService;
    private final ScheduleJobLogDomainService scheduleJobLogDomainService;
    private final ThreadPoolExecutor executor;
    private final ThreadPoolExecutor invoker;
    private static final int HALF_CORES = (Integer) Optional.of(Runtime.getRuntime().availableProcessors()).map((it) -> {
        return it / 2;
    }).filter((it) -> {
        return it > 0;
    }).orElse(1);
    private static final Map<String, MethodInvoker> METHOD_INVOKERS = new ConcurrentHashMap();

    void execute(JobLog jobLog) {
        ExecutorUtils.run(this.executor, () -> {
            this.doExecute(jobLog, false);
        });
    }

    void stop() {
        if (log.isInfoEnabled()) {
            log.info("正在关闭任务执行器...");
        }

        this.executor.shutdown();
        this.invoker.shutdown();
    }

    private void doExecute(JobLog jobLog, boolean isRetry) {
        if (!isRetry) {
            LambdaUpdateWrapper<JobLog> updateWrapper = Wrappers.<JobLog>lambdaUpdate();
            updateWrapper.eq(JobLog::getState, JobState.PENDING)
                        .eq(JobLog::getJobId, jobLog.getJobId());
            boolean update = this.scheduleJobLogDomainService.update(jobLog.setState(JobState.RUNNING).setStartedAt(LocalDateTime.now()), updateWrapper);
            if (!update) {
                return;
            }
        }

        FutureTask<JobLog> futureTask = new FutureTask(() -> {
            return this.doInvoke(jobLog);
        });
        Throwable throwable = null;
        boolean isTimeout = false;
        boolean var13 = false;

        LocalDateTime now;
        boolean needRetry;
        label227: {
            try {
                var13 = true;
                ExecutorUtils.run(this.invoker, futureTask);
                if (jobLog.getTimeout() <= 0) {
                    futureTask.get();
                    var13 = false;
                } else {
                    futureTask.get((long)jobLog.getTimeout(), TimeUnit.SECONDS);
                    var13 = false;
                }
                break label227;
            } catch (ExecutionException | TimeoutException | InterruptedException var14) {
                Exception e = var14;
                if (e instanceof TimeoutException) {
                    isTimeout = true;
                    futureTask.cancel(true);
                }

                throwable = e;
                var13 = false;
            } finally {
                if (var13) {
                    needRetry = false;
                    now = LocalDateTime.now();
                    if (Objects.isNull(throwable)) {
                        jobLog.setState(JobState.SUCCESS).setFinishedAt(now).calcElapse();
                    } else {
                        needRetry = jobLog.tryRetry();
                        if (!needRetry) {
                            jobLog.setState(isTimeout ? JobState.TIMEOUT : JobState.FAIL).setMessage(isTimeout ? TimeoutException.class.getSimpleName() : ((Throwable)throwable).getMessage()).setFinishedAt(now).calcElapse();
                        }
                    }

                    this.scheduleJobLogDomainService.updateById(jobLog);
                    if (needRetry) {
                        this.doExecute(jobLog, true);
                    }

                }
            }

            needRetry = false;
            now = LocalDateTime.now();
            if (Objects.isNull(throwable)) {
                jobLog.setState(JobState.SUCCESS).setFinishedAt(now).calcElapse();
            } else {
                needRetry = jobLog.tryRetry();
                if (!needRetry) {
                    jobLog.setState(isTimeout ? JobState.TIMEOUT : JobState.FAIL).setMessage(isTimeout ? TimeoutException.class.getSimpleName() : ((Throwable)throwable).getMessage()).setFinishedAt(now).calcElapse();
                }
            }

            this.scheduleJobLogDomainService.updateById(jobLog);
            if (needRetry) {
                this.doExecute(jobLog, true);
            }
            return;
        }

        needRetry = false;
        now = LocalDateTime.now();
        if (Objects.isNull(throwable)) {
            jobLog.setState(JobState.SUCCESS).setFinishedAt(now).calcElapse();
        } else {
            needRetry = jobLog.tryRetry();
            if (!needRetry) {
                jobLog.setState(isTimeout ? JobState.TIMEOUT : JobState.FAIL).setMessage(isTimeout ? TimeoutException.class.getSimpleName() : ((Throwable)throwable).getMessage()).setFinishedAt(now).calcElapse();
            }
        }

        this.scheduleJobLogDomainService.updateById(jobLog);
        if (needRetry) {
            this.doExecute(jobLog, true);
        }

    }

    private JobLog doInvoke(JobLog jobLog) {
        ((MethodInvoker)METHOD_INVOKERS.computeIfAbsent(String.format("%d-%d", jobLog.getJobId(), jobLog.getJobVersion().atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli()), (it) -> {
            try {
                Job job = (Job)this.scheduleJobDomainService.getById(jobLog.getJobId());
                Class<?> clazz = Class.forName(job.getBean());
                Object bean = ApplicationContexts.getBean(clazz);
                Method method = clazz.getMethod(job.getMethod());
                if (!method.isAccessible()) {
                    method.setAccessible(true);
                }

                return new MethodInvoker(bean, method);
            } catch (NoSuchMethodException | ClassNotFoundException var7) {
                ReflectiveOperationException e = var7;
                throw new MaginaException(((ReflectiveOperationException)e).getMessage(), e);
            }
        })).doInvoke();
        return jobLog;
    }

    public JobExecutor(ScheduleJobDomainService scheduleJobDomainService, ScheduleJobLogDomainService scheduleJobLogDomainService) {
        this.executor = new ThreadPoolExecutor(HALF_CORES, 200, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue(500), FarioThreadFactory.of("Job-Executor", false, new AtomicInteger()));
        this.invoker = new ThreadPoolExecutor(HALF_CORES, 200, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue(500), FarioThreadFactory.of("Job-Invoker", false, new AtomicInteger()));
        this.scheduleJobDomainService = scheduleJobDomainService;
        this.scheduleJobLogDomainService = scheduleJobLogDomainService;
    }
}

