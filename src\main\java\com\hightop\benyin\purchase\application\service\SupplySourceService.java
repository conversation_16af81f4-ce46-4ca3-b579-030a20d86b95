package com.hightop.benyin.purchase.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hightop.benyin.product.domain.dto.ProductTreeDto;
import com.hightop.benyin.product.domain.service.PartProductTreeDomainService;
import com.hightop.benyin.purchase.api.dto.query.SupplySourceQuery;
import com.hightop.benyin.purchase.domain.service.InquiryDomainService;
import com.hightop.benyin.purchase.domain.service.SupplySourceServiceDomain;
import com.hightop.benyin.purchase.infrastructure.entity.Inquiry;
import com.hightop.benyin.purchase.infrastructure.entity.SupplySource;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.domain.service.StorageArticleServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应源服务
 *
 * <AUTHOR>
 * @date 2023-12-06 14:48:56
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class SupplySourceService {

    SupplySourceServiceDomain supplySourceServiceDomain;

    /**
     * 物品管理
     */
    StorageArticleServiceDomain storageArticleServiceDomain;

    /**
     * 制造商服务
     */
    ManufacturerServiceDomain manufacturerServiceDomain;

    /**
     * 零件、机型关系服务
     */
    PartProductTreeDomainService partProductTreeDomainService;

    /**
     * 询价单服务
     */
    InquiryDomainService inquiryDomainService;

    /**
     * 供应源分页查询
     *
     * @param qo {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<SupplySource> page(SupplySourceQuery qo) {
        return PageHelper.startPage(qo, p -> supplySourceServiceDomain.getBaseMapper().pageList(qo))
                .peek(p -> {
                            //物品信息
                            StorageArticle article = storageArticleServiceDomain.getById(p.getArticleId());
                            p.setStorageArticle(article);
                            //供应商(制造商)
                            p.setManufacturer(manufacturerServiceDomain.getById(p.getManufacturerId()));
                            //适用机型
                            List<ProductTreeDto> productTreeDtos = partProductTreeDomainService.getByPartId(article.getPartId());
                            p.setProductTreeDtoList(productTreeDtos);
                            //当前价格是否是最低价格
                            Inquiry inquiry = inquiryDomainService.lambdaQuery().select(Inquiry::getPrice).eq(Inquiry::getArticleId, p.getArticleId())
                                    .ge(Inquiry::getExpiresTime, LocalDateTime.now())
                                    .orderByAsc(Inquiry::getPrice)
                                    .last(" LIMIT 1").one();
                            // 查到了询价记录，当前价格大于询价最低价则认为非最低价
                            if (Objects.nonNull(inquiry) && inquiry.getPrice() < p.getPrice()) {
                                p.setMiniPriceFlag(Boolean.FALSE);
                            } else {
                                // 否则认定为最低价
                                p.setMiniPriceFlag(Boolean.TRUE);
                            }
                        }
                );
    }

    /**
     * 供应源添加
     *
     * @param supplySourceList {@link SupplySource}
     * @return true/false
     * <p>
     * [
     * {
     * "applicantId": 3,
     * "applicantName": "申请人1",
     * "articleId": 2,
     * "manufacturerId": 3,
     * "price": 20,
     * "arrivalTime": "aaaaa",
     * "contractFiles": "/",
     * "deliveryTime": "bbbbb",
     * "inquiryOrderId": 1,
     * "settlementNumberFive": 11,
     * "settlementNumberFour": 22,
     * "settlementNumberOne": 33,
     * "settlementNumberThree": 44,
     * "settlementNumberTwo": 55,
     * "settlementPriceFive": 66,
     * "settlementPriceFour": 77,
     * "settlementPriceOne": 88,
     * "settlementPriceThree": 99,
     * "settlementPriceTwo": 1010,
     * "validityEndTime": "2203-12-06 15:31:30",
     * "validityStartTime": "2203-12-22 23:59:59"
     * },
     * {
     * "applicantId": 2,
     * "applicantName": "申请人2",
     * "articleId": 4,
     * "manufacturerId": 4,
     * "price": 15,
     * "arrivalTime": "aaaaa2",
     * "contractFiles": "/",
     * "deliveryTime": "bbbbb2",
     * "inquiryOrderId": 1,
     * "settlementNumberFive": 111,
     * "settlementNumberFour": 222,
     * "settlementNumberOne": 333,
     * "settlementNumberThree": 444,
     * "settlementNumberTwo": 555,
     * "settlementPriceFive": 666,
     * "settlementPriceFour": 777,
     * "settlementPriceOne": 888,
     * "settlementPriceThree": 999,
     * "settlementPriceTwo": 101010,
     * "validityEndTime": "2203-12-06 12:31:30",
     * "validityStartTime": "2203-12-12 23:59:59"
     * }
     * ]
     */
    public boolean save(List<SupplySource> supplySourceList) {
        for (SupplySource supplySource : supplySourceList) {
            // 校验：同一个物品，同一个供应商，只能启用一条记录
            if (supplySource.getStatus() != null && supplySource.getStatus() == 1) {
                List<SupplySource> existingList = supplySourceServiceDomain.list(new LambdaQueryWrapper<SupplySource>()
                        .eq(SupplySource::getArticleId, supplySource.getArticleId())
                        .eq(SupplySource::getManufacturerId, supplySource.getManufacturerId())
                        .eq(SupplySource::getStatus, 1)
                );
                if (CollectionUtils.isNotEmpty(existingList)) {
                    //throw new MaginaException("同一个物品，同一个供应商，只能启用一条记录");
                    existingList.forEach(existing -> {
                        existing.setStatus(0);
                        this.supplySourceServiceDomain.updateById(existing);
                    });
                }
            }
            
            supplySource.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            supplySource.setUpdatedBy(ApplicationSessions.id());
            supplySource.setUpdatedAt(LocalDateTime.now());
            this.supplySourceServiceDomain.save(supplySource);
        }
        return true;
    }

    /**
     * 供应源修改
     *
     * @param supplySource {@link SupplySource}
     * @return true/false
     */
    public boolean updateById(SupplySource supplySource) {
        supplySource.setUpdatedBy(ApplicationSessions.id());
        return this.supplySourceServiceDomain.updateById(supplySource);
    }

    /**
     * 供应源删除
     *
     * @param id id
     * @return true/false
     */
    public boolean removeById(Long id) {
        return this.supplySourceServiceDomain.removeById(id);
    }

    /**
     * 修改状态
     *
     * @param supplySourceParam
     * @return
     */
    public boolean updateStatus(SupplySource supplySourceParam) {
        SupplySource supplySource = supplySourceServiceDomain.getById(supplySourceParam.getId());
        if (supplySource == null) {
            throw new MaginaException("不存在记录", supplySourceParam.getId());
        }
        //一个物品，一个供应商，只能启用一条
        if (supplySourceParam.getStatus() == 1) {
            List<SupplySource> supplySourceList = supplySourceServiceDomain.list(new LambdaQueryWrapper<SupplySource>()
                    .eq(SupplySource::getArticleId, supplySource.getArticleId())
                    .eq(SupplySource::getManufacturerId, supplySource.getManufacturerId())
                    .eq(SupplySource::getStatus, 1)
            );
            if (CollectionUtils.isNotEmpty(supplySourceList)) {
                throw new MaginaException("一个物品，一个供应商，只能启用一条记录");
            }
        }
        supplySource.setStatus(supplySourceParam.getStatus());
        return this.supplySourceServiceDomain.updateById(supplySource);
    }

    /**
     * 根据物品编号，查询供应源
     *
     * @param ids
     * @return
     */
    public Map<Long,List<SupplySource>> queryByArticleIds(List<Long> ids) {
        Map<Long, List<SupplySource>> result = new HashMap<>(16);
        Map<Long, List<SupplySource>> supplySourceMap = new HashMap<>(16);
        List<SupplySource> supplySourceList = supplySourceServiceDomain.list(new LambdaQueryWrapper<SupplySource>()
                .in(SupplySource::getArticleId, ids).eq(SupplySource::getStatus, Boolean.TRUE)
        );
        if (CollectionUtils.isEmpty(supplySourceList)) {
            supplySourceList = new ArrayList<>();
        }else {
            supplySourceList.forEach(p -> {
                StorageArticle article = storageArticleServiceDomain.getById(p.getArticleId());
                p.setStorageArticle(article);
                p.setManufacturer(manufacturerServiceDomain.getById(p.getManufacturerId()));
                //适用机型
                List<ProductTreeDto> productTreeDtos = partProductTreeDomainService.getByPartId(article.getPartId());
                p.setProductTreeDtoList(productTreeDtos);
            });
            supplySourceMap = supplySourceList.stream().collect(Collectors.groupingBy(SupplySource::getArticleId));
        }
        for (Long articleId : ids) {
            if (supplySourceMap.get(articleId) == null) {
                result.put(articleId, new ArrayList<>());
            }else {
                result.put(articleId, supplySourceMap.get(articleId));
            }
        }
        return result;
    }
}
