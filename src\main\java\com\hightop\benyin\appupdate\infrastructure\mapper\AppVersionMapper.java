package com.hightop.benyin.appupdate.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 应用版本Mapper
 * <AUTHOR>
 * @date 2025-01-29
 */
@Mapper
public interface AppVersionMapper extends MPJBaseMapper<AppVersion> {
    
    /**
     * 增加下载次数
     * @param id 版本ID
     * @return 影响行数
     */
    @Update("UPDATE b_app_version SET download_count = download_count + 1 WHERE id = #{id}")
    int incrementDownloadCount(@Param("id") Long id);
}
