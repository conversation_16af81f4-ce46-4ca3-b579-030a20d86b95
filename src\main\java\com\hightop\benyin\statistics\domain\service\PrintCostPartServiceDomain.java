package com.hightop.benyin.statistics.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.statistics.infrastructure.entity.PrintCostPart;
import com.hightop.benyin.statistics.infrastructure.mapper.PrintCostPartMapper;
import com.hightop.magina.standard.task.log.JobLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 零件打印成本统计
 *
 * <AUTHOR>
 * @date 2024-05-05 16:29:08
 */
@Service
public class PrintCostPartServiceDomain extends MPJBaseServiceImpl<PrintCostPartMapper, PrintCostPart> {

    /**
     * 查询机器基础耗材成本数据
     * @param productId
     * @return
     */
    public List<PrintCostPart> getBasePrintCostPart(Long productId) {
        return this.baseMapper.getBasePrintCostPart(productId);
    }

    public List<JobLog> getAllotedLogs(LocalDateTime now) {
        return this.baseMapper.getAllotedLogs(now);
    }
}
