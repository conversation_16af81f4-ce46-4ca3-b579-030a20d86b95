package com.hightop.benyin.work.order.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.hightop.benyin.appeal.domain.service.AppealDomainService;
import com.hightop.benyin.appeal.infrastructure.entity.Appeal;
import com.hightop.benyin.customer.domain.service.CustomerBusinessDomainService;
import com.hightop.benyin.customer.domain.service.CustomerDeviceGroupDomainService;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.domain.service.CustomerStaffDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerBusiness;
import com.hightop.benyin.customer.infrastructure.entity.CustomerDeviceGroup;
import com.hightop.benyin.customer.infrastructure.entity.CustomerStaff;
import com.hightop.benyin.customer.infrastructure.enums.MembershipLevel;
import com.hightop.benyin.customer.infrastructure.enums.SerTypeEnums;
import com.hightop.benyin.customer.infrastructure.util.CustomerMpUtils;
import com.hightop.benyin.iot.domain.service.IotCounterServiceDomain;
import com.hightop.benyin.iot.infrastructure.entity.IotCounter;
import com.hightop.benyin.item.infrastructure.entity.SaleSku;
import com.hightop.benyin.items.store.domain.service.ItemStoreServiceDomain;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.order.infrastructure.enmu.PayModeEnum;
import com.hightop.benyin.payment.domain.event.PayVoucherCloseEvent;
import com.hightop.benyin.payment.domain.service.PayOrderDomainService;
import com.hightop.benyin.payment.domain.service.TransactionDomainService;
import com.hightop.benyin.payment.domain.vo.PayOrderVo;
import com.hightop.benyin.payment.infrastructure.entity.PayOrder;
import com.hightop.benyin.payment.infrastructure.enums.TradeOrderOrigin;
import com.hightop.benyin.payment.infrastructure.restful.WeChatDataPackage;
import com.hightop.benyin.product.domain.dto.ProductTreeDto;
import com.hightop.benyin.product.domain.service.ProductDeviceDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductDevice;
import com.hightop.benyin.repair.price.domain.service.RepairPriceServiceDomain;
import com.hightop.benyin.repair.price.domain.service.VisitPriceServiceDomain;
import com.hightop.benyin.repair.price.infrastructure.entity.RepairPrice;
import com.hightop.benyin.repair.price.infrastructure.entity.VisitPrice;
import com.hightop.benyin.replace.domain.service.ReplaceDetailServiceDomain;
import com.hightop.benyin.replace.domain.service.ReplaceOrderServiceDomain;
import com.hightop.benyin.replace.infrastructure.entity.ReplaceDetail;
import com.hightop.benyin.replace.infrastructure.entity.ReplaceOrder;
import com.hightop.benyin.share.domain.service.AligenieNotificationDomainService;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.statistics.domain.event.RepairCounterEvent;
import com.hightop.benyin.statistics.infrastructure.constants.StatisticsConstants;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.work.order.api.dto.*;
import com.hightop.benyin.work.order.application.vo.*;
import com.hightop.benyin.work.order.domain.service.EngineerTrackDomainService;
import com.hightop.benyin.work.order.domain.service.RepairReportDomainService;
import com.hightop.benyin.work.order.domain.service.WorkEvaluateDomainService;
import com.hightop.benyin.work.order.domain.service.WorkOrderDomainService;
import com.hightop.benyin.work.order.infrastructure.entity.EngineerTrack;
import com.hightop.benyin.work.order.infrastructure.entity.RepairReport;
import com.hightop.benyin.work.order.infrastructure.entity.WorkEvaluate;
import com.hightop.benyin.work.order.infrastructure.entity.WorkOrder;
import com.hightop.benyin.work.order.infrastructure.enums.RepairProcessEnum;
import com.hightop.benyin.work.order.infrastructure.enums.WorkOrderCancelStatusEnum;
import com.hightop.benyin.work.order.infrastructure.enums.WorkOrderStatus;
import com.hightop.benyin.work.order.infrastructure.util.DateTimeUtil;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacyDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 工单服务
 * @Author: xhg
 * @Date: 2024/1/2 10:38
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class WorkOrderService {
    WorkOrderDomainService workOrderDomainService;
    SequenceDomainService sequenceDomainService;
    CustomerStaffDomainService customerStaffDomainService;
    RepairPriceServiceDomain repairPriceServiceDomain;
    TransactionDomainService transactionDomainService;
    CustomerDomainService customerDomainService;
    CustomerDeviceGroupDomainService customerDeviceGroupDomainService;
    CustomerBusinessDomainService customerBusinessDomainService;
    ProductTreeDomainService productTreeDomainService;
    AppealDomainService appealDomainService;
    ReplaceOrderServiceDomain replaceOrderServiceDomain;
    RepairReportDomainService repairReportDomainService;
    UserPrivacyDomainService userPrivacyDomainService;
    ItemStoreServiceDomain itemStoreServiceDomain;
    ProductDeviceDomainService productDeviceDomainService;
    IotCounterServiceDomain iotCounterServiceDomain;
    AligenieNotificationDomainService aligenieNotificationDomainService;
    VisitPriceServiceDomain visitPriceServiceDomain;
    ApplicationEventPublisher applicationEventPublisher;
    PayOrderDomainService payOrderDomainService;
    ReplaceDetailServiceDomain replaceDetailServiceDomain;
    WorkEvaluateDomainService workEvaluateDomainService;
    EngineerTrackDomainService engineerTrackDomainService;
    /**
     * 根据客户等级减去折扣费
     *
     * @param customer
     * @param p
     * @param totalPay
     * @return: {@link Long}
     * @Author: xhg
     * @Date: 2024/1/19 14:24
     */
    private Long calcDiscount(Customer customer, RepairPrice p, Long totalPay) {
        if (Objects.nonNull(customer) && Objects.nonNull(customer.getMembershipLevel())) {
            if (Objects.equals(MembershipLevel.VIP, customer.getMembershipLevel())) {
                return totalPay - p.getVipPrice();
            } else if (Objects.equals(MembershipLevel.INSTALLED, customer.getMembershipLevel())) {
                return totalPay - p.getDiscountPrice();
            } else if (Objects.equals(MembershipLevel.SIGNED, customer.getMembershipLevel())) {
                return totalPay - p.getSigningPrice();
            } else if (Objects.equals(MembershipLevel.REGISTERED, customer.getMembershipLevel())) {
                return totalPay - p.getRegisterPrice();
            }
        }
        return totalPay;
    }

    /**
     * 报修前校验
     *
     * @param deviceGroupId
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/9 11:25
     */
    public boolean beforeStartRepair(Long deviceGroupId) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        Long staffId = customerStaffDomainService.getRequiredCustomerStaff(
                customerId, ApplicationSessions.code()).getId();
        CustomerDeviceGroup cdg = this.customerDeviceGroupDomainService.getById(deviceGroupId);
        if (!cdg.getStatus()) {
            throw new MaginaException("当前设备组未启用，不可发起报修!");
        }
        CustomerBusiness customerBusiness = customerBusinessDomainService.lambdaQuery().eq(CustomerBusiness::getCustomerId, customerId).one();
        if (customerBusiness != null && customerBusiness.getSettleMethod() != null &&
                customerBusiness.getSettleMethod().equals(CustomerBusiness.CYCLE_SETTLE)) {
            return true;
        }

        // 校验机型是否有维修费用
        this.getModelRepairPrice(cdg.getProductId());
        Long settle = this.workOrderDomainService.lambdaQuery()
                .eq(WorkOrder::getCustomerStaffId, staffId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.TO_BE_SETTLED.getCode())
                .count();
        if (settle > 0) {
            throw new MaginaException("上一笔维修未确认报告或未支付金额，完成支付后可发起机器故障报修!");
        }
        Long waitConfirm = this.workOrderDomainService.lambdaQuery()
                .eq(WorkOrder::getCustomerStaffId, staffId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_CONFIRMED_REPORT.getCode())
                .count();
        if (waitConfirm > 0) {
            throw new MaginaException("上一笔维修工单尚未确认维修报告，工单完成后可发起机器故障报修!");
        }
        // 当前机型已经在维修了，不能发起第二次维修
        Long fixing = this.workOrderDomainService.lambdaQuery()
                .eq(WorkOrder::getDeviceGroupId, deviceGroupId)
                .notIn(WorkOrder::getStatus, Lists.newArrayList
                        (WorkOrderStatus.CLOSE.getCode(), WorkOrderStatus.COMPLETED.getCode()))
                .count();
        if (fixing > 0) {
            throw new MaginaException("当前设备组还未完成上次维修，不能重复发起维修!");
        }
        Customer customer = customerDomainService.getById(customerId);
        if (StringUtils.isBlank(customer.getAddress())
                || customer.getAddress().equals("未登地址")) {
            throw new MaginaException("请先在信息管理完善店铺后再进行报修!");
        }
        return true;
    }

    /**
     * 发起报修
     *
     * @param workOrder
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/2 13:56
     */
    public boolean startRepair(WorkOrder workOrder) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        Customer customer = customerDomainService.getById(customerId);
        Long staffId = customerStaffDomainService.getRequiredCustomerStaff(
                customerId, ApplicationSessions.code()).getId();
        // 工单编号设置
        workOrder.setCode(sequenceDomainService.nextDateSequence(WorkOrder.SEQ_PREFIX, WorkOrder.SEQ_LEN));
        // 工单状态默认为待提交
        workOrder.setStatus(WorkOrderStatus.PENDING_ORDERS);
        workOrder.setCurrentProcess(RepairProcessEnum.CREATE);
        workOrder.setCustomerId(customerId);
        workOrder.setCustomerStaffId(staffId);
        boolean assignEng = Objects.nonNull(workOrder.getEngineerId());
        workOrder.setIsAssignEngineer(assignEng);
        RepairPrice p = repairPriceServiceDomain.queryByProductId(workOrder.getProductId());
        workOrder.setRepairPay(p.getCheckPay());
        workOrder.setReplacePay(p.getReplacePay());
        workOrder.setVisitPay(p.getVisitPrice());
        Long discountAmount = 0L;
        if(!workOrder.getSerType().equals(SerTypeEnums.SCATTERED)){
            CustomerDeviceGroup cdg = this.customerDeviceGroupDomainService.getById(workOrder.getDeviceGroupId());
            workOrder.setMachineNum(cdg.getMachineNum());
        }
        // 新工单总费用=诊断费+基础上门费+追加报酬+远程误工费-维修折扣
        Long totalPay = p.getCheckPay() + p.getVisitPrice() + workOrder.getAdditionalPay() + workOrder.getLongWayVisitPay();
        if (Objects.nonNull(customer) && Objects.nonNull(customer.getMembershipLevel())) {
            if (Objects.equals(MembershipLevel.VIP, customer.getMembershipLevel())) {
                totalPay = totalPay - p.getVipPrice();
                discountAmount = p.getVipPrice();
            } else if (Objects.equals(MembershipLevel.INSTALLED, customer.getMembershipLevel())) {
                totalPay = totalPay - p.getDiscountPrice();
                discountAmount = p.getDiscountPrice();
            } else if (Objects.equals(MembershipLevel.SIGNED, customer.getMembershipLevel())) {
                totalPay = totalPay - p.getSigningPrice();
                discountAmount = p.getSigningPrice();
            } else if (Objects.equals(MembershipLevel.REGISTERED, customer.getMembershipLevel())) {
                totalPay = totalPay - p.getRegisterPrice();
                discountAmount = p.getRegisterPrice();
            }
        }
        workOrder.setDiscountAmount(discountAmount);

        workOrder.setTotalAmount(totalPay);
        workOrder.setTotalPay(totalPay);
        if (SerTypeEnums.getServeType().contains(workOrder.getSerType())) {
            workOrder.setTotalPay(0L);
        }


        // 修改设备状态
        this.customerDeviceGroupDomainService.updateDeviceStatus(workOrder.getDeviceGroupId(), CustomerDeviceGroup.DEVICE_STATUS_FIXING);
        // 事务提交后工单提醒
        ExecutorUtils.doAfterCommit(() -> this.aligenieNotificationDomainService.unicast("维修工单"));
        return this.workOrderDomainService.save(workOrder);
    }

    /**
     * 工单详情
     *
     * @param id
     * @return: {@link WorkOrder}
     * @Author: xhg
     * @Date: 2024/1/3 17:37
     */
    public WorkOrderAppletPageVo detail(Long id) {
        WorkOrderAppletPageVo exist = this.workOrderDomainService
                .selectJoinOne(WorkOrderAppletPageVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(WorkOrder.class)
                        .eq(WorkOrder::getId, id));
        // 回显设备组信息（即时被删了也可以查到）
        CustomerDeviceGroup cdg = this.customerDeviceGroupDomainService.getBaseMapper()
                .selectById(exist.getDeviceGroupId());
        ProductTreeDto info = this.productTreeDomainService.getFullProductTree(cdg.getProductId());
        cdg.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
        exist.setCdg(cdg);
        //路途时间：工程师点击“出发” 到点击“到店”的时间
        exist.setTravelTime(DateTimeUtil.calcDateTime(exist.getDepartureTime(), exist.getActualArriveTime()));
        //维修时长：工程师点击“到店”到发送“维修报告”的时间
        exist.setFixTime(DateTimeUtil.calcDateTime(exist.getActualArriveTime(), exist.getSendReportTime()));
        UserPrivacy userPrivacy = userPrivacyDomainService.getById(exist.getEngineerId().getId());
        if (userPrivacy != null) {
            exist.setEngineerMobile(userPrivacy.getMobileNumber().getValue());
        }
        exist.setIsContracted(SerTypeEnums.getServeType().contains(exist.getSerType()));
        exist.setIsAllServe(SerTypeEnums.getServeAllType().contains(exist.getSerType()));
        this.checkIsAppeal(exist);
        if(exist.getIsEvaluated()){
            exist.setWorkEvaluate(workEvaluateDomainService.getByWorkOrderCode(exist.getCode()));
        }
        return exist;
    }

    /**
     * 根据工单ID，获取维修报告
     *
     * @param workOrderId
     * @return
     */
    public RepairReportVo getReportByWorkOrderId(Long workOrderId) {
        RepairReportVo repairReportVo = new RepairReportVo();
        WorkOrder workOrder = workOrderDomainService.getById(workOrderId);

        ReplaceOrder replaceOrder = replaceOrderServiceDomain.getFullByWorkOrderId(workOrderId);
        if (replaceOrder != null && CollectionUtils.isNotEmpty(replaceOrder.getReplaceDetailList())) {
            for (ReplaceDetail replaceDetail : replaceOrder.getReplaceDetailList()) {
                ItemStore itemStore = itemStoreServiceDomain.getById(replaceDetail.getItemStoreId());
                replaceDetail.setItemStore(itemStore);
            }
        }
        RepairReport repairReport = repairReportDomainService.getByWorkOrderId(workOrderId);
        CustomerDeviceGroup customerDeviceGroup = customerDeviceGroupDomainService.getById(workOrder.getDeviceGroupId());
        ProductTreeDto info = this.productTreeDomainService.getFullProductTree(customerDeviceGroup.getProductId());
        customerDeviceGroup.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
        ProductDevice pdd = this.productDeviceDomainService.lambdaQuery()
                .eq(ProductDevice::getProductId, workOrder.getProductId()).one();
        if (pdd != null) {
            repairReportVo.setColorType(pdd.getColor());
        }
        repairReportVo.setWorkOrder(workOrder);
        repairReportVo.setRepairReport(repairReport);
        repairReportVo.setReplaceOrder(replaceOrder);
        repairReportVo.setIsContracted(SerTypeEnums.getServeType().contains(workOrder.getSerType()));
        repairReportVo.setCustomerDeviceGroup(customerDeviceGroup);
        return repairReportVo;
    }

    /**
     * 我的维修单分页列表
     *
     * @param pageQuery
     * @return: {@link DataGrid< WorkOrderAppletPageVo>}
     * @Author: xhg
     * @Date: 2024/1/3 17:37
     */
    public DataGrid<WorkOrderAppletPageVo> page(WorkOrderPageQuery pageQuery) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        CustomerStaff customerStaff = customerStaffDomainService.getRequiredCustomerStaff(
                customerId, ApplicationSessions.code());
        Long staffId = customerStaff.getId();
        //老板或者店长看所有数据
        boolean staff = true;
        String roleId = customerStaff.getRole().getValue();
        if (StringUtils.isNotBlank(roleId) && (roleId.equals("501") || roleId.equals("505"))) {
            staff = false;//表示非员工
        }

        boolean all = CollectionUtils.isEmpty(pageQuery.getWorkOrderStatus());
        boolean isRepairman = false;
        if(roleId.equals("504") && all){
            isRepairman = true;
            staff = false;
        }
        boolean isStaff = staff;
        boolean finalIsRepairman = isRepairman;
        return PageHelper.startPage(pageQuery, p -> this.workOrderDomainService
                        .selectJoinList(WorkOrderAppletPageVo.class, MPJWrappers.lambdaJoin()
                                .selectAll(WorkOrder.class)
                                .eq(WorkOrder::getCustomerId, customerId)
                                .eq(isStaff, WorkOrder::getCustomerStaffId, staffId)
                                .apply(finalIsRepairman, " ((t.status != 'completed') or (t.customer_staff_id = {0} and t.status = 'completed'))", staffId)
                                .in(!all, WorkOrder::getStatus, pageQuery.getWorkOrderStatus())
                                .orderByDesc(!all, WorkOrder::getCreatedAt)
                                // 所有状态工单，按照修改时间
                                .orderByDesc(all, WorkOrder::getUpdatedAt)
                        ))
                .peek(p -> {
                            // 回显设备组信息（即时被删了也可以查到）
                            p.setCdg(this.customerDeviceGroupDomainService.getBaseMapper().selectById(p.getDeviceGroupId()));
                            this.checkIsAppeal(p);
                            if (p.getPayMode() != null && p.getPayMode().equals(PayModeEnum.OFFLINE)) {
                                PayOrder payOrder = payOrderDomainService.getByOrderNumber(p.getCode(),null);
                                if (payOrder != null) {
                                    p.setPayStatus(payOrder.getStatus());
                                }
                            }
                            p.setIsContracted(SerTypeEnums.getServeType().contains(p.getSerType()));
                            p.setIsAllServe(SerTypeEnums.getServeAllType().contains(p.getSerType()));
                        }
                );
    }

    public DataGrid<WorkOrderAppletPageVo> mechinePage(WorkOrderPageQuery pageQuery) {
        DataGrid<WorkOrderAppletPageVo> workOrderDataGrid = PageHelper.startPage(pageQuery, p -> this.workOrderDomainService
                .selectJoinList(WorkOrderAppletPageVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(WorkOrder.class)
                        .selectAs(RepairReport::getResolveDesc, WorkOrderAppletPageVo::getResolveDesc)
                        .leftJoin(RepairReport.class, RepairReport::getWorkOrderId, WorkOrder::getId)
                        .eq(pageQuery.getCustomerId() != null, WorkOrder::getCustomerId, pageQuery.getCustomerId())
                        .eq(pageQuery.getDeviceGroupId() != null, WorkOrder::getDeviceGroupId, pageQuery.getDeviceGroupId())
                        .ne(WorkOrder::getStatus, WorkOrderStatus.CLOSE)
                        .orderByDesc(WorkOrder::getCreatedAt)
                ));
        if (pageQuery.getDeviceGroupId() != null && workOrderDataGrid.getTotal() > 0) {
            // 当前设备组最新计数器
            IotCounter counter = iotCounterServiceDomain.getLatestCounter(pageQuery.getDeviceGroupId());
            if (counter != null) {
                workOrderDataGrid.getRows().forEach(p -> {
                    p.setCurrBlackCount(counter.getBlackWhiteCounter());
                    p.setCurrColorCount(counter.getCyanCounter());
                });
            } else {
                // 上次维修的维修报告[只获取已完成的工单的维修报告]的计数器
                RepairReport lastRepairReport = this.repairReportDomainService.selectJoinOne(RepairReport.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(RepairReport.class)
                                .innerJoin(WorkOrder.class, WorkOrder::getId, RepairReport::getWorkOrderId)
                                .eq(RepairReport::getDeviceGroupId, pageQuery.getDeviceGroupId())
                                .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED)
                                .orderByDesc(RepairReport::getFinishTime)
                                .last(" LIMIT 1")
                );
                if (lastRepairReport != null) {
                    workOrderDataGrid.getRows().forEach(p -> {
                        p.setCurrBlackCount(lastRepairReport.getBlackWhiteCount());
                        p.setCurrColorCount(lastRepairReport.getColorCount());
                    });
                }
            }
        }
        return workOrderDataGrid;
    }

    /**
     * 工单分配/指派
     *
     * @param ced
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/8 11:30
     */
    public boolean changeEngineer(ChangeEngineerDto ced) {
        WorkOrder exist = this.workOrderDomainService.getById(ced.getId());
        // 如果工程师没接单，可以直接分配
        if (!Objects.equals(WorkOrderStatus.PENDING_ORDERS, exist.getStatus())) {
            throw new MaginaException("工单状态非待接单，不可以重新指定工程师");
        }
        return this.workOrderDomainService.lambdaUpdate()
                .set(WorkOrder::getEngineerId, ced.getEngineerId())
                .eq(WorkOrder::getId, exist.getId())
                .update();
    }

    /**
     * 客户确认工程师接单
     *
     * @param
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/3 17:38
     */
    public boolean confirm(ConfirmOrderDto cod) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        WorkOrder exist = this.workOrderDomainService.getById(cod.getId());
        RepairPrice p = repairPriceServiceDomain.queryByProductId(exist.getProductId());
        Customer customer = customerDomainService.getById(customerId);
        // 新工单总费用=诊断费+基础上门费+追加报酬
        Long totalPay = p.getCheckPay() + p.getVisitPrice() + exist.getAdditionalPay();
        // 减去会员折扣费
        totalPay = this.calcDiscount(customer, p, totalPay);
        Long appointEngineer = Objects.nonNull(cod.getEngineerId()) ? cod.getEngineerId() : null;
        return this.workOrderDomainService.lambdaUpdate()
                // 如果拒绝需要重新选择工程师，再次更改为待接单状态
                .set(WorkOrder::getStatus, cod.getAccept() ? WorkOrderStatus.ENGINEER_RECEIVE : WorkOrderStatus.PENDING_ORDERS)
                .set(!cod.getAccept(), WorkOrder::getCurrentProcess, RepairProcessEnum.CREATE)
                // 如果接受则默认工程师接单，总价加上工程师加价减去减免;工程师接单的时候，已经重新计算过总价;拒绝：清除工单上工程师加价、减免信息
                .set(!cod.getAccept(), WorkOrder::getEngineerAdditionalPay, null)
                .set(!cod.getAccept(), WorkOrder::getDerateAmount, null)
                .set(!cod.getAccept(), WorkOrder::getLongWayVisitPay, null)
                // 如果拒绝，需要重新计算总价并归还工单到工单池/指定到工程师
                .set(!cod.getAccept(), WorkOrder::getTotalPay, totalPay)
                .set(!cod.getAccept(), WorkOrder::getEngineerId, appointEngineer)
                .eq(WorkOrder::getId, exist.getId())
                .update();
    }

    /**
     * 转让工单
     *
     * @param
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/3 17:38
     */
    public boolean transfer(TransferenceDto cod) {
        WorkOrder workOrder = this.workOrderDomainService.getById(cod.getId());
        if (workOrder == null) {
            throw new MaginaException("工单不存在");
        }

        UserPrivacy userPrivacy = userPrivacyDomainService.getById(cod.getEngineerId());
        if (userPrivacy == null) {
            throw new MaginaException("工程师不存在");
        }
        workOrder.setEngineerId(new UserEntry().setId(cod.getEngineerId()));
        return this.workOrderDomainService.updateById(workOrder);
    }


    /**
     * 客户主动取消工单，需要工程师确认才生效
     *
     * @param id
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/4 11:15
     */
    public boolean cancelOrder(Long id) {
        WorkOrder exist = this.workOrderDomainService.getById(id);
        // 未接单的工单店铺可以取消。
        if (!Objects.equals(WorkOrderStatus.PENDING_ORDERS, exist.getStatus())) {
            throw new MaginaException("已有工程师接单，不可取消");
        }

        // 修改设备状态
        this.customerDeviceGroupDomainService.updateDeviceStatus(exist.getDeviceGroupId(), CustomerDeviceGroup.DEVICE_STATUS_NORMAL);
        this.workOrderDomainService.lambdaUpdate()
                .set(WorkOrder::getStatus, WorkOrderStatus.CLOSE)
                .set(WorkOrder::getEngineerId, null)
                .set(WorkOrder::getCancelStatus, WorkOrderCancelStatusEnum.CUSTOMER_CANCEL)
                .eq(WorkOrder::getId, exist.getId())
                .update();
        //关闭支付凭证
        if (exist.getPayMode() != null && exist.getPayMode().equals(PayModeEnum.OFFLINE)) {
            ExecutorUtils.doAfterCommit(() -> {
                applicationEventPublisher.publishEvent(new PayVoucherCloseEvent(exist.getCode(), TradeOrderOrigin.REPAIR_ORDER));
            });
        }
        return true;
    }

    /**
     * 根据机型获取当前机型的维修费用
     *
     * @param productId
     * @return: {@link WorkOrderRepairPriceVo}
     * @Author: xhg
     * @Date: 2024/1/4 15:24
     */
    public WorkOrderRepairPriceVo getModelRepairPrice(Long productId) {
        RepairPrice p = this.repairPriceServiceDomain.queryByProductId(productId);
        Long customerId = CustomerMpUtils.requiredCustomerId();
        Customer customer = customerDomainService.getById(customerId);
        WorkOrderRepairPriceVo vo = new WorkOrderRepairPriceVo();
        vo.setCustomerSeqId(customer.getSeqId());
        vo.setCheckPay(p.getCheckPay());
        vo.setVisitPrice(p.getVisitPrice());

        VisitPrice visitPrice =
                visitPriceServiceDomain.getRequiredRegionVisitPrice(
                        510108,
                        customer.getRegionCode()
                );
        vo.setLongWayVisitPay(visitPrice.getPrice());
        if (Objects.nonNull(customer) && Objects.nonNull(customer.getMembershipLevel())) {
            if (Objects.equals(MembershipLevel.VIP, customer.getMembershipLevel())) {
                vo.setDisCountPay(p.getVipPrice());
            } else if (Objects.equals(MembershipLevel.INSTALLED, customer.getMembershipLevel())) {
                vo.setDisCountPay(p.getDiscountPrice());
            } else if (Objects.equals(MembershipLevel.SIGNED, customer.getMembershipLevel())) {
                vo.setDisCountPay(p.getSigningPrice());
            } else if (Objects.equals(MembershipLevel.REGISTERED, customer.getMembershipLevel())) {
                vo.setDisCountPay(p.getRegisterPrice());
            } else {
                vo.setDisCountPay(0L);
            }
        }
        return vo;
    }

    /**
     * 重新计算机型的维修总价
     *
     * @param tpc
     * @return: {@link String}
     * @Author: xhg
     * @Date: 2024/1/19 10:41
     */
    public String getTotalPay(TotalPayCalcVo tpc) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        Customer customer = customerDomainService.getById(customerId);
        RepairPrice p = this.repairPriceServiceDomain.queryByProductId(tpc.getProductId());
        VisitPrice visitPrice =
                visitPriceServiceDomain.getRequiredRegionVisitPrice(
                        510108,
                        customer.getRegionCode()
                );
        // 新工单总费用=诊断费+基础上门费+追加报酬+远程误工费
        Long totalPay = p.getCheckPay() + p.getVisitPrice() + tpc.getAdditionalPay() + visitPrice.getPrice();
        totalPay = this.calcDiscount(customer, p, totalPay);
        return new BigDecimal(totalPay).divide(BigDecimal.TEN.pow(2), 2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 获取当前未完成支付的工单、待确认（工程师取消）的工单
     *
     * @param
     * @return: {@link List< Long>}
     * @Author: xhg
     * @Date: 2024/1/4 14:01
     */
    public MessagePromptVo getPaymentAndConfirmList() {
        MessagePromptVo vo = new MessagePromptVo();
        Long customerId = CustomerMpUtils.requiredCustomerId();
        Long staffId = customerStaffDomainService.getRequiredCustomerStaff(
                customerId, ApplicationSessions.code()).getId();
        vo.setPayment(this.workOrderDomainService.lambdaQuery()
                .select(WorkOrder::getId)
                .eq(WorkOrder::getCustomerId, customerId)
                .eq(WorkOrder::getCustomerStaffId, staffId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.TO_BE_SETTLED)
                .list().stream().map(WorkOrder::getId).collect(Collectors.toList()));
        vo.setConfirm(this.workOrderDomainService.lambdaQuery()
                .select(WorkOrder::getId)
                .eq(WorkOrder::getCustomerId, customerId)
                .eq(WorkOrder::getCustomerStaffId, staffId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.CUSTOMER_CONFIRMING)
                .list().stream().map(WorkOrder::getId).collect(Collectors.toList()));
        return vo;
    }

    /**
     * 确认维修报告
     *
     * @param id
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/4 15:03
     */
    public boolean confirmReport(Long id) {
        WorkOrder workOrder = this.workOrderDomainService.getById(id);
        if (Objects.isNull(workOrder)) {
            throw new MaginaException("工单不存在");
        }
        if (!Objects.equals(WorkOrderStatus.WAIT_CONFIRMED_REPORT, workOrder.getStatus())) {
            throw new MaginaException("工单状态非待确认，不可以确认维修报告");
        }

        this.checkSerType(workOrder);
        this.workOrderDomainService.updateById(workOrder);
        // 事务提交时发布回调事件
        ExecutorUtils.doAfterCommit(
                StatisticsConstants.EXECUTOR,
                () ->
                        this.applicationEventPublisher.publishEvent(
                                RepairCounterEvent.builder()
                                        .workCode(workOrder.getCode())
                                        .deviceGroupId(workOrder.getDeviceGroupId())
                                        .build()
                        )
        );
        return Boolean.TRUE;
    }

    public void checkSerType(WorkOrder workOrder) {
        if (SerTypeEnums.getServeType().contains(workOrder.getSerType())) {
            //全保直接完成工单
            if (SerTypeEnums.getServeAllType().contains(workOrder.getSerType())) {
                workOrder.setStatus(WorkOrderStatus.COMPLETED);
                workOrder.setCurrentProcess(RepairProcessEnum.DONE);
                workOrder.setCompletedAt(LocalDateTime.now());
                workOrder.setConfirmReportTime(LocalDateTime.now());
                workOrder.setTotalPay(0L);
                workOrder.setDiscountAmount(workOrder.getTotalAmount());
            }

            if (SerTypeEnums.getServeHalfType().contains(workOrder.getSerType())) {
                if (workOrder.getItemPay() != null && workOrder.getItemPay() > 0) {
                    Long pinkAmount = replaceDetailServiceDomain.selectJoinOne(Long.class, MPJWrappers.lambdaJoin()
                            .selectSum(ReplaceDetail::getAmount)
                            .selectAs(StorageArticle::getArticleType, SaleSku::getArticleType)
                            .selectAs(StorageArticle::getType, SaleSku::getType)
                            .leftJoin(ReplaceOrder.class, ReplaceOrder::getId, ReplaceDetail::getReplaceOrderId)
                            .leftJoin(SaleSku.class, SaleSku::getId, ReplaceDetail::getSaleSkuId)
                            .leftJoin(StorageArticle.class, StorageArticle::getCode, SaleSku::getArticleCode)
                            .eq(ReplaceOrder::getWorkOrderId, workOrder.getId())
                            .eq(StorageArticle::getType, SaleSku.PINK_TYPE)
                    );
                    if (pinkAmount != null && pinkAmount > 0) {
                        workOrder.setDiscountAmount(workOrder.getTotalAmount() - pinkAmount);
                        workOrder.setTotalPay(pinkAmount);
                        workOrder.setConfirmReportTime(LocalDateTime.now());
                        workOrder.setStatus(WorkOrderStatus.TO_BE_SETTLED);
                    } else {
                        //半保不含粉 直接完成工单
                        workOrder.setStatus(WorkOrderStatus.COMPLETED);
                        workOrder.setCurrentProcess(RepairProcessEnum.DONE);
                        workOrder.setConfirmReportTime(LocalDateTime.now());
                        workOrder.setCompletedAt(LocalDateTime.now());
                        workOrder.setTotalPay(0L);
                        workOrder.setDiscountAmount(workOrder.getTotalAmount());
                    }
                } else {
                    //半保不含粉 直接完成工单
                    workOrder.setStatus(WorkOrderStatus.COMPLETED);
                    workOrder.setCurrentProcess(RepairProcessEnum.DONE);
                    workOrder.setConfirmReportTime(LocalDateTime.now());
                    workOrder.setCompletedAt(LocalDateTime.now());
                    workOrder.setTotalPay(0L);
                    workOrder.setDiscountAmount(workOrder.getTotalAmount());
                }
            }
        } else {
            //支付费用为0 直接完成工单
            if (workOrder.getTotalPay() == 0L) {
                workOrder.setStatus(WorkOrderStatus.COMPLETED);
                workOrder.setCurrentProcess(RepairProcessEnum.DONE);
                workOrder.setConfirmReportTime(LocalDateTime.now());
                workOrder.setCompletedAt(LocalDateTime.now());
            } else {
                workOrder.setConfirmReportTime(LocalDateTime.now());
                workOrder.setStatus(WorkOrderStatus.TO_BE_SETTLED);
            }
        }

        //更新设备状态
        if (workOrder.getStatus().equals(WorkOrderStatus.COMPLETED)) {
            this.customerDeviceGroupDomainService.updateDeviceStatus(
                    workOrder.getDeviceGroupId(), CustomerDeviceGroup.DEVICE_STATUS_NORMAL
            );
        }
    }

    /**
     * 获取工单需要支付的金额
     *
     * @param id
     * @return: {@link String}
     * @Author: xhg
     * @Date: 2024/1/4 15:11
     */
    public String getPayAmount(Long id) {
        WorkOrder workOrder = this.workOrderDomainService.getById(id);
        return new BigDecimal(Optional.ofNullable(workOrder)
                .map(WorkOrder::getTotalPay)
                .orElse(0L))
                .divide(BigDecimal.TEN.pow(2), 2, RoundingMode.HALF_UP)
                .toString();
    }

    /**
     * 支付工单
     *
     * @param id
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/4 15:04
     */
    public WeChatDataPackage payOrder(Long id) {
        WorkOrder workOrder = this.workOrderDomainService.getById(id);
        if (!Objects.equals(WorkOrderStatus.TO_BE_SETTLED, workOrder.getStatus())) {
            throw new MaginaException("工单状态不可支付");
        }
        workOrder.setPayMode(PayModeEnum.WECHART);
        workOrderDomainService.updateById(workOrder);
        // 完成支付
        PayOrderVo payOrderVo = this.transactionDomainService.order(ApplicationSessions.openId(),
                workOrder.getCode(), TradeOrderOrigin.REPAIR_ORDER,
                CustomerMpUtils.requiredCustomerId(),
                workOrder.getTotalPay(), workOrder.getCode(), null, 0L);
        return payOrderVo.getWeChatDataPackage();

    }

    /**
     * 修改追加报酬
     *
     * @param sad
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/4 15:50
     */
    public synchronized boolean fixDerateAmount(SubstractPayDto sad) {
        WorkOrder exist = this.workOrderDomainService.getById(sad.getId());
        //已完成工单就不可以追加报酬了
        if (Objects.equals(WorkOrderStatus.COMPLETED, exist.getStatus())) {
            throw new MaginaException("工单已完成，不能继续修改!");
        }

        Long totalPay = exist.getTotalPay();
        if (sad.getDerateAmount() != null) {
            totalPay = totalPay + exist.getDerateAmount() - sad.getDerateAmount();
            exist.setDerateAmount(sad.getDerateAmount());
        }

        if (sad.getEngineerAdditionalPay() != null) {
            if (exist.getEngineerAdditionalPay() != null) {
                totalPay = totalPay - exist.getEngineerAdditionalPay();
            }
            exist.setEngineerAdditionalPay(sad.getEngineerAdditionalPay());
            totalPay = totalPay + exist.getEngineerAdditionalPay();
        }
        exist.setTotalPay(totalPay);
        exist.setTotalAmount(exist.getTotalPay());

        return this.workOrderDomainService.updateById(exist);
    }

    /**
     * 工程师
     *
     * @param sad
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/4 15:50
     */
    public synchronized boolean fixAdditionalPay(SubmitAppDto sad) {
        WorkOrder exist = this.workOrderDomainService.getById(sad.getId());
        // 有工程师接单就不可以追加报酬了
//        if (Objects.equals(WorkOrderStatus.CUSTOMER_CONFIRMING, exist.getStatus())) {
//            throw new MaginaException("已有工程师接单，不能继续追加报酬!");
//        }
        return this.workOrderDomainService.lambdaUpdate()
                .set(WorkOrder::getAdditionalPay, sad.getAdditionalPay())
                // 总费用需要更新
                .set(WorkOrder::getTotalPay, (exist.getTotalPay() - exist.getAdditionalPay() + sad.getAdditionalPay()))
                .eq(WorkOrder::getId, exist.getId())
                .update();
    }

    /**
     * 历史报修：只查已关闭和已完成的工单
     *
     * @param deviceGroupId
     * @return: {@link List< WorkOrderAppletPageVo>}
     * @Author: xhg
     * @Date: 2024/1/5 16:27
     */
    public List<WorkOrderAppletPageVo> historyList(Long deviceGroupId) {
        Long customerId = CustomerMpUtils.requiredCustomerId();
        CustomerStaff customerStaff = customerStaffDomainService.getRequiredCustomerStaff(
                customerId, ApplicationSessions.code());
        Long staffId = customerStaff.getId();
        //老板或者店长看所有数据
        boolean staff = true;
        String roleId = customerStaff.getRole().getValue();
        if (StringUtils.isNotBlank(roleId) && (roleId.equals("501") || roleId.equals("505"))) {
            staff = false;//表示非员工
        }
        List<WorkOrderAppletPageVo> res = this.workOrderDomainService.selectJoinList(
                WorkOrderAppletPageVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(WorkOrder.class)
                        // 不区分店铺，直接查询设备组id
                        .eq(WorkOrder::getDeviceGroupId, deviceGroupId)
                        .eq(staff , WorkOrder::getCustomerStaffId, staffId)
                        .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED)
                        .orderByDesc(WorkOrder::getUpdatedAt)
        );
        if (CollectionUtils.isNotEmpty(res)) {
            res.stream().forEach(e -> {
                        e.setIsContracted(SerTypeEnums.getServeType().contains(e.getSerType()));
                        e.setIsAllServe(SerTypeEnums.getServeAllType().contains(e.getSerType()));
                        e.setCdg(this.customerDeviceGroupDomainService.getById(e.getDeviceGroupId()));
                    }
            );

        }
        return res;
    }

    /**
     * PC端的维修工单分页列表
     *
     * @param query
     * @return: {@link DataGrid<  WorkOrderPcListVo >}
     * @Author: xhg
     * @Date: 2024/1/8 10:29
     */
    public DataGrid<WorkOrderPcListVo> pcPage(WorkOrderPcPageQuery query) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAll(WorkOrder.class)
                .selectAs(Customer::getName, WorkOrderPcListVo::getCustomerName)
                .selectAs(Customer::getSubbranch, WorkOrderPcListVo::getSubbranch)
                .selectAs(Customer::getSeqId, WorkOrderPcListVo::getCustomerSeq)
                .selectAs(Customer::getLocation, WorkOrderPcListVo::getLocation)
                .selectAs(CustomerDeviceGroup::getDeviceGroup, WorkOrderPcListVo::getDeviceGroup)
                .selectAs(CustomerStaff::getTel, WorkOrderPcListVo::getPhone)
                .innerJoin(CustomerDeviceGroup.class, CustomerDeviceGroup::getId, WorkOrder::getDeviceGroupId)
                .leftJoin(Customer.class, Customer::getId, WorkOrder::getCustomerId)
                .leftJoin(CustomerStaff.class, CustomerStaff::getId, WorkOrder::getCustomerStaffId);
        this.addCondition(query, wrapper);
        wrapper.orderByDesc(WorkOrder::getCreatedAt);
        return PageHelper.startPage(query, page -> this.workOrderDomainService.selectJoinList(WorkOrderPcListVo.class, wrapper
        )).peek(p -> {
            ProductTreeDto info = this.productTreeDomainService.getFullProductTree(p.getProductId());
            p.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
        });
    }

    public WorkOrderPcListVo total(WorkOrderPcPageQuery query) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectCount(WorkOrder::getId, "orderNum")
                .selectSum(WorkOrder::getTotalPay, "totalPay")
                .selectSum(WorkOrder::getItemPay, "itemPay")
                .selectSum(WorkOrder::getDiscountAmount, "discountAmount")
                .selectSum(WorkOrder::getTotalAmount, "totalAmount")
                // 只查询已完成、待结算、待审核的工单
                .in(WorkOrder::getStatus, Lists.newArrayList(WorkOrderStatus.COMPLETED, WorkOrderStatus.TO_BE_SETTLED,
                        WorkOrderStatus.WAIT_AUDIT))
                .leftJoin(CustomerDeviceGroup.class, CustomerDeviceGroup::getId, WorkOrder::getDeviceGroupId)
                .leftJoin(Customer.class, Customer::getId, WorkOrder::getCustomerId)
                .leftJoin(CustomerStaff.class, CustomerStaff::getId, WorkOrder::getCustomerStaffId);
        this.addCondition(query, wrapper);
        WorkOrderPcListVo workOrderPcListVo = this.workOrderDomainService.selectJoinOne(WorkOrderPcListVo.class, wrapper);
        workOrderPcListVo.setTotalPay(workOrderPcListVo.getTotalPay() == null ? 0L : workOrderPcListVo.getTotalPay());
        workOrderPcListVo.setTotalAmount(workOrderPcListVo.getTotalAmount() == null ? 0L : workOrderPcListVo.getTotalAmount());

        workOrderPcListVo.setItemPay(workOrderPcListVo.getItemPay() == null ? 0L : workOrderPcListVo.getItemPay());
        workOrderPcListVo.setLaborAmount(workOrderPcListVo.getTotalAmount() - workOrderPcListVo.getItemPay());
        return workOrderPcListVo;
    }

    private void addCondition(WorkOrderPcPageQuery query, MPJLambdaWrapper<Object> wrapper) {
        wrapper.eq(Objects.nonNull(query.getEngineerId()), WorkOrder::getEngineerId, query.getEngineerId())
                .like(StringUtils.isNotBlank(query.getCustomerName()), Customer::getName, query.getCustomerName())
                .like(StringUtils.isNotBlank(query.getCustomerSeq()), Customer::getSeqId, query.getCustomerSeq())
                .like(StringUtils.isNotBlank(query.getPhone()), CustomerStaff::getTel, query.getPhone())
                .like(StringUtils.isNotBlank(query.getCode()), WorkOrder::getCode, query.getCode())
                .like(StringUtils.isNotBlank(query.getErrorCode()), WorkOrder::getErrorCode, query.getErrorCode())
                .in(CollectionUtils.isNotEmpty(query.getProductIds()), WorkOrder::getProductId, query.getProductIds())
                .like(StringUtils.isNotBlank(query.getDeviceGroup()), CustomerDeviceGroup::getDeviceGroup, query.getDeviceGroup())
                .eq(StringUtils.isNotBlank(query.getSerType()), WorkOrder::getSerType, query.getSerType())
                .eq(StringUtils.isNotBlank(query.getPayMode()), WorkOrder::getPayMode, query.getPayMode())
                .eq(query.getCustomerId() != null, WorkOrder::getCustomerId, query.getCustomerId())
                .eq(query.getIsEvaluated() != null, WorkOrder::getIsEvaluated, query.getIsEvaluated())
                .in(CollectionUtils.isNotEmpty(query.getSerTypes()), WorkOrder::getSerType, query.getSerTypes())
                .in(CollectionUtils.isNotEmpty(query.getStatus()), WorkOrder::getStatus, query.getStatus())
                .ge(StringUtils.isNotBlank(query.getCreateTimeStart()), WorkOrder::getCreatedAt, query.getCreateTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getCreateTimeEnd()), WorkOrder::getCreatedAt, query.getCreateTimeEnd() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getCompleteTimeStart()), WorkOrder::getCompletedAt, query.getCompleteTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getCompleteTimeEnd()), WorkOrder::getCompletedAt, query.getCompleteTimeEnd() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getSendReportTimeStart()), WorkOrder::getSendReportTime, query.getSendReportTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getSendReportTimeEnd()), WorkOrder::getSendReportTime, query.getSendReportTimeEnd() + " 23:59:59");
    }

    /**
     * PC端维修工单详情
     *
     * @param id
     * @return: {@link WorkOrderPcDetailVo}
     * @Author: xhg
     * @Date: 2024/1/8 10:42
     */
    public WorkOrderPcDetailVo pcDetail(Long id) {
        WorkOrderPcDetailVo wo = this.workOrderDomainService.selectJoinOne(
                WorkOrderPcDetailVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(WorkOrder.class)
                        .selectAs(CustomerDeviceGroup::getDeviceGroup, WorkOrderPcDetailVo::getDeviceGroup)
                        .selectAs(CustomerDeviceGroup::getDeviceGroupImg, WorkOrderPcDetailVo::getDeviceGroupImg)
                        .selectAs(CustomerStaff::getTel, WorkOrderPcDetailVo::getContactPhone)
                        .selectAs(CustomerStaff::getName, WorkOrderPcDetailVo::getCustomerStaff)
                        .innerJoin(CustomerDeviceGroup.class, CustomerDeviceGroup::getId, WorkOrder::getDeviceGroupId)
                        .leftJoin(CustomerStaff.class, CustomerStaff::getId, WorkOrder::getCustomerStaffId)
                        .eq(WorkOrder::getId, id));
        wo.setCustomer(this.customerDomainService.getBaseMapper().detail(wo.getCustomerId()));
        ProductTreeDto info = this.productTreeDomainService.getFullProductTree(wo.getProductId());
        wo.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
        wo.setAppealCount(this.appealDomainService.lambdaQuery()
                .eq(Appeal::getWorkOrderId, id).count());
        // 找到工单的唯一维修报告
        List<RepairReport> repairReport = this.repairReportDomainService.lambdaQuery()
                .eq(RepairReport::getWorkOrderId, id).list();
        if (CollectionUtils.isNotEmpty(repairReport)) {
            wo.setRepairReport(repairReport.get(0).getId());
        }
        wo.setLaborCost(wo.getRepairPay() + wo.getVisitPay());
        if (wo.getLongWayVisitPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getLongWayVisitPay());
        }
        if (wo.getEngineerAdditionalPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getEngineerAdditionalPay());
        }
        if (wo.getActualReplacePay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getActualReplacePay());
        }
        if (wo.getAdditionalPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getAdditionalPay());
        }
/*        if (wo.getDiscountAmount() != null) {
            wo.setLaborCost(wo.getLaborCost() - wo.getDiscountAmount());
        }
        if (wo.getDerateAmount() != null) {
            wo.setLaborCost(wo.getLaborCost() - wo.getDerateAmount());
        }*/
        if (wo.getLaborCost() < 0) {
            wo.setLaborCost(0L);
        }

        if(wo.getIsEvaluated()){
            wo.setWorkEvaluate(workEvaluateDomainService.getByWorkOrderCode(wo.getCode()));
        }
        //路途时间：工程师点击“出发” 到点击“到店”的时间
        wo.setTravelTime(DateTimeUtil.calcDateTime(wo.getDepartureTime(), wo.getActualArriveTime()));
        //维修时长：工程师点击“到店”到发送“维修报告”的时间
        wo.setFixTime(DateTimeUtil.calcDateTime(wo.getActualArriveTime(), wo.getSendReportTime()));

        // 查询工程师轨迹地址信息
        setEngineerTrackAddresses(wo);

        return wo;
    }

    /**
     * 设置工程师轨迹地址信息
     * @param wo 工单详情VO
     */
    private void setEngineerTrackAddresses(WorkOrderPcDetailVo wo) {
        if (wo.getId() == null) {
            return;
        }

        // 查询工程师出发轨迹（ENGINEER_DEPARTURE）
        EngineerTrack departureTrack = engineerTrackDomainService.lambdaQuery()
                .eq(EngineerTrack::getWorkOrderId, wo.getId())
                .eq(EngineerTrack::getCurrentProcess, RepairProcessEnum.ENGINEER_DEPARTURE)
                .orderByDesc(EngineerTrack::getCreatedAt)
                .last("LIMIT 1")
                .one();

        if (departureTrack != null && StringUtils.isNotEmpty(departureTrack.getAddress())) {
            wo.setDepartureAddress(departureTrack.getAddress());
        }

        // 暂时注释掉到店轨迹查询
        // // 查询工程师到店轨迹（ENGINEER_ARRIVE）
        // EngineerTrack arrivalTrack = engineerTrackDomainService.lambdaQuery()
        //         .eq(EngineerTrack::getWorkOrderId, wo.getId())
        //         .eq(EngineerTrack::getCurrentProcess, RepairProcessEnum.ENGINEER_ARRIVE)
        //         .orderByDesc(EngineerTrack::getCreatedAt)
        //         .last("LIMIT 1")
        //         .one();
        //
        // if (arrivalTrack != null && StringUtils.isNotEmpty(arrivalTrack.getAddress())) {
        //     wo.setArrivalAddress(arrivalTrack.getAddress());
        // }
    }

    /**
     * PC端维修工单详情
     *
     * @param code
     * @return: {@link WorkOrderPcDetailVo}
     * @Author: xhg
     * @Date: 2024/1/8 10:42
     */
    public WorkOrderPcDetailVo detailByCode(String code) {
        WorkOrderPcDetailVo wo = this.workOrderDomainService.selectJoinOne(
                WorkOrderPcDetailVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(WorkOrder.class)
                        .selectAs(CustomerDeviceGroup::getDeviceGroup, WorkOrderPcDetailVo::getDeviceGroup)
                        .selectAs(CustomerDeviceGroup::getDeviceGroupImg, WorkOrderPcDetailVo::getDeviceGroupImg)
                        .innerJoin(CustomerDeviceGroup.class, CustomerDeviceGroup::getId, WorkOrder::getDeviceGroupId)
                        .eq(WorkOrder::getCode, code));
        wo.setCustomer(this.customerDomainService.getBaseMapper().detail(wo.getCustomerId()));
        ProductTreeDto info = this.productTreeDomainService.getFullProductTree(wo.getProductId());
        wo.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
        wo.setAppealCount(this.appealDomainService.lambdaQuery()
                .eq(Appeal::getWorkOrderCode, code).count());
        // 找到工单的唯一维修报告
        List<RepairReport> repairReport = this.repairReportDomainService.lambdaQuery()
                .eq(RepairReport::getWorkOrderCode, code).list();
        if (CollectionUtils.isNotEmpty(repairReport)) {
            wo.setRepairReport(repairReport.get(0).getId());
        }
        wo.setLaborCost(0L);
        if (wo.getLongWayVisitPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getLongWayVisitPay());
        }
        if (wo.getDiscountAmount() != null) {
            wo.setLaborCost(wo.getLaborCost() - wo.getDiscountAmount());
        }
        if (wo.getVisitPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getVisitPay());
        }
        if (wo.getRepairPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getRepairPay());
        }
        if (wo.getActualReplacePay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getActualReplacePay());
        }

        if (wo.getEngineerAdditionalPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getEngineerAdditionalPay());
        }
        if (wo.getAdditionalPay() != null) {
            wo.setLaborCost(wo.getLaborCost() + wo.getAdditionalPay());
        }
        if (wo.getDerateAmount() != null) {
            wo.setLaborCost(wo.getLaborCost() - wo.getDerateAmount());
        }
        if (wo.getLaborCost() < 0) {
            wo.setLaborCost(0L);
        }
        //路途时间：工程师点击“出发” 到点击“到店”的时间
        wo.setTravelTime(DateTimeUtil.calcDateTime(wo.getDepartureTime(), wo.getActualArriveTime()));
        //维修时长：工程师点击“到店”到发送“维修报告”的时间
        wo.setFixTime(DateTimeUtil.calcDateTime(wo.getActualArriveTime(), wo.getSendReportTime()));
        return wo;
    }

    /**
     * 关闭维修工单
     *
     * @param cod
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2024/1/8 11:15
     */
    public boolean closeOrder(CloseOrderDto cod) {
        WorkOrder workOrder = this.workOrderDomainService.getById(cod.getId());
        boolean needPay = cod.getNeedPay();
        if (needPay) {
            Customer customer = customerDomainService.getById(workOrder.getCustomerId());
            RepairPrice p = this.repairPriceServiceDomain.queryByProductId(workOrder.getProductId());
            if (Objects.equals(WorkOrderStatus.COMPLETED, workOrder.getStatus())) {
                throw new MaginaException("工单已完成，不可关闭");
            }
            Long repairPay = Optional.ofNullable(cod.getRepairPay()).orElse(0L);
            Long visitPay = Optional.ofNullable(cod.getVisitPay()).orElse(0L);
            // 总金额=上门费+维修费-会员减免-工程师减免（+工程师加价）+ 客户追加报酬+远程上门费(如果工程师出发了) +（如果填写了维修报告还要加上耗材费+零件更换费）
            Long visitPrice = Optional.ofNullable(workOrder.getLongWayVisitPay()).orElse(0L);
            // 耗材费+换件费(只有产生了维修报告才会有费用)
            Long itemReplacePay = this.replaceOrderServiceDomain.getReplaceAndItemPay(workOrder, p.getReplacePay());
            Long totalPay = repairPay + visitPay
                    + Optional.ofNullable(workOrder.getAdditionalPay()).orElse(0L)
                    + Optional.ofNullable(workOrder.getEngineerAdditionalPay()).orElse(0L)
                    - Optional.ofNullable(workOrder.getDerateAmount()).orElse(0L)
                    + visitPrice + itemReplacePay;
            // 会员减免
            totalPay = this.calcDiscount(customer, p, totalPay);
            this.workOrderDomainService.lambdaUpdate()
                    .set(WorkOrder::getStatus, WorkOrderStatus.TO_BE_SETTLED)
                    .set(WorkOrder::getRepairPay, repairPay)
                    .set(WorkOrder::getVisitPay, visitPay)
                    // 上门费+维修费
                    .set(WorkOrder::getTotalPay, totalPay)
                    .set(WorkOrder::getCurrentProcess, null)
                    .eq(WorkOrder::getId, workOrder.getId())
                    .update();
        } else {
            this.workOrderDomainService.lambdaUpdate()
                    .set(WorkOrder::getStatus, WorkOrderStatus.CLOSE)
                    .set(WorkOrder::getCurrentProcess, null)
                    .eq(WorkOrder::getId, workOrder.getId())
                    .update();
        }
        // 修改设备状态
        this.customerDeviceGroupDomainService.updateDeviceStatus(workOrder.getDeviceGroupId(), CustomerDeviceGroup.DEVICE_STATUS_NORMAL);
        return true;
    }

    /**
     * 根据输入的维修费/上门费计算应付费用
     *
     * @param cod
     * @return: {@link String}
     * @Author: xhg
     * @Date: 2024/1/24 17:48
     */
    public String calcPay(CloseOrderDto cod) {
        WorkOrder workOrder = this.workOrderDomainService.getById(cod.getId());
        Customer customer = customerDomainService.getById(workOrder.getCustomerId());
        RepairPrice p = this.repairPriceServiceDomain.queryByProductId(workOrder.getProductId());
        Long repairPay = Optional.ofNullable(cod.getRepairPay()).orElse(0L);
        Long visitPay = Optional.ofNullable(cod.getVisitPay()).orElse(0L);
        // 总金额=上门费+维修费-会员减免-工程师减免（+工程师加价）+ 客户追加报酬+远程上门费(如果工程师出发了) +（如果填写了维修报告还要加上耗材费+零件更换费）
        Long visitPrice = Optional.ofNullable(workOrder.getLongWayVisitPay()).orElse(0L);
        // 耗材费+换件费(只有产生了维修报告才会有费用)
        Long itemReplacePay = this.replaceOrderServiceDomain.getReplaceAndItemPay(workOrder, p.getReplacePay());
        Long totalPay = repairPay + visitPay
                + Optional.ofNullable(workOrder.getAdditionalPay()).orElse(0L)
                + Optional.ofNullable(workOrder.getEngineerAdditionalPay()).orElse(0L)
                - Optional.ofNullable(workOrder.getDerateAmount()).orElse(0L)
                + visitPrice + itemReplacePay;
        // 会员减免
        totalPay = this.calcDiscount(customer, p, totalPay);
        return new BigDecimal(totalPay).divide(BigDecimal.TEN.pow(2), 2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 获取设备组对应的计数器和印量
     *
     * @param deviceGroupId
     * @return: {@link CounterVo}
     * @Author: xhg
     * @Date: 2024/1/15 18:09
     */
    public CounterVo getCounter(Long deviceGroupId) {
        CustomerDeviceGroup cdg = this.customerDeviceGroupDomainService.getById(deviceGroupId);
        // 对应设备组的设备
        ProductDevice pdd = this.productDeviceDomainService.lambdaQuery()
                .eq(ProductDevice::getProductId, cdg.getProductId())
                .one();
        CounterVo vo = new CounterVo();
        // 当前设备组最新计数器
        IotCounter counter = iotCounterServiceDomain.getLatestCounter(deviceGroupId);
        Integer currentColorCount = Optional.ofNullable(counter).map(IotCounter::getCyanCounter).orElse(0);
        Integer currentBlackWhiteCount = Optional.ofNullable(counter).map(IotCounter::getBlackWhiteCounter).orElse(0);
        vo.setColorCount(currentColorCount);
        vo.setBlackWhiteCount(currentBlackWhiteCount);
        // 除了黑白就是彩机（包括没找到对应设备的情况）
        boolean isBlack = Objects.nonNull(pdd) && Objects.equals(ProductDevice.WHITE_BLACK_COLOR, pdd.getColor().getValue());
        vo.setIsBlack(isBlack);
        // 上次维修的维修报告[只获取已完成的工单的维修报告]的计数器
        RepairReport lastRepairReport = this.repairReportDomainService.selectJoinOne(RepairReport.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(RepairReport.class)
                        .innerJoin(WorkOrder.class, WorkOrder::getId, RepairReport::getWorkOrderId)
                        .eq(RepairReport::getDeviceGroupId, deviceGroupId)
                        .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED)
                        .orderByDesc(RepairReport::getFinishTime)
                        .last(" LIMIT 1")
        );
        Integer lastPrintCount = Objects.isNull(lastRepairReport) ?
                0 : Optional.ofNullable(lastRepairReport.getPrintCount()).orElse(0);
        if (isBlack) {
            // 如果是黑白色，上次维修后到目前印量=(当前黑白 - 上次维修印量)
            vo.setPrintCount(currentBlackWhiteCount - lastPrintCount);
        } else {
            // 如果是彩色，上次维修后印量=(当前黑白 + 当前彩色 - 上次维修印量)
            vo.setPrintCount(currentBlackWhiteCount + currentColorCount - lastPrintCount);
        }
        return vo;
    }

    /**
     * 根据输入的彩色/黑白计数器计算更换后印量
     *
     * @param vo
     * @return: {@link Long}
     * @Author: xhg
     * @Date: 2024/1/18 20:20
     */
    public Integer calcPrintCount(CounterCalcVo vo) {
        // 计算上次更换后印量
        CustomerDeviceGroup cdg = this.customerDeviceGroupDomainService.getById(vo.getDeviceGroupId());
        // 对应设备组的设备
        ProductDevice pdd = this.productDeviceDomainService.lambdaQuery()
                .eq(ProductDevice::getProductId, cdg.getProductId())
                .one();
        // 当前设备组最新计数器
        IotCounter counter = iotCounterServiceDomain.getLatestCounter(vo.getDeviceGroupId());
        Integer currentColorCount = Optional.ofNullable(counter).map(IotCounter::getCyanCounter).orElse(0);
        Integer currentFiveColorCount = Optional.ofNullable(counter).map(IotCounter::getFifthCounter).orElse(0);
        Integer currentBlackWhiteCount = Optional.ofNullable(counter).map(IotCounter::getBlackWhiteCounter).orElse(0);
        // 输入了值则使用输入值
        if (Objects.nonNull(vo.getColorCount()) && vo.getColorCount() > 0) {
            currentColorCount = vo.getColorCount();
        }
        if (Objects.nonNull(vo.getBlackWhiteCount()) && vo.getBlackWhiteCount() > 0) {
            currentBlackWhiteCount = vo.getBlackWhiteCount();
        }
        if (Objects.nonNull(vo.getFiveColorCount()) && vo.getFiveColorCount() > 0) {
            currentFiveColorCount = vo.getFiveColorCount();
        }
        // 除了黑白就是彩机（包括没找到对应设备的情况）
        boolean isBlack = Objects.nonNull(pdd) && Objects.equals(ProductDevice.WHITE_BLACK_COLOR, pdd.getColor().getValue());
        boolean isFive = Objects.nonNull(pdd) && Objects.equals(ProductDevice.FIVE_COLOUR, pdd.getColor().getValue());

        // 上次维修的维修报告[只获取已完成的工单的维修报告]的计数器
        RepairReport lastRepairReport = this.repairReportDomainService.selectJoinOne(RepairReport.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(RepairReport.class)
                        .innerJoin(WorkOrder.class, WorkOrder::getId, RepairReport::getWorkOrderId)
                        .eq(RepairReport::getDeviceGroupId, vo.getDeviceGroupId())
                        .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED)
                        .orderByDesc(RepairReport::getFinishTime)
                        .last(" LIMIT 1")
        );
        if (lastRepairReport == null) {
            return 0;
        }
        Integer lastBlackCount = Objects.isNull(lastRepairReport) ?
                0 : Optional.ofNullable(lastRepairReport.getBlackWhiteCount()).orElse(0);
        Integer lastColorCount = Objects.isNull(lastRepairReport) ?
                0 : Optional.ofNullable(lastRepairReport.getColorCount()).orElse(0);
        Integer lastFiveColorCount = Objects.isNull(lastRepairReport) ?
                0 : Optional.ofNullable(lastRepairReport.getFiveColourCount()).orElse(0);
        if (isBlack) {
            // 如果是黑白色，上次维修后到目前印量=(当前黑白 - 上次维修计数器)
            return currentBlackWhiteCount - lastBlackCount;
        } else if (!isFive) {
            // 如果是彩色，上次维修后印量=(当前黑白 + 当前彩色 - 上次维修计数器)
            return currentBlackWhiteCount + currentColorCount - lastBlackCount - lastColorCount;
        } else {
            // 如果是彩色，上次维修后印量=(当前黑白 + 当前彩色 - 上次维修计数器)
            return currentBlackWhiteCount + currentColorCount + currentFiveColorCount - lastBlackCount - lastColorCount - lastFiveColorCount;
        }
    }

    public Boolean calcPrintCount(Long deviceGroupId) {
        List<RepairReport> repairReports = this.repairReportDomainService.lambdaQuery()
                .eq(deviceGroupId != null, RepairReport::getDeviceGroupId, deviceGroupId)
                .orderByAsc(RepairReport::getCreatedAt).list();
        Map<Long, List<RepairReport>> repairReportMap = repairReports.stream().collect(Collectors.groupingBy(RepairReport::getDeviceGroupId));
        for (Long groupId : repairReportMap.keySet()) {
            List<RepairReport> repairReportList = repairReportMap.get(groupId);
            Integer lastBlackCount = 0;
            Integer lastColorCount = 0;
            for (int i = 0; i < repairReportList.size(); i++) {
                RepairReport repairReport = repairReportList.get(i);
                if (i == 0) {
                    repairReport.setPrintCount(0);
                } else {
                    Integer currBlackCount = Optional.ofNullable(repairReport.getBlackWhiteCount()).orElse(0);
                    Integer currColorCount = Optional.ofNullable(repairReport.getColorCount()).orElse(0);
                    Integer printCount = currBlackCount + currColorCount - lastBlackCount - lastColorCount;
                    if (printCount < 0) {
                        printCount = 0;
                    }
                    repairReport.setPrintCount(printCount);
                }
                lastBlackCount = Optional.ofNullable(repairReport.getBlackWhiteCount()).orElse(0);
                lastColorCount = Optional.ofNullable(repairReport.getColorCount()).orElse(0);
                repairReportDomainService.updateById(repairReport);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 判断工单是否确认维修报告超48小时
     *
     * @param wo
     * @return:
     * @Author: xhg
     * @Date: 2024/1/19 11:58
     */
    private void checkIsAppeal(WorkOrder wo) {
        // （申诉中的话已有字段）确认维修报告后时间过了48小时，就不能申诉了（即使工单已经完成了）
        RepairReport report = this.repairReportDomainService.getByWorkOrderId(wo.getId());
        Optional.ofNullable(report).ifPresent(i -> {
            Optional.ofNullable(i.getFinishTime()).ifPresent(f -> {
                if (f.plusHours(Appeal.APPEAL_EXPIRE).compareTo(LocalDateTime.now()) < 0) {
                    wo.setIsAppeal(Boolean.TRUE);
                }
            });
        });
    }


    /***
     * 获取 单个客户的工单材料消费总额
     * @param customerId
     * @return
     */
    public BigDecimal getSumConsumableNum(Long customerId) {
        return this.workOrderDomainService.getSumConsumableNum(customerId);
    }


    /**
     * 根据客户ID 获取人工消费总金额
     */
    public BigDecimal getManualWorkNum(Long customerId) {
        return this.workOrderDomainService.getManualWorkNum(customerId);
    }


    public Long getManualWorkNumCount(Long customerId) {
        return workOrderDomainService.lambdaQuery()
                .eq(WorkOrder::getCustomerId, customerId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED.getCode())
                .count();
    }


    public WorkOrder getLastWorkOrderCountByCustomerId(Long customerId) {
        return workOrderDomainService.lambdaQuery()
                .eq(WorkOrder::getCustomerId, customerId)
                .eq(WorkOrder::getStatus, WorkOrderStatus.COMPLETED.getCode())
                .last("limit 1").one();
    }

    public Boolean completeWorkOrder(WorkOrder workOrder) {

        return this.workOrderDomainService.updateById(workOrder);
    }


    public DataGrid<MonthRepairVo> getMonthRepairList(WorkOrderPcPageQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getMonthRepairList(query)).peek((p) -> {
            if (p.getRepairTime() > 0) {
                p.setRepairTimeStr(convertSecondsToHMS(p.getRepairTime()));
            }
            if (p.getAvgRepairTime() > 0) {
                p.setAvgRepairTimeStr(convertSecondsToHMS(p.getAvgRepairTime()));
            }
        });
    }

    public MonthRepairVo getMonthRepairSummary(WorkOrderPcPageQuery query) {
        MonthRepairVo monthRepairVo = workOrderDomainService.getMonthRepairSummary(query);
        if (monthRepairVo.getRepairTime() != null && monthRepairVo.getRepairTime() > 0) {
            monthRepairVo.setRepairTimeStr(convertSecondsToHMS(monthRepairVo.getRepairTime()));
        }
        return monthRepairVo;
    }


    private String convertSecondsToHMS(Long totalSeconds) {
        Long hours = totalSeconds / 3600;
        Long minutes = (totalSeconds % 3600) / 60;
        Long seconds = totalSeconds % 60;
        return String.format("%d小时%d分%d秒", hours, minutes, seconds);
    }

    public DataGrid<MonthRepairVo> getMonthRepairBrandList(WorkOrderPcPageQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getMonthRepairBrandList(query));
    }

    public MonthRepairVo getMonthRepairBrandSummary(WorkOrderPcPageQuery query) {
        MonthRepairVo monthRepairVo = workOrderDomainService.getMonthRepairBrandSummary(query);
        return monthRepairVo;
    }

    public DataGrid<MonthRepairVo> getMonthRepairDistributionList(WorkOrderPcPageQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getMonthRepairDistributionList(query));
    }

    public MonthRepairVo getMonthRepairDistributionSummary(WorkOrderPcPageQuery query) {
        MonthRepairVo monthRepairVo = workOrderDomainService.getMonthRepairDistributionSummary(query);
        return monthRepairVo;
    }

    public DataGrid<MonthRepairVo> getMonthRepairTreatyList(WorkOrderPcPageQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getMonthRepairTreatyList(query));
    }

    public DataGrid<MonthRepairVo> getRepairBrandList(WorkOrderPcPageQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getRepairBrandList(query));
    }

    public DataGrid<MonthProblemRepairVo> getMonthRepairProblemList(WorkOrderProblemQuery query) {
        return PageHelper.startPage(query, page -> this.workOrderDomainService.getMonthRepairProblemList(query));
    }

}
