package com.hightop.benyin.items.store.api.controller;

import cn.hutool.json.JSONObject;
import com.google.common.collect.Lists;
import com.hightop.benyin.customer.infrastructure.util.CustomerMpUtils;
import com.hightop.benyin.item.application.service.ShopService;
import com.hightop.benyin.item.application.vo.shop.ShopQueryItemVo;
import com.hightop.benyin.item.infrastructure.entity.ItemCategory;
import com.hightop.benyin.items.store.api.params.CustomerItemStoreAddParam;
import com.hightop.benyin.items.store.api.params.ItemStoreQueryParam;
import com.hightop.benyin.items.store.application.service.ItemStoreService;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStoreLog;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import com.hightop.benyin.items.store.infrastructure.enums.UserTypeEnum;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/6 10:04
 */
@RestController
@RequestMapping("/customerItemStore")
@Api(tags = "客户耗材仓库")
public class CustomerItemStoreController {
    @Autowired
    private ItemStoreService itemStoreService;

    @Autowired
    private ShopService shopService;

    @PostMapping("/pagePc")
    @ApiOperation("耗材仓库分页列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ItemStore>> pagePc(@RequestBody ItemStoreQueryParam pageQuery) {
        return RestResponse.ok(this.itemStoreService.pcPage(pageQuery));
    }

    @PostMapping("/export")
    @ApiOperation("耗材仓库导出")
    @IgnoreOperationLog
    public RestResponse<Void> exportItemStore(HttpServletResponse response, @RequestBody ItemStoreQueryParam pageQuery) {
        // 仅导出当前客户仓库数据
        pageQuery.setUserType(UserTypeEnum.CUSTOMER);
        Boolean b = this.itemStoreService.exportItemStore(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @PostMapping("/page")
    @ApiOperation("耗材仓库分页列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ItemStore>> page(@RequestBody ItemStoreQueryParam pageQuery) {
        pageQuery.setHasInventory(true);
        return RestResponse.ok(this.itemStoreService.pcPage(pageQuery));
    }

    @PostMapping("/total")
    @ApiOperation("金额统计")
    @IgnoreOperationLog
    public RestResponse<BigDecimal> total(@RequestBody ItemStoreQueryParam pageQuery) {
        BigDecimal total = this.itemStoreService.total(pageQuery);
        // 防止空指针异常：当查询结果为空时，SUM()函数返回null
        if (total == null) {
            total = BigDecimal.ZERO;
        }
        total = total.setScale(2, BigDecimal.ROUND_DOWN);
        return RestResponse.ok(total);
    }


    @PostMapping("/logPageList")
    @ApiOperation("客户耗材仓库入库日志列表")
    public RestResponse<DataGrid<ItemStoreLog>> queryLogPageList(@RequestBody ItemStoreQueryParam query) {
        if(StringUtils.isEmpty(query.getOperateType())){
            query.setOperateTypes(Lists.newArrayList(OperateTypeEnum.APPLY.getValue(),
                    OperateTypeEnum.MALL.getValue(),OperateTypeEnum.PURCHASE.getValue()));
        }
        return RestResponse.ok(this.itemStoreService.queryLogPageList(query));
    }

    @GetMapping("/totalMoney/{customerId}")
    @ApiOperation("耗材仓库金额统计")
    public RestResponse<JSONObject> totalMoney(@PathVariable("customerId") Long customerId) {
        return RestResponse.ok(itemStoreService.totalMoney(customerId));
    }

    @GetMapping("/modelConditions")
    @ApiOperation("耗材仓库获取筛选数据（带品牌+机型）")
    public RestResponse<ShopQueryItemVo> modelConditions() {
        return RestResponse.ok(shopService.modelConditions(ItemCategory.DEFAULT_CATEGORY_ID, null));
    }

    @PostMapping("/list")
    @ApiOperation("耗材仓库列表")
    @IgnoreOperationLog
    public RestResponse<List<ItemStore>> list(@RequestBody ItemStoreQueryParam queryParam) {
        queryParam.setCustomerId(CustomerMpUtils.requiredCustomerId());
        return RestResponse.ok(
                itemStoreService.queryList(queryParam, UserTypeEnum.CUSTOMER, CustomerMpUtils.requiredCustomerId()));
    }

    @PostMapping("/add")
    @ApiOperation("客户登记耗材")
    public RestResponse<Void> add(@RequestBody CustomerItemStoreAddParam itemStoreAddParam) {
        return Operation.ADD.response(
                this.itemStoreService.customerItemStoreAdd(itemStoreAddParam, CustomerMpUtils.requiredCustomerId())
        );

    }
}
