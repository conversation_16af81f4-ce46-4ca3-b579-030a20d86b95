package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 日志配置DTO
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@ApiModel("日志配置")
public class LogConfigDto {

    @ApiModelProperty("配置ID")
    private Long id;

    @ApiModelProperty(value = "配置名称", required = true)
    @NotBlank(message = "配置名称不能为空")
    private String configName;

    @ApiModelProperty(value = "日志级别", required = true)
    @NotBlank(message = "日志级别不能为空")
    private String logLevel;

    @ApiModelProperty(value = "是否启用位置日志", required = true)
    @NotNull(message = "是否启用位置日志不能为空")
    private Boolean enableLocationLog;

    @ApiModelProperty(value = "位置日志间隔(秒)", required = true)
    @NotNull(message = "位置日志间隔不能为空")
    @Positive(message = "位置日志间隔必须大于0")
    private Integer locationLogInterval;

    @ApiModelProperty(value = "上传间隔(秒)", required = true)
    @NotNull(message = "上传间隔不能为空")
    @Positive(message = "上传间隔必须大于0")
    private Integer logUploadInterval;

    @ApiModelProperty(value = "最大日志文件数量", required = true)
    @NotNull(message = "最大日志文件数量不能为空")
    @Positive(message = "最大日志文件数量必须大于0")
    private Integer maxLogFiles;

    @ApiModelProperty(value = "配置版本（可选，为空时自动生成）", required = false)
    private String configVersion;

    @ApiModelProperty("是否激活")
    private Boolean isActive;
}
