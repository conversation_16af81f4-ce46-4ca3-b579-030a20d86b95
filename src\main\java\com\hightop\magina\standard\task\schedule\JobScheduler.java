package com.hightop.magina.standard.task.schedule;


import com.hightop.benyin.statistics.domain.service.PrintCostPartServiceDomain;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.FarioThreadFactory;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.standard.task.cron.CronExpression;
import com.hightop.magina.standard.task.cron.CronExpressionUtil;
import com.hightop.magina.standard.task.job.Job;
import com.hightop.magina.standard.task.job.JobExpireStrategy;
import com.hightop.magina.standard.task.lock.JobLocker;
import com.hightop.magina.standard.task.log.JobLog;
import com.hightop.magina.standard.task.log.JobState;
import com.hightop.magina.standard.task.log.TriggerType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Component
public class JobScheduler {
    private static final Logger log = LoggerFactory.getLogger(JobScheduler.class);
    private final ScheduleJobDomainService scheduleJobDomainService;
    private final ScheduleJobLogDomainService scheduleJobLogDomainService;
    private final PrintCostPartServiceDomain printCostPartServiceDomain;
    private final JobExecutor jobExecutor;
    private volatile boolean isSchedulerStop = false;
    private volatile boolean isAlloterStop = false;
    private final ThreadPoolExecutor scheduler;
    private final ThreadPoolExecutor alloter;
    private static final Map<String, CronExpression> CRONS = new ConcurrentHashMap();

    void start() {
        this.startScheduler();
        this.startAlloter();
    }

    void stopScheduler() {
        this.isSchedulerStop = true;
        this.scheduler.shutdown();
    }

    void stopAlloter() {
        this.isAlloterStop = true;
        this.alloter.shutdown();
    }

    @PreDestroy
    void destroy() {
        if (log.isInfoEnabled()) {
            log.info("正在关闭任务调度器...");
        }

        this.stopScheduler();
        if (log.isInfoEnabled()) {
            log.info("正在关闭任务分配器...");
        }

        this.stopAlloter();
        CRONS.clear();
        this.jobExecutor.stop();
    }

    private void startScheduler() {
        ExecutorUtils.run(this.scheduler, () -> {
            try {
                TimeUnit.MILLISECONDS.sleep(1000L - System.currentTimeMillis() % 1000L);
            } catch (InterruptedException var6) {
            }

            while(!this.isSchedulerStop) {
                LocalDateTime now = LocalDateTime.now();
                JobLocker.execute(() -> {
                    List<Job> jobs = this.scheduleJobDomainService.getScheduledJobs(now.plusSeconds(5L));
                    if (CollectionUtils.isNotEmpty(jobs)) {
                        List<Job> updated = new ArrayList();
                        List<JobLog> inserted = new ArrayList();
                        jobs.forEach((it) -> {
                            boolean needAllot = false;
                            if (it.getNext().plusSeconds(1L).isBefore(now)) {
                                it.setNext(getNext(it.getCron(), now));
                                if (it.getExpireStrategy() == JobExpireStrategy.EXECUTE_ONCE) {
                                    needAllot = true;
                                    it.setPrevious(now);
                                }
                            } else {
                                it.setPrevious(it.getNext());
                                it.setNext(getNext(it.getCron(), it.getPrevious()));
                                needAllot = true;
                            }

                            updated.add(it);
                            if (needAllot) {
                                inserted.add((new JobLog()).setJobId(it.getId()).setJobVersion(it.getUpdatedAt()).setTriggerType(TriggerType.SCHEDULED).setScheduledAt(now).setRetry(it.getRetry()).setTimeout(it.getTimeout()).setTriggeredAt(it.getPrevious()).setState(JobState.PENDING));
                            }

                        });
                        this.scheduleJobDomainService.updateBatchById(updated);
                        if (CollectionUtils.isNotEmpty(inserted)) {
                            this.scheduleJobLogDomainService.saveBatch(inserted);
                        }
                    }

                });
                long l = Duration.between(now, LocalDateTime.now()).toMillis();
                if (l < 1000L) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(1000L - System.currentTimeMillis() % 1000L);
                    } catch (InterruptedException var5) {
                    }
                }
            }

        });
    }

    private void startAlloter() {
        ExecutorUtils.run(this.alloter, () -> {
            while(!this.isAlloterStop) {
                LocalDateTime now = LocalDateTime.now();
                //List<JobLog> logs = this.scheduleJobLogDomainService.getAllotedLogs(now);
                List<JobLog> logs = printCostPartServiceDomain.getAllotedLogs(now);
                if (CollectionUtils.isNotEmpty(logs)) {
                    JobExecutor var10001 = this.jobExecutor;
                    logs.forEach(var10001::execute);
                }

                long l = Duration.between(now, LocalDateTime.now()).toMillis();
                if (l < 1000L) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(1000L - l);
                    } catch (InterruptedException var6) {
                    }
                }
            }

        });
    }

    private static LocalDateTime getNext(String cron, LocalDateTime now) {
        return ((CronExpression)CRONS.computeIfAbsent(cron, CronExpressionUtil::parse)).getNextValidTimeAfter(now);
    }

    public JobScheduler(ScheduleJobDomainService scheduleJobDomainService, ScheduleJobLogDomainService scheduleJobLogDomainService, JobExecutor jobExecutor, PrintCostPartServiceDomain printCostPartServiceDomain) {
        this.scheduler = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue(), FarioThreadFactory.ofDaemon("Job-Scheduler"));
        this.alloter = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue(), FarioThreadFactory.ofDaemon("Job-Alloter"));
        this.scheduleJobDomainService = scheduleJobDomainService;
        this.scheduleJobLogDomainService = scheduleJobLogDomainService;
        this.printCostPartServiceDomain = printCostPartServiceDomain;
        this.jobExecutor = jobExecutor;
    }
}
