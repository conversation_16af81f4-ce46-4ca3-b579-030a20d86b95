package com.hightop.benyin.logcontrol.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import com.hightop.benyin.logcontrol.domain.repository.ConfigDistributionRepository;
import com.hightop.benyin.logcontrol.infrastructure.mapper.ConfigDistributionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 配置分发关系Repository实现类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Repository
public class ConfigDistributionRepositoryImpl implements ConfigDistributionRepository {

    @Autowired
    private ConfigDistributionMapper configDistributionMapper;

    @Override
    public boolean save(ConfigDistribution distribution) {
        try {
            return configDistributionMapper.insert(distribution) > 0;
        } catch (Exception e) {
            log.error("保存配置分发关系失败", e);
            return false;
        }
    }

    @Override
    public boolean update(ConfigDistribution distribution) {
        try {
            return configDistributionMapper.updateById(distribution) > 0;
        } catch (Exception e) {
            log.error("更新配置分发关系失败", e);
            return false;
        }
    }

    @Override
    public List<ConfigDistribution> findActiveByTarget(String targetType, String targetId) {
        return configDistributionMapper.findActiveByTarget(targetType, targetId);
    }

    @Override
    public List<ConfigDistribution> findDistributionsWithStatus(String targetType, String keyword) {
        return configDistributionMapper.findDistributionsWithStatus(targetType, keyword);
    }

    @Override
    public boolean existsByConfigAndTarget(Long configId, String targetType, String targetId) {
        return configDistributionMapper.existsByConfigAndTarget(configId, targetType, targetId);
    }

    @Override
    public List<ConfigDistribution> findByTargetTypeAndDeleted(String targetType, Boolean deleted) {
        LambdaQueryWrapper<ConfigDistribution> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(targetType)) {
            wrapper.eq(ConfigDistribution::getTargetType, targetType);
        }
        wrapper.eq(ConfigDistribution::getDeleted, deleted);
        return configDistributionMapper.selectList(wrapper);
    }

    @Override
    public List<ConfigDistribution> findByDeleted(Boolean deleted) {
        LambdaQueryWrapper<ConfigDistribution> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfigDistribution::getDeleted, deleted);
        return configDistributionMapper.selectList(wrapper);
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            // 使用逻辑删除而非物理删除
            ConfigDistribution distribution = new ConfigDistribution();
            distribution.setId(id);
            distribution.setDeleted(true);
            return configDistributionMapper.updateById(distribution) > 0;
        } catch (Exception e) {
            log.error("删除配置分发关系失败，ID: {}", id, e);
            return false;
        }
    }
}
