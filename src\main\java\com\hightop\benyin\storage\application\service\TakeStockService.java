package com.hightop.benyin.storage.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.hightop.benyin.machine.domain.service.MachineDomainService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.product.domain.dto.ProductTreeDto;
import com.hightop.benyin.product.domain.service.ProductAccessoryDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductAccessory;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.RedisLockUtils;
import com.hightop.benyin.storage.api.dto.TakeStockApproveDto;
import com.hightop.benyin.storage.api.dto.excel.TakeDetailExcel;
import com.hightop.benyin.storage.api.dto.query.TakeDiscrepancyApproveDto;
import com.hightop.benyin.storage.api.dto.query.TakeStockDto;
import com.hightop.benyin.storage.api.dto.query.TakeStockQuery;
import com.hightop.benyin.storage.application.vo.TakeDetailStatistics;
import com.hightop.benyin.storage.application.vo.TakeDetailTotalVo;
import com.hightop.benyin.storage.application.vo.TakeDiscrepancyTotalVo;
import com.hightop.benyin.storage.domain.service.*;
import com.hightop.benyin.storage.domain.vo.BatchPriceNum;
import com.hightop.benyin.storage.infrastructure.entity.*;
import com.hightop.benyin.storage.infrastructure.enums.*;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 库存盘点服务
 *
 * <AUTHOR>
 * @date 2023-10-30 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class TakeStockService {
    TakeStockDomainService takeStockDomainService;
    TakeDetailDomainService takeDetailDomainService;
    StorageInventoryServiceDomain storageInventoryServiceDomain;
    SequenceDomainService sequenceDomainService;
    WarehouseServiceDomain warehouseServiceDomain;
    TakeDiscrepancyDomainService takeDiscrepancyDomainService;
    ProductTreeDomainService productTreeDomainService;
    StorageInWarehouseServiceDomain storageInWarehouseServiceDomain;
    StorageInWarehouseGoodsServiceDomain storageInWarehouseGoodsServiceDomain;
    StorageOutWarehouseServiceDomain storageOutWarehouseServiceDomain;
    StorageOutWarehouseGoodsServiceDomain storageOutWarehouseGoodsServiceDomain;
    MachineDomainService machineDomainService;
    ProductAccessoryDomainService productAccessoryDomainService;
    StorageArticleServiceDomain storageArticleServiceDomain;
    StorageWarehouseFlowServiceDomain storageWarehouseFlowServiceDomain;
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;

    /**
     * 库存盘点分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<TakeStock> page(TakeStockQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.takeStockDomainService.page(pageQuery)
        );
    }

    /**
     * 机器盘点明细保存
     *
     * @param takeDetail {@link TakeDetail}
     * @return true/false
     */
    public Long stash(TakeDetail takeDetail) {
        if (takeDetail.getTakeStockId() == null) {
            TakeStock takeStock = new TakeStock();
            takeStock.setWarehouseId(0L);
            takeStock.setStockType(1);
            takeStock.setStockStatus(StockStatus.STASH);
            takeStock.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            takeStock.setCode(sequenceDomainService.nextDateSequence(TakeStock.SEQ_PREFIX, 4));
            takeStockDomainService.save(takeStock);
            takeDetail.setTakeStockId(takeStock.getId());
        } else {
            if (takeDetail.getId() == null) {
                TakeDetail dataDetail = takeDetailDomainService.lambdaQuery()
                        .eq(TakeDetail::getArticleCode, takeDetail.getArticleCode())
                        .eq(TakeDetail::getTakeStockId, takeDetail.getTakeStockId()).one();
                if (dataDetail != null) {
                    takeDetail.setId(dataDetail.getId());
                }
            }
        }

        if (takeDetail.getHostType() == null) {
            throw new MaginaException("请选择主机类型！");
        }

        if (takeDetail.getStockNum() == null) {
            throw new MaginaException("请选择是否在库！");
        }
        if (takeDetail.getProductId() == null) {
            throw new MaginaException("请选择机器型号！");
        }

        Machine machine = machineDomainService.getMachineByCode(takeDetail.getArticleCode());
        if (machine.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
            ProductTree productTree = productTreeDomainService.getById(takeDetail.getProductId());
            takeDetail.setArticleName(productTree.getName());
            takeDetail.setFullIdPath(productTree.getFullIdPath());
        } else {
            ProductAccessory productAccessory = productAccessoryDomainService.getById(takeDetail.getProductId());
            takeDetail.setArticleName(productAccessory.getModeType());
        }
        takeDetail.setInventoryNum(1);
        takeDetail.setStockAmount(0L);
        if(machine.getPurchasePrice()!= null ){
            takeDetail.setPrice(machine.getPurchasePrice());
            if(takeDetail.getStockNum()!=null && takeDetail.getStockNum() >0){
                takeDetail.setStockAmount(machine.getPurchasePrice());
            }
        }

        takeDetailDomainService.saveOrUpdate(takeDetail);
        return takeDetail.getTakeStockId();
    }

    /**
     * 库存盘点添加
     *
     * @param takeStock {@link TakeStock}
     * @return true/false
     */
    public Long saveOrUpdate(TakeStock takeStock) {

        if (takeStock.getId() == null) {
            this.build(takeStock);
        }

        String lockKey = "LOCK:" + takeStock.getCode();
        Boolean lock = RedisLockUtils.lock(lockKey,takeStock.getCode(), 3);
        if (!lock) {
            throw new MaginaException("当前盘点单正在保存数据，请稍候重试！");
        }

        List<TakeDetail> takeDetails = takeStock.getTakeDetails().stream().filter(v -> v.getStockNum() != null).map(item->{
            item.setInventoryNum(item.getNum());
            item.setPrice(item.getInPrice());
            return item;
        }).collect(Collectors.toList());
        List<TakeDetail> dataList = takeDetailDomainService.lambdaQuery()
                .eq(TakeDetail::getTakeStockId, takeStock.getId())
                .list();
        if (CollectionUtils.isNotEmpty(dataList)) {
            //Map<String, TakeDetail> map = dataList.stream().collect(Collectors.toMap(TakeDetail::getArticleCode, v -> v));
            Map<String, TakeDetail> map = dataList.stream().collect(Collectors.toMap(v -> v.getArticleCode() + "_" + v.getBatchCode(), Function.identity(), (oldVal, newVal) -> newVal));
            takeDetails.forEach(v -> {
                String key = v.getArticleCode() + "_" + v.getBatchCode();
                if (map.containsKey(key)) {
                    v.setId(map.get(key).getId());
                    map.remove( key);
                }
            });
            if (MapUtils.isNotEmpty( map)){
                for (Map.Entry<String, TakeDetail> entry : map.entrySet()) {
                    if (StringUtils.isNotBlank(entry.getKey())){
                        takeDetails.add(entry.getValue());
                    }
                }
            }
        }
        takeDetails.forEach(v -> {
            v.setTakeStockId(takeStock.getId());
            v.setStockAmount(0L);
            if(v.getPrice()!= null ){
                v.setStockAmount(v.getPrice()*v.getStockNum());
                v.setInventoryAmount(v.getPrice()*v.getInventoryNum());
            }
        });
        takeDetailDomainService.saveOrUpdateBatch(takeDetails);
        if(CollectionUtils.isEmpty(dataList))
            takeDetails.addAll(dataList);

        takeStock.setStockNum(takeDetails.stream().mapToInt(TakeDetail::getStockNum).sum());
        takeStock.setStockAmount(takeDetails.stream().map(v -> {
            BigDecimal price = v.getPrice() != null ? new BigDecimal(v.getPrice()) : BigDecimal.ZERO;
            BigDecimal num = v.getStockNum() != null ? new BigDecimal(v.getStockNum()) : BigDecimal.ZERO;
            return price.multiply(num);
        }).reduce(BigDecimal.ZERO, BigDecimal::add).longValue());
        TakeStockDto takeStockDto = new TakeStockDto();
        takeStockDto.setWarehouseId(takeStock.getWarehouseId());
        TakeDetailStatistics takeDetailStatistics = this.stockDetailStatistics(takeStockDto);
        takeStock.setInventoryAmount(takeDetailStatistics.getInventoryAmount());
        takeStock.setInventoryNum(takeDetailStatistics.getInventoryNum());
//        takeStock.setInventoryNum(takeDetails.stream().mapToInt(TakeDetail::getInventoryNum).sum());
//        takeStock.setInventoryAmount(takeDetails.stream().map(v -> {
//            BigDecimal price = v.getPrice() != null ? new BigDecimal(v.getPrice()) : BigDecimal.ZERO;
//            BigDecimal num = v.getInventoryNum() != null ? new BigDecimal(v.getInventoryNum()) : BigDecimal.ZERO;
//            return price.multiply(num);
//        }).reduce(BigDecimal.ZERO, BigDecimal::add).longValue());
        takeStockDomainService.updateById(takeStock);
        RedisLockUtils.unlock(lockKey,takeStock.getCode());
        return takeStock.getId();
    }

    private void build(TakeStock takeStock) {
        takeStock.setCode(sequenceDomainService.nextDateSequence(TakeStock.SEQ_PREFIX, 4));
        takeStock.setStockStatus(StockStatus.STASH);
        if (takeStock.getWarehouseId() == null) {
            throw new MaginaException("参数异常，未选择仓库！");
        }
        takeStock.setAuditorBy(null);
        if (takeStock.getStockType().equals(1)) {
            takeStock.setWarehouseId(0L);
        } else {
            Warehouse warehouse = warehouseServiceDomain.getById(takeStock.getWarehouseId());
            takeStock.setType(warehouse.getType());
        }
        takeStock.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        takeStockDomainService.save(takeStock);

    }

    /**
     * 库存盘点添加
     *
     * @param takeStock {@link TakeStock}
     * @return true/false
     */
    public boolean submit(TakeStock takeStock) {
        List<TakeDetail> notSaveList = takeStock.getTakeDetails().stream().filter(v -> v.getStockNum() != null).map(item->{
            item.setInventoryNum(item.getNum());
            item.setPrice(item.getInPrice());
            return item;
        }).collect(Collectors.toList());
        if (takeStock.getId() == null) {
            this.build(takeStock);
        }
        Long id = takeStock.getId();
        if (CollectionUtils.isNotEmpty(notSaveList)) {
            notSaveList.forEach(v -> {
                v.setTakeStockId(id);
                v.setStockAmount(0L);
                if(v.getPrice()!= null ){
                    v.setStockAmount(v.getPrice()*v.getStockNum());
                }
            });
            takeDetailDomainService.saveOrUpdateBatch(notSaveList);
        }
        takeStock = this.takeStockDomainService.getById(id);
        takeStock.setStockStatus(StockStatus.WAIT_APPROVE);
        List<TakeDetail> takeDetails = takeDetailDomainService.lambdaQuery().eq(TakeDetail::getTakeStockId, takeStock.getId()).list();
        if (CollectionUtils.isEmpty(takeDetails)) {
            throw new MaginaException("盘点单明细为空，请检查明细");
        }
        takeStock.setStockNum(takeDetails.stream().mapToInt(TakeDetail::getStockNum).sum());
        takeStock.setStockAmount(takeDetails.stream().map(v -> {
            return new BigDecimal(v.getPrice()).multiply(new BigDecimal(v.getStockNum()));
        }).reduce(BigDecimal.ZERO, BigDecimal::add).longValue());
//        takeStock.setInventoryNum(takeDetails.stream().mapToInt(TakeDetail::getInventoryNum).sum());
//        takeStock.setInventoryAmount(takeDetails.stream().mapToLong(TakeDetail::getInventoryAmount).sum());
        TakeStockDto takeStockDto = new TakeStockDto();
        takeStockDto.setWarehouseId(takeStock.getWarehouseId());
        TakeDetailStatistics takeDetailStatistics = this.stockDetailStatistics(takeStockDto);
        takeStock.setInventoryAmount(takeDetailStatistics.getInventoryAmount());
        takeStock.setInventoryNum(takeDetailStatistics.getInventoryNum());
        takeStock.setAuditorBy(null);
        return this.takeStockDomainService.updateById(takeStock);
    }

    /**
     * 库存盘点修改
     *
     * @param takeStock {@link TakeStock}
     * @return true/false
     */
    public boolean updateById(TakeStock takeStock) {
        TakeStock saveEntity = this.takeStockDomainService.getById(takeStock.getId());
        return this.takeStockDomainService.updateById(saveEntity);
    }


    /**
     * 库存盘点确认
     *
     * @param takeStockApproveDto {@link TakeStock}
     * @return true/false
     */
    public boolean approve(TakeStockApproveDto takeStockApproveDto) {
        TakeStock saveEntity = this.takeStockDomainService.getById(takeStockApproveDto.getId());
        if (takeStockApproveDto.getStockStatus().equals(StockStatus.REJECT)) {
            saveEntity.setStockStatus(StockStatus.STASH);
        }else {
            saveEntity.setStockStatus(takeStockApproveDto.getStockStatus());
        }
        saveEntity.setRecheckAt(LocalDateTime.now());
        saveEntity.setRecheckBy(ApplicationSessions.id());
        this.takeStockDomainService.updateById(saveEntity);
        if (takeStockApproveDto.getStockStatus().equals(StockStatus.REJECT))
            return Boolean.TRUE;
        ExecutorUtils.doAfterCommit(() -> {
            this.discrepancy(saveEntity.getId());
        });
        return Boolean.TRUE;
    }

    /**
     * 库存报损处理
     *
     * @param takeDiscrepancyApproveDto {@link TakeStock}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approveDiscrepancy(TakeDiscrepancyApproveDto takeDiscrepancyApproveDto) {
        TakeStock saveEntity = this.takeStockDomainService.getById(takeDiscrepancyApproveDto.getId());
        saveEntity.setDiscrepancyStatus(takeDiscrepancyApproveDto.getStatus());
        saveEntity.setAuditAt(LocalDateTime.now());
        saveEntity.setAuditorBy(ApplicationSessions.id());

        if (saveEntity.getStockType().equals(0)) {
            //生成出入库单
            List<TakeDiscrepancy> takeDiscrepancies = takeDiscrepancyDomainService.selectJoinList(TakeDiscrepancy.class, MPJWrappers.lambdaJoin()
                    .select(TakeDiscrepancy::getId, TakeDiscrepancy::getTakeDetailId, TakeDiscrepancy::getDiscrepancyNum, TakeDiscrepancy::getDiscrepancyAmount)
                    .select(TakeDiscrepancy::getBatchCode)
                    .selectAs(TakeDiscrepancy::getDiscrepancyStatus, "discrepancyStatus")
                    .selectAs(TakeDetail::getArticleCode, TakeDiscrepancy::getArticleCode)
                    .leftJoin(TakeDetail.class, TakeDetail::getId, TakeDiscrepancy::getTakeDetailId)
                    .eq(TakeDiscrepancy::getTakeStockCode, saveEntity.getCode())

            );
            if (CollectionUtils.isNotEmpty(takeDiscrepancies)) {
                List<TakeDiscrepancy> inStockDiscrepancies = takeDiscrepancies.stream().filter(v -> v.getDiscrepancyStatus().equals(DiscrepancyStatus.OVERFLOW)).collect(Collectors.toList());
                //报溢入库单生成
                if (CollectionUtils.isNotEmpty(inStockDiscrepancies)) {
                    this.saveInWarehouse(inStockDiscrepancies, saveEntity);
                }
                //报损出库单生成
                List<TakeDiscrepancy> outStockDiscrepancies = takeDiscrepancies.stream().filter(v -> v.getDiscrepancyStatus().equals(DiscrepancyStatus.WITHOUT)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outStockDiscrepancies)) {
                    this.saveOutWarehouse(outStockDiscrepancies, saveEntity);
                }
            }
        }

        return this.takeStockDomainService.updateById(saveEntity);
    }

    /**
     * 报损出库单生成
     *
     * @param outStockDiscrepancies 损溢列表
     * @param takeStock             盘点单
     * @return:
     * @Author: xhg
     * @Date: 2024/1/29 15:02
     */
    private void saveInWarehouse(List<TakeDiscrepancy> outStockDiscrepancies, TakeStock takeStock) {
        StorageInWarehouse storageInWarehouse = new StorageInWarehouse();
        String rkCode = this.sequenceDomainService.nextDateSequence(StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
        storageInWarehouse.setInWarehouseId(rkCode);
        storageInWarehouse.setInType(InOutTypeEnum.OVERFLOW_IN);
        storageInWarehouse.setWarehouseId(takeStock.getWarehouseId());
        storageInWarehouse.setShopWaybill(takeStock.getCode());
        storageInWarehouse.setRemarks(InOutTypeEnum.RETURN_GOODS.getName());
        storageInWarehouse.setInStatus(InStatusEnum.DRK);
        storageInWarehouse.setAuditInWarehouseNumber(0);
        int num = 0;
        List<StorageInWarehouseGoods> storageInWarehouseGoods = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        Long operatorId = ApplicationSessions.id();
        List<TakeDiscrepancy> outTakeDiscrepancyList  = new ArrayList<>();
        Map<String, List<TakeDiscrepancy>> list = outStockDiscrepancies.stream().collect(Collectors.groupingBy(TakeDiscrepancy::getArticleCode));
        //把同物品的合成一个
        list.forEach((k, v) -> {
            TakeDiscrepancy takeDiscrepancy = new TakeDiscrepancy();
            takeDiscrepancy.setDiscrepancyNum(v.stream().mapToInt(TakeDiscrepancy::getDiscrepancyNum).sum());
            takeDiscrepancy.setArticleCode(k);
            outTakeDiscrepancyList.add(takeDiscrepancy);
        });
        for (TakeDiscrepancy rod : outTakeDiscrepancyList) {
            StorageInventory inv = this.storageInventoryServiceDomain.lambdaQuery()
                    .eq(StorageInventory::getWarehouseId, takeStock.getWarehouseId())
                    .eq(StorageInventory::getCode, rod.getArticleCode()).one();

            StorageInWarehouseGoods in = new StorageInWarehouseGoods();
            in.setInWarehouseId(rkCode);
            String batchCode = takeStock.getCode().replace("PD", "OF");
            in.setBatchCode(batchCode);
            in.setCode(inv.getCode());
            in.setName(inv.getName());
            in.setWarehouseId(storageInWarehouse.getWarehouseId());
            // 应入库数量=退货数量
            in.setInWarehouseNumber(rod.getDiscrepancyNum());
            in.setAuditInWarehouseNumber(0);
            in.setInWarehouseTime(now);
            in.setOperatorId(operatorId);
            num += in.getInWarehouseNumber();
            storageInWarehouseGoods.add(in);
        }
        storageInWarehouse.setInWarehouseNumber(num);
        storageInWarehouse.setInWarehouseTime(now);
        storageInWarehouse.setOperatorId(operatorId);
        this.storageInWarehouseServiceDomain.save(storageInWarehouse);
        this.storageInWarehouseGoodsServiceDomain.saveBatch(storageInWarehouseGoods);
    }

    /**
     * 创建出库单
     *
     * @param outStockDiscrepancies
     * @param takeStock
     */
    public void saveOutWarehouse(List<TakeDiscrepancy> outStockDiscrepancies, TakeStock takeStock) {

        //出库主表
        StorageOutWarehouse storageOutWarehouse = new StorageOutWarehouse();
        String outWarehouseId = sequenceDomainService.nextDateSequence("CKID", 6);
        storageOutWarehouse.setOutWarehouseId(outWarehouseId);
        storageOutWarehouse.setOutStatus(OutStatusEnum.DCK);
        storageOutWarehouse.setOutType(InOutTypeEnum.WITHOUT);
        storageOutWarehouse.setShopWaybill(takeStock.getCode());
        List<AdvanceInvoiceDetail> advanceInvoiceDetailList = new ArrayList<>();
        List<StorageOutWarehouseGoods> storageOutWarehouseGoodsList = new ArrayList<>();
        // 预发货发货总数（即出库单应出库量）
        int detailOutNumber = 0;
        for (TakeDiscrepancy detail : outStockDiscrepancies) {
            //出库明细
            StorageOutWarehouseGoods storageOutWarehouseGoods = new StorageOutWarehouseGoods();
            storageOutWarehouseGoods.setOutWarehouseId(storageOutWarehouse.getOutWarehouseId());
            storageOutWarehouseGoods.setWarehouseId(takeStock.getWarehouseId());

            StorageInventory inv = this.storageInventoryServiceDomain.lambdaQuery()
                    .eq(StorageInventory::getWarehouseId, takeStock.getWarehouseId())
                    .eq(StorageInventory::getCode, detail.getArticleCode()).one();

            storageOutWarehouseGoods.setInventoryId(inv.getId());
            storageOutWarehouseGoods.setOutWarehouseNumber(detail.getDiscrepancyNum() * -1);
            storageOutWarehouseGoods.setOutStatus(OutStatusEnum.DCK);
            detailOutNumber = detailOutNumber + storageOutWarehouseGoods.getOutWarehouseNumber();
            storageOutWarehouseGoods.setArticleCode(detail.getArticleCode());
            storageOutWarehouseGoods.setArticleType(inv.getArticleType());
            storageOutWarehouseGoods.setBatchCode(detail.getBatchCode());
            storageOutWarehouseGoodsList.add(storageOutWarehouseGoods);
        }
        // 不同出库单的应出库根据申领数量决定
        storageOutWarehouse.setOutWarehouseNumber(detailOutNumber);
        storageOutWarehouse.setWarehouseId(takeStock.getWarehouseId());
        storageOutWarehouseServiceDomain.save(storageOutWarehouse);
        storageOutWarehouseGoodsServiceDomain.saveBatch(storageOutWarehouseGoodsList);
    }


    /**
     * id查询库存盘点
     *
     * @param id {@link TakeStock}
     * @return true/false
     */
    public TakeStock getById(Long id) {
        TakeStock saveEntity = this.takeStockDomainService.getById(id);
        List<TakeDetail> takeDetails = takeDetailDomainService.selectJoinList(TakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(TakeDetail.class)
                .selectAs(StorageArticle::getName, TakeDetail::getArticleName)
                .selectAs(StorageArticle::getName, TakeDetail::getArticleName)
                .selectAs(StorageArticle::getManufacturerChannel, TakeDetail::getManufacturerChannel)
                .selectAs(StorageArticle::getImageFiles, TakeDetail::getImageFiles)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                .eq(TakeDetail::getTakeStockId, id)
        );
        takeDetails.forEach(v -> {
            if (v.getBatchInfo() == null) {
                List<BatchPriceNum> batchPriceNums = takeDetailDomainService.getBatchPriceNum(v.getArticleCode(), saveEntity.getWarehouseId());
                if (CollectionUtils.isNotEmpty(batchPriceNums)) {
                    v.setBatchInfo(JSON.parseArray(JSON.toJSONString(batchPriceNums)));
                    v.setPrice(batchPriceNums.get(0).getPrice() != null ? batchPriceNums.get(0).getPrice() * 100 : 0);
                }
            }
        });
        saveEntity.setTakeDetails(takeDetails);
        return saveEntity;
    }

    public DataGrid<TakeDetail> getTakeDetailPage(TakeStockQuery takeStockQuery) {
        if(takeStockQuery.getTakeStockId() == null){
            return new DataGrid<>();
        }
        TakeStock saveEntity = this.takeStockDomainService.getById(takeStockQuery.getTakeStockId());
        return PageHelper.startPage(takeStockQuery, p ->
                takeDetailDomainService.selectJoinList(TakeDetail.class, MPJWrappers.lambdaJoin()
                        .selectAll(TakeDetail.class)
                        .selectAs(TakeDetail::getPrice, TakeDetail::getInPrice)
                        .selectAs(TakeDetail::getInventoryNum, TakeDetail::getNum)
                        .selectAs(StorageArticle::getManufacturerGoodsCode, TakeDetail::getManufacturerGoodsCode)
                        .selectAs(StorageArticle::getManufacturerGoodsName, TakeDetail::getManufacturerGoodsName)
                        .selectAs(StorageArticle::getName, TakeDetail::getArticleName)
                        //.selectAs(StorageArticle::getName, TakeDetail::getArticleName)
                        .selectAs(StorageArticle::getManufacturerChannel, TakeDetail::getManufacturerChannel)
                        .selectAs(StorageArticle::getImageFiles, TakeDetail::getImageFiles)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                        .eq(TakeDetail::getTakeStockId, takeStockQuery.getTakeStockId())
                        .like(StringUtils.isNotBlank(takeStockQuery.getArticleCode()), TakeDetail::getArticleCode, takeStockQuery.getArticleCode())
                        .like(StringUtils.isNotBlank(takeStockQuery.getArticleName()), TakeDetail::getArticleName, takeStockQuery.getArticleName())
                        .like(StringUtils.isNotBlank(takeStockQuery.getLocation()), TakeDetail::getLocation, takeStockQuery.getLocation())
                        .like(StringUtils.isNotBlank(takeStockQuery.getNumberOem()), TakeDetail::getNumberOem, takeStockQuery.getNumberOem())
                        .eq(StringUtils.isNotBlank(takeStockQuery.getManufacturerChannel()), TakeDetail::getManufacturerChannel, takeStockQuery.getManufacturerChannel())
                        .like(StringUtils.isNotBlank(takeStockQuery.getManufacturerCode()), StorageArticle::getManufacturerCode, takeStockQuery.getManufacturerCode())
                        .like(StringUtils.isNotBlank(takeStockQuery.getManufacturerName()), StorageArticle::getManufacturerName, takeStockQuery.getManufacturerName())
                )).peek(v -> {
                    if (v.getBatchInfo() == null) {
                        List<BatchPriceNum> batchPriceNums = takeDetailDomainService.getBatchPriceNum(v.getArticleCode(), saveEntity.getWarehouseId());
                        if (CollectionUtils.isNotEmpty(batchPriceNums)) {
                            v.setBatchInfo(JSON.parseArray(JSON.toJSONString(batchPriceNums)));
                            v.setPrice(batchPriceNums.get(0).getPrice() != null ? batchPriceNums.get(0).getPrice() * 100 : 0);
                        }
                    }
      });
    }

    /**
     * 库存盘点删除
     *
     * @param id id
     * @return true/false
     */
    public boolean removeById(Long id) {
        TakeStock takeStock = this.takeStockDomainService.getById(id);
        if (takeStock.getStockStatus().equals(StockStatus.PASS)
                || takeStock.getStockStatus().equals(StockStatus.WAIT_APPROVE)) {
            throw new MaginaException("当前盘点单不允许删除！");
        }
        takeStock.setStockStatus(StockStatus.CLOSE);
        return this.takeStockDomainService.updateById(takeStock);
    }

    /**
     * 库存盘点基础数据
     *
     * @param takeStockQuery
     * @return
     */
    public DataGrid<TakeDetail> getStockDetail(TakeStockQuery takeStockQuery) {
        return PageHelper.startPage(takeStockQuery, p ->
                this.takeDetailDomainService.selectJoinList(TakeDetail.class,
                        MPJWrappers.lambdaJoin().selectAll(TakeDetail.class)
                                .selectAs(StorageArticle::getProductId, TakeDetail::getProductId)
                                .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                                .leftJoin(TakeStock.class, TakeStock::getId, TakeDetail::getTakeStockId)
                                .like(StringUtils.isNotBlank(takeStockQuery.getTakeStockCode()), TakeStock::getCode, takeStockQuery.getTakeStockCode())
                                .like(StringUtils.isNotBlank(takeStockQuery.getArticleCode()), StorageArticle::getCode, takeStockQuery.getArticleCode())
                                .like(StringUtils.isNotBlank(takeStockQuery.getArticleName()), StorageArticle::getName, takeStockQuery.getArticleName())
                                .orderByDesc(TakeDetail::getId)
                )
        ).peek(v -> {
            if (v.getProductId() == null) {
                ProductTreeDto info = this.productTreeDomainService.getFullProductTree(v.getProductId());
                v.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
            }
        });
    }

    /**
     * 库存盘点基础数据
     *
     * @param takeStockDto
     * @return
     */
    public DataGrid<TakeDetail> getStockDetail(TakeStockDto takeStockDto) {
        return PageHelper.startPage(takeStockDto, p ->
                this.storageInventoryServiceDomain.getTakeDetailByStorageInventory(p)
        ).peek(v -> {
//            if (v.getBatchInfo() == null) {
//                List<BatchPriceNum> batchPriceNums = takeDetailDomainService.getBatchPriceNum(v.getArticleCode(), takeStockDto.getWarehouseId());
//                if (CollectionUtils.isNotEmpty(batchPriceNums)) {
//                    v.setBatchInfo(JSON.parseArray(JSON.toJSONString(batchPriceNums)));
//                    v.setPrice(batchPriceNums.get(0).getPrice() != null ? batchPriceNums.get(0).getPrice() * 100 : 0);
//                }
//                v.setTakeStockId(takeStockDto.getTakeStockId());
//            }
            if (takeStockDto.getStockType().equals(1)) {
                ProductTreeDto info = this.productTreeDomainService.getFullProductTree(v.getProductId());
                v.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
            } else {
                if (StringUtils.isBlank(v.getLocation())) {
                    StorageInventory storageInventory = storageInventoryServiceDomain.selectJoinOne(StorageInventory.class, MPJWrappers.lambdaJoin()
                            .select(StorageInventory::getLocation)
                            .selectAs(StorageArticle::getManufacturerChannel, StorageInventory::getManufacturerChannel)
                            .leftJoin(StorageArticle.class, StorageArticle::getCode, StorageInventory::getCode)
                            .eq(StorageInventory::getCode, v.getArticleCode())
                    );
                    if (storageInventory != null) {
                        v.setLocation(storageInventory.getLocation());
                        v.setManufacturerChannel(storageInventory.getManufacturerChannel());
                    }
                }
            }

        });
    }

    public TakeDetailStatistics stockDetailStatistics(TakeStockDto takeStockDto) {
       return this.storageInventoryServiceDomain.stockDetailStatistics(takeStockDto);
    }

    /**
     * 库存盘点基础数据
     *
     * @param takeStockDto
     * @return
     */
    public DataGrid<TakeDetail> getStockMachine(TakeStockDto takeStockDto) {
        return PageHelper.startPage(takeStockDto, p ->
                this.storageInventoryServiceDomain.getTakeMachineDetail(p)
        ).peek(v -> {
            if (takeStockDto.getStockType().equals(1)) {
                if (v.getHostType() == null || StringUtils.isBlank(v.getHostType().getValue())) {
                    Machine machine = machineDomainService.lambdaQuery().eq(Machine::getMachineNum, v.getArticleCode()).one();
                    v.setHostType(machine.getHostType());
                    v.setDeviceStatus(machine.getDeviceStatus());
                    v.setProductId(machine.getProductId());
                    v.setArticleName(machine.getProductName());
                    v.setDeviceOn(machine.getDeviceOn());
                    v.setLocation(machine.getLocation());
                    v.setMachineStatus(machine.getStatus());
                    v.setOriginCode(machine.getOriginCode());
                    if (machine.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                        ProductTree productTree = productTreeDomainService.getById(machine.getProductId());
                        v.setArticleName(productTree.getName());
                        v.setFullIdPath(productTree.getFullIdPath());
                    }
                }
            }
        });
    }
    /**
     * 库存盘点基础数据
     *
     * @param takeStockDto
     * @return
     */
    public DataGrid<TakeDetail> getTakeMachine(TakeStockDto takeStockDto) {
        return PageHelper.startPage(takeStockDto, p ->
               this.getTakeMachineDetails(takeStockDto)
        );
    }

    private List<TakeDetail> getTakeMachineDetails(TakeStockDto takeStockDto) {
        return this.takeDetailDomainService.selectJoinList(TakeDetail.class,MPJWrappers.lambdaJoin()
                .selectAll(TakeDetail.class)
                .selectAs(Machine::getHostType, TakeDetail::getHostType)
                .selectAs(Machine::getOriginCode, TakeDetail::getOriginCode)
                .selectAs(Machine::getDeviceOn, TakeDetail::getDeviceOn)
                .selectAs(Machine::getDeviceStatus, TakeDetail::getDeviceStatus)
                .selectAs(Machine::getStatus, TakeDetail::getMachineStatus)
                .selectAs(ProductTree::getFullIdPath, TakeDetail::getFullIdPath)
                .leftJoin(Machine.class, Machine::getMachineNum, TakeDetail::getArticleCode)
                .leftJoin(ProductTree.class, ProductTree::getId, Machine::getProductId)
                .eq(TakeDetail::getTakeStockId, takeStockDto.getTakeStockId())
                .like(StringUtils.isNotBlank(takeStockDto.getArticleCode()), TakeDetail::getArticleCode, takeStockDto.getArticleCode())
                .like(StringUtils.isNotBlank(takeStockDto.getArticleName()), TakeDetail::getArticleName, takeStockDto.getArticleName())
                .like(StringUtils.isNotBlank(takeStockDto.getOriginCode()), Machine::getOriginCode, takeStockDto.getOriginCode())
                .like(StringUtils.isNotBlank(takeStockDto.getLocation()), TakeDetail::getLocation, takeStockDto.getLocation())
                .in(CollectionUtils.isNotEmpty(takeStockDto.getHostTypes()), TakeDetail::getHostType, takeStockDto.getHostTypes())
                .in(CollectionUtils.isNotEmpty(takeStockDto.getProductIds()), TakeDetail::getProductId, takeStockDto.getProductIds())
                .in(CollectionUtils.isNotEmpty(takeStockDto.getDeviceStatus()), TakeDetail::getDeviceStatus, takeStockDto.getDeviceStatus())
                .in(CollectionUtils.isNotEmpty(takeStockDto.getDeviceOn()), TakeDetail::getDeviceOn, takeStockDto.getDeviceOn())
                .orderByDesc(TakeDetail::getCreatedAt)
        );
    }

    public TakeDetailTotalVo getStockMachineTotal(TakeStockDto takeStockDto) {
        List<TakeDetail> takeDetails =  this.getTakeMachineDetails(takeStockDto);
        TakeDetailTotalVo takeDetailTotalVo = new TakeDetailTotalVo();
        takeDetailTotalVo.setTotalNum(takeDetails.size());
        takeDetailTotalVo.setOverTakeNum(takeDetails.stream().filter(v -> v.getId() != null).count());
        takeDetailTotalVo.setNotTakeNum(takeDetails.stream().filter(v -> v.getId() == null).count());
        takeDetailTotalVo.setInStockNum(takeDetails.stream().filter(v -> v.getId() != null && v.getStockNum() != null && v.getStockNum() > 0).count());
        takeDetailTotalVo.setNotStockNum(takeDetails.stream().filter(v -> v.getId() != null && v.getStockNum() != null && v.getStockNum() == 0).count());
        return takeDetailTotalVo;
    }

    public TakeDetailTotalVo getStockDetailTotal(TakeStockDto takeStockDto) {
        List<TakeDetail> takeDetails = this.storageInventoryServiceDomain.getTakeMachineDetail(takeStockDto);
        TakeDetailTotalVo takeDetailTotalVo = new TakeDetailTotalVo();
        takeDetailTotalVo.setTotalNum(takeDetails.size());
        takeDetailTotalVo.setOverTakeNum(takeDetails.stream().filter(v -> v.getId() != null).count());
        takeDetailTotalVo.setNotTakeNum(takeDetails.stream().filter(v -> v.getId() == null).count());
        takeDetailTotalVo.setInStockNum(takeDetails.stream().filter(v -> v.getId() != null && v.getStockNum() != null && v.getStockNum() > 0).count());
        takeDetailTotalVo.setNotStockNum(takeDetails.stream().filter(v -> v.getId() != null && v.getStockNum() != null && v.getStockNum() == 0).count());
        return takeDetailTotalVo;
    }


    public Boolean discrepancy(Long id) {
        if (Objects.isNull(id)) {
            throw new MaginaException("参数错误，盘点单为空！");
        }
        TakeStock takeStock = this.takeStockDomainService.getById(id);
        if (Objects.isNull(takeStock)) {
            throw new MaginaException("参数错误，盘点单不存在！");
        }
        if (!takeStock.getStockStatus().equals(StockStatus.PASS)) {
            throw new MaginaException("盘点尚未完成复核，无法进行报损报溢！");
        }
        List<TakeDetail> takeDetails = takeDetailDomainService.lambdaQuery().eq(TakeDetail::getTakeStockId, id).list();
        if (CollectionUtils.isEmpty(takeDetails)) {
            throw new MaginaException("盘点单明细为空，无法进行报损报溢！");
        }
        if (takeStock.getStockType().equals(1)) {
            takeDetails.forEach(v -> {
                machineDomainService.lambdaUpdate()
                        .set(StringUtils.isNotBlank(v.getLocation()), Machine::getLocation, v.getLocation())
                        .set(StringUtils.isNotBlank(v.getTagName()), Machine::getTagName, v.getTagName())
                        .set(StringUtils.isNotBlank(v.getArticleName()), Machine::getProductName, v.getArticleName())
                        .set(v.getDeviceOn() != null, Machine::getDeviceOn, v.getDeviceOn())
                        .set(v.getDeviceStatus() != null, Machine::getDeviceStatus, v.getDeviceStatus())
                        .set(v.getMachineStatus() != null, Machine::getStatus, v.getMachineStatus())
                        .set(v.getHostType() != null, Machine::getHostType, v.getHostType())
                        .set(v.getProductId() != null, Machine::getProductId, v.getProductId())
                        .set(StringUtils.isNotBlank(v.getOriginCode()), Machine::getOriginCode, v.getOriginCode())
                        .eq(Machine::getMachineNum, v.getArticleCode()).update();
            });
        } else {
            takeDetails.forEach(v -> {
                if (StringUtils.isNotBlank(v.getLocation())) {
                    storageInventoryServiceDomain.lambdaUpdate()
                            .set(StorageInventory::getLocation, v.getLocation())
                            .eq(StorageInventory::getCode, v.getArticleCode()).update();
                }
                if (v.getManufacturerChannel() != null) {
                    storageArticleServiceDomain.lambdaUpdate()
                            .set(StorageArticle::getManufacturerChannel, v.getManufacturerChannel())
                            .eq(StorageArticle::getCode, v.getArticleCode()).update();
                }
            });
        }

        List<TakeDetail> discrepancyDetails = takeDetails.stream().filter(v -> !v.getStockNum().equals(v.getInventoryNum())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(discrepancyDetails)) {
            takeStock.setIsDiscrepancy(false);
            takeStock.setDiscrepancyStatus(DiscrepancyStatus.NORMAL);
            takeStockDomainService.updateById(takeStock);
            return Boolean.TRUE;
        }
        List<TakeDiscrepancy> takeDiscrepancies = discrepancyDetails.stream().map(v -> {
            TakeDiscrepancy takeDiscrepancy = new TakeDiscrepancy();
            takeDiscrepancy.setTakeStockCode(takeStock.getCode());
            takeDiscrepancy.setStockType(takeStock.getStockType());
            takeDiscrepancy.setTakeDetailId(v.getId());
            takeDiscrepancy.setArticleCode(v.getArticleCode());
            takeDiscrepancy.setInventoryNum(v.getInventoryNum());
            takeDiscrepancy.setDiscrepancyNum(v.getStockNum() - v.getInventoryNum());
            takeDiscrepancy.setDiscrepancyAmount(v.getPrice() * takeDiscrepancy.getDiscrepancyNum().longValue());
            //takeDiscrepancy.setBatchCode(getBatchCode(v.getBatchInfo(), v.getStockNum()));
            takeDiscrepancy.setBatchCode(v.getBatchCode());
            takeDiscrepancy.setDiscrepancyStatus(takeDiscrepancy.getDiscrepancyNum() < 0 ? DiscrepancyStatus.WITHOUT : DiscrepancyStatus.OVERFLOW);
            return takeDiscrepancy;
        }).collect(Collectors.toList());
        //机器盘点报损报溢
        if(takeStock.getStockType().equals(1)){
            List<String> takeWithoutDiscrepancies = takeDiscrepancies.stream().filter(v -> v.getDiscrepancyStatus().equals(DiscrepancyStatus.WITHOUT)).map(TakeDiscrepancy::getArticleCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(takeWithoutDiscrepancies)) {
                machineDomainService.lambdaUpdate()
                       .set(Machine::getStatus, MachineStatus.WITHOUT)
                       .in(Machine::getMachineNum, takeWithoutDiscrepancies)
                        .update();
            }
        }
        takeDiscrepancyDomainService.saveBatch(takeDiscrepancies);
        takeStock.setIsDiscrepancy(true);
        takeStock.setDiscrepancyStatus(DiscrepancyStatus.ABNORMAL);
        return takeStockDomainService.updateById(takeStock);
    }

    private String getBatchCode(JSONArray batchInfo, Integer stockNum) {
        if (stockNum == 0 || CollectionUtils.isEmpty(batchInfo)) {
            return null;
        }
        List<BatchPriceNum> batchPriceNums = JSON.parseArray(JSON.toJSONString(batchInfo), BatchPriceNum.class);
        Integer totalNum = batchPriceNums.stream().mapToInt(BatchPriceNum::getNum).sum();
        if (totalNum.equals(stockNum)) {
            return null;
        }
        String batchCode = "";
        for (BatchPriceNum batchPriceNum : batchPriceNums) {
            if (stockNum <= 0) {
                batchCode = batchCode + batchPriceNum.getBatcheCode() + ",";
                continue;
            }
            if (batchPriceNum.getNum() < 0) {
                batchCode = batchCode + batchPriceNum.getBatcheCode() + ",";
            } else {
                if (batchPriceNum.getNum() <= stockNum) {
                    stockNum = stockNum - batchPriceNum.getNum();
                } else {
                    stockNum = 0;
                    batchCode = batchCode + batchPriceNum.getBatcheCode() + ",";
                }
            }
        }
        return batchCode.length() > 0 ? batchCode.substring(0, batchCode.length() - 1) : null;
    }


    /**
     * 库存盘点分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<TakeDiscrepancy> pageDiscrepancy(TakeStockQuery pageQuery) {
        if (StringUtils.isNotBlank(pageQuery.getTakeStockCode())) {
            TakeStock takeStock = this.takeStockDomainService.lambdaQuery().eq(TakeStock::getCode, pageQuery.getTakeStockCode()).one();
            if (Objects.nonNull(takeStock)) {
                pageQuery.setStockType(takeStock.getStockType());
            }
        }

        if (pageQuery.getStockType() != null && pageQuery.getStockType().equals(1)) {
            MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                    .selectAll(TakeDiscrepancy.class)
                    .selectAs(UserBasic::getName, TakeDiscrepancy::getAuditorByName)
                    .selectAs(TakeDiscrepancy::getDiscrepancyStatus, "discrepancyStatus")
                    .selectAs(TakeDetail::getArticleCode, TakeDiscrepancy::getArticleCode)
                    .selectAs(TakeDetail::getArticleName, TakeDiscrepancy::getArticleName)
                    .selectAs(TakeDetail::getInventoryNum, TakeDiscrepancy::getInventoryNum)
                    .selectAs(TakeDetail::getStockNum, TakeDiscrepancy::getStockNum)
                    .selectAs(Machine::getPurchasePrice, TakeDiscrepancy::getPrice)
                    .selectAs(TakeDetail::getInventoryAmount, TakeDiscrepancy::getInventoryAmount)
                    .selectAs(TakeStock::getWarehouseId, TakeDiscrepancy::getWarehouseId)
                    .selectAs(Machine::getPicsUrl, TakeDiscrepancy::getImageFiles)

                    .leftJoin(TakeDetail.class, TakeDetail::getId, TakeDiscrepancy::getTakeDetailId)
                    .leftJoin(TakeStock.class, TakeStock::getCode, TakeDiscrepancy::getTakeStockCode)
                    .leftJoin(Machine.class, Machine::getMachineNum, TakeDetail::getArticleCode)
                    .leftJoin(UserBasic.class, UserBasic::getId, TakeDiscrepancy::getAuditorBy);
            addCondition(pageQuery, wrapper);
            wrapper.orderByDesc(TakeStock::getId);
            return PageHelper.startPage(pageQuery, p ->
                    this.takeDiscrepancyDomainService.selectJoinList(TakeDiscrepancy.class, wrapper)
            ).peek(v -> {
                List<BatchPriceNum> batchPriceNums = takeDetailDomainService.getBatchPriceNum(v.getArticleCode(), v.getWarehouseId());
                if (CollectionUtils.isNotEmpty(batchPriceNums)) {
                    v.setBatchInfo(JSON.parseArray(JSON.toJSONString(batchPriceNums)));
                    v.setPrice(batchPriceNums.get(0).getPrice() != null ? batchPriceNums.get(0).getPrice() * 100 : 0);
                }
                if (v.getPrice() != null) {
                    Long amount = v.getDiscrepancyNum() * v.getPrice();
                    v.setDiscrepancyAmount(amount);
                }
            });
        } else {
            MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                    .selectAll(TakeDiscrepancy.class)
                    .selectAs(UserBasic::getName, TakeDiscrepancy::getAuditorByName)
                    .selectAs(TakeDiscrepancy::getDiscrepancyStatus, "discrepancyStatus")
                    .selectAs(TakeDiscrepancy::getBatchCode, TakeDiscrepancy::getBatchCode)
                    .selectAs(TakeDetail::getArticleCode, TakeDiscrepancy::getArticleCode)
                    .selectAs(TakeDetail::getArticleName, TakeDiscrepancy::getArticleName)
                    .selectAs(StorageArticle::getUnit, TakeDiscrepancy::getUnit)
                    .selectAs(StorageArticle::getNumberOem, TakeDiscrepancy::getNumberOem)
                    .selectAs(TakeDetail::getInventoryNum, TakeDiscrepancy::getInventoryNum)
                    .selectAs(TakeDetail::getStockNum, TakeDiscrepancy::getStockNum)
                    .selectAs(TakeDetail::getPrice, TakeDiscrepancy::getPrice)
                    .selectAs(TakeDetail::getInventoryAmount, TakeDiscrepancy::getInventoryAmount)
                    .selectAs(StorageArticle::getProductId, TakeDiscrepancy::getProductId)
                    .selectAs(TakeStock::getWarehouseId, TakeDiscrepancy::getWarehouseId)
                    .selectAs(StorageArticle::getImageFiles, TakeDiscrepancy::getImageFiles)

                    .leftJoin(TakeDetail.class, TakeDetail::getId, TakeDiscrepancy::getTakeDetailId)
                    .leftJoin(TakeStock.class, TakeStock::getCode, TakeDiscrepancy::getTakeStockCode)
                    .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                    .leftJoin(UserBasic.class, UserBasic::getId, TakeDiscrepancy::getAuditorBy);
            addCondition(pageQuery, wrapper);
            wrapper.orderByDesc(TakeStock::getId);
            return PageHelper.startPage(pageQuery, p ->
                    this.takeDiscrepancyDomainService.selectJoinList(TakeDiscrepancy.class, wrapper)
            );
//                    .peek(v -> {
//                List<BatchPriceNum> batchPriceNums = takeDetailDomainService.getBatchPriceNum(v.getArticleCode(), v.getWarehouseId());
//                if (CollectionUtils.isNotEmpty(batchPriceNums)) {
//                    v.setBatchInfo(JSON.parseArray(JSON.toJSONString(batchPriceNums)));
//                    v.setPrice(batchPriceNums.get(0).getPrice() != null ? batchPriceNums.get(0).getPrice() * 100 : 0);
//                }
//            });
        }


    }

    private void addCondition(TakeStockQuery pageQuery, MPJLambdaWrapper<Object> wrapper) {
        wrapper.eq(StringUtils.isNotBlank(pageQuery.getType()), TakeStock::getType, pageQuery.getType())
                .eq(pageQuery.getStockStatus() != null, TakeStock::getStockStatus, pageQuery.getStockStatus())
                .eq(Objects.nonNull(pageQuery.getStockType()), TakeStock::getStockType, pageQuery.getStockType())
                .eq(Objects.nonNull(pageQuery.getTakeStockId()), TakeStock::getId, pageQuery.getTakeStockId())
                .like(StringUtils.isNotBlank(pageQuery.getTakeStockCode()), TakeDiscrepancy::getTakeStockCode, pageQuery.getTakeStockCode())
                .like(StringUtils.isNotBlank(pageQuery.getArticleCode()), StorageArticle::getCode, pageQuery.getArticleCode())
                .like(StringUtils.isNotBlank(pageQuery.getArticleName()), StorageArticle::getName, pageQuery.getArticleName())
                .like(StringUtils.isNotBlank(pageQuery.getNumberOem()), StorageArticle::getNumberOem, pageQuery.getNumberOem())
                .like(StringUtils.isNotBlank(pageQuery.getName()), Warehouse::getName, pageQuery.getName())
                .ge(StringUtils.isNotBlank(pageQuery.getStartTime()), TakeStock::getCreatedAt, pageQuery.getStartTime() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndTime()), TakeStock::getCreatedAt, pageQuery.getEndTime() + " 23.59.59")
                .ge(pageQuery.getMinAmount() != null, TakeDiscrepancy::getDiscrepancyAmount, pageQuery.getMinAmount())
                .le(pageQuery.getMaxAmount() != null, TakeDiscrepancy::getDiscrepancyAmount, pageQuery.getMaxAmount())
                .ge(pageQuery.getMinNum() != null, TakeDiscrepancy::getDiscrepancyNum, pageQuery.getMinNum())
                .le(pageQuery.getMaxNum() != null, TakeDiscrepancy::getDiscrepancyNum, pageQuery.getMaxNum())
                .eq(pageQuery.getDiscrepancyStatus() != null, TakeDiscrepancy::getDiscrepancyStatus, pageQuery.getDiscrepancyStatus());
    }

    public TakeDiscrepancyTotalVo getDiscrepancyTotal(TakeStockQuery pageQuery) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAs(TakeDiscrepancy::getDiscrepancyStatus, "discrepancyStatus")
                .selectCount(TakeDiscrepancy::getId, "rowCount")
                .selectSum(TakeDiscrepancy::getDiscrepancyNum, "discrepancyNum")
                .selectSum(TakeDiscrepancy::getDiscrepancyAmount, "discrepancyAmount")
                .leftJoin(TakeDetail.class, TakeDetail::getId, TakeDiscrepancy::getTakeDetailId)
                .leftJoin(TakeStock.class, TakeStock::getCode, TakeDiscrepancy::getTakeStockCode)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                .leftJoin(UserBasic.class, UserBasic::getId, TakeDiscrepancy::getAuditorBy);
        addCondition(pageQuery, wrapper);
        wrapper.groupBy(TakeDiscrepancy::getDiscrepancyStatus);
        List<TakeDiscrepancy> takeDiscrepancies = this.takeDiscrepancyDomainService.selectJoinList(TakeDiscrepancy.class, wrapper);
        TakeDiscrepancyTotalVo takeDiscrepancyVo = new TakeDiscrepancyTotalVo();
        for (TakeDiscrepancy takeDiscrepancy : takeDiscrepancies) {
            if (takeDiscrepancy.getDiscrepancyStatus().equals(DiscrepancyStatus.OVERFLOW)) {
                takeDiscrepancyVo.setOverflowCount(takeDiscrepancy.getRowCount());
                takeDiscrepancyVo.setOverflowNum(takeDiscrepancy.getDiscrepancyNum());
                takeDiscrepancyVo.setOverflowAmount(takeDiscrepancy.getDiscrepancyAmount());
            }
            if (takeDiscrepancy.getDiscrepancyStatus().equals(DiscrepancyStatus.WITHOUT)) {
                takeDiscrepancyVo.setWithoutCount(takeDiscrepancy.getRowCount());
                takeDiscrepancyVo.setWithoutNum(takeDiscrepancy.getDiscrepancyNum());
                takeDiscrepancyVo.setWithoutAmount(takeDiscrepancy.getDiscrepancyAmount());
            }
        }
        takeDiscrepancyVo.setTotalAmount(takeDiscrepancyVo.getOverflowAmount() + takeDiscrepancyVo.getWithoutAmount());
        return takeDiscrepancyVo;
    }

    /**
     * 导出机器盘点
     *
     * @param response
     * @return
     */
    public Boolean downloadData(HttpServletResponse response, TakeStockQuery pageQuery) throws IOException {
        try {
            //查询数据
            List<TakeDetail> excelList = this.takeDetailDomainService.selectJoinList(TakeDetail.class, MPJWrappers.lambdaJoin()
                    .selectAll(TakeDetail.class)
                    .selectAs(TakeStock::getCode, TakeDetail::getTakeStockCode)
                    .selectAs(Manufacturer::getCode, TakeDetail::getManufacturerCode)
                    .selectAs(Manufacturer::getName, TakeDetail::getManufacturerName)
                    .leftJoin(TakeStock.class, TakeStock::getId, TakeDetail::getTakeStockId)
                    .leftJoin(Machine.class, Machine::getMachineNum, TakeDetail::getArticleCode)
                    .leftJoin(Manufacturer.class, Manufacturer::getId, Machine::getManufacturerId)
                    .eq(TakeDetail::getTakeStockId, pageQuery.getTakeStockId())
            );
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "盘点明细数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), TakeDetail.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 导出耗材盘点
     *
     * @param response
     * @return
     */
    public Boolean downloadArticleData(HttpServletResponse response, TakeStockQuery pageQuery) throws IOException {
        try {
            //查询数据
            List<TakeDetailExcel> excelList = this.takeDetailDomainService.selectJoinList(TakeDetailExcel.class, MPJWrappers.lambdaJoin()
                    .selectAll(TakeDetail.class)
                    .selectAs(TakeStock::getCode, TakeDetailExcel::getTakeStockCode)
                    .selectAs(StorageArticle::getManufacturerGoodsCode, TakeDetailExcel::getManufacturerGoodsCode)
                    .selectAs(StorageArticle::getManufacturerGoodsName, TakeDetailExcel::getManufacturerGoodsName)
                    .leftJoin(TakeStock.class, TakeStock::getId, TakeDetail::getTakeStockId)
                    .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                    .eq(TakeDetail::getTakeStockId, pageQuery.getTakeStockId())
            );
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "盘点明细数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), TakeDetailExcel.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 盘点完成 一键入库
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean inStock(Long id) {
        TakeStock takeStock = takeStockDomainService.getById(id);
        if (takeStock == null) {
            throw new MaginaException("盘点单不存在！");
        }
        if (takeStock.getStockType().equals(1)) {
            return Boolean.FALSE;
        }

        List<TakeDetail> takeDetails = takeDetailDomainService.lambdaQuery().eq(TakeDetail::getTakeStockId, id).list();
        if (CollectionUtils.isEmpty(takeDetails)) {
            throw new MaginaException("盘点单明细为空，无法进行入库操作！");
        }
        List<StorageInventory> storageInventories = new ArrayList<>();
        List<StorageInWarehouseGoods> inGoodsList = new ArrayList<>();

        StorageInWarehouse inWarehouse = new StorageInWarehouse();
        String rkCode = this.sequenceDomainService.nextDateSequence(StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
        inWarehouse.setInWarehouseId(rkCode);
        inWarehouse.setWarehouseId(takeStock.getWarehouseId());

        for (TakeDetail takeDetail : takeDetails) {
            if (takeDetail.getStockNum() != null) {
                StorageInventory storageInventory = storageInventoryServiceDomain.getByWarehouseAndCode(takeStock.getWarehouseId(), takeDetail.getArticleCode());
                if (storageInventory == null) {
                    storageInventory = new StorageInventory();
                    storageInventory.setCode(takeDetail.getArticleCode());
                    storageInventory.setWarehouseId(takeStock.getWarehouseId());
                    storageInventory.setName(takeDetail.getArticleName());
                }
                storageInventory.setLocation(takeDetail.getLocation());
                storageInventory.setSumWarehouseNumber(takeDetail.getStockNum());
                storageInventories.add(storageInventory);

                StorageInWarehouseGoods inGoods = new StorageInWarehouseGoods();
                inGoods.setBatchCode(DateUtil.format(new Date(), DateTimeFormatter.ofPattern("yyyyMMdd")));
                inGoods.setCode(takeDetail.getArticleCode());
                inGoods.setName(takeDetail.getArticleName());
                inGoods.setWarehouseId(takeStock.getWarehouseId());
                inGoods.setInWarehouseNumber(takeDetail.getStockNum());
                inGoods.setAuditInWarehouseNumber(takeDetail.getStockNum());
                inGoods.setInWarehouseTime(LocalDateTime.now());
                inGoods.setOperatorId(ApplicationSessions.id());
                inGoods.setInWarehouseId(inWarehouse.getInWarehouseId());
                inGoodsList.add(inGoods);

                this.initInventoryBatchAndFlow(storageInventory, rkCode);
            }
        }
        if (CollectionUtils.isNotEmpty(storageInventories)) {
            storageInventoryServiceDomain.saveOrUpdateBatch(storageInventories);
            storageInWarehouseGoodsServiceDomain.saveBatch(inGoodsList);
        }

        //入库单主表
        inWarehouse.setInType(InOutTypeEnum.ENGINEER_APPLY_RETURN);
        inWarehouse.setShopWaybill(takeStock.getCode());
        inWarehouse.setInStatus(InStatusEnum.YRK);
        inWarehouse.setInWarehouseNumber(takeStock.getStockNum());
        inWarehouse.setInType(InOutTypeEnum.TAKE_STOCK);
        inWarehouse.setAuditInWarehouseNumber(takeStock.getStockNum());
        return storageInWarehouseServiceDomain.save(inWarehouse);

    }

    public void initInventoryBatchAndFlow(StorageInventory storageInventory, String flowId) {
        StorageInventoryBatch storageInventoryBatch = new StorageInventoryBatch();
        storageInventoryBatch.setCode(storageInventory.getCode());
        storageInventoryBatch.setWarehouseId(storageInventory.getWarehouseId());
        storageInventoryBatch.setInWarehouseId(flowId);
        storageInventoryBatch.setInWarehouseType(InOutTypeEnum.TAKE_STOCK);
        storageInventoryBatch.setInWarehouseTime(LocalDateTime.now());
        storageInventoryBatch.setBatchCode(DateUtil.format(new Date(), DateTimeFormatter.ofPattern("yyyyMMdd")));
        storageInventoryBatch.setRemWarehouseNumber(storageInventory.getSumWarehouseNumber());
        storageInventoryBatch.setSumWarehouseNumber(storageInventory.getSumWarehouseNumber());
        storageInventoryBatch.setPrice(storageInventory.getAveragePrice());
        storageInventoryBatch.setCreatedAt(storageInventory.getCreatedAt());
        storageInventoryBatch.setUpdatedAt(storageInventory.getUpdatedAt());
        storageInventoryBatch.setPrice(storageInventory.getAveragePrice());
        storageInventoryBatchServiceDomain.save(storageInventoryBatch);

        StorageWarehouseFlow storageWarehouseFlow = new StorageWarehouseFlow();
        storageWarehouseFlow.setCode(storageInventory.getCode());
        storageWarehouseFlow.setFlowId(flowId);
        storageWarehouseFlow.setWarehouseId(storageInventory.getWarehouseId());
        storageWarehouseFlow.setBatchCode(storageInventoryBatch.getBatchCode());
        storageWarehouseFlow.setTime(storageInventory.getCreatedAt());
        storageWarehouseFlow.setInOutType(1);
        storageWarehouseFlow.setType(InOutTypeEnum.TAKE_STOCK);
        storageWarehouseFlow.setNumber(storageInventory.getSumWarehouseNumber());
        storageWarehouseFlow.setOperatorId(ApplicationSessions.id());
        storageWarehouseFlow.setCreatedAt(storageInventory.getCreatedAt());
        storageWarehouseFlow.setUpdatedAt(storageInventory.getUpdatedAt());
        storageWarehouseFlowServiceDomain.save(storageWarehouseFlow);
    }

}
