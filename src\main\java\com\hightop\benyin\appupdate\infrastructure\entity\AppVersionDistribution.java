package com.hightop.benyin.appupdate.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 应用版本分发关系实体
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("b_app_version_distribution")
@ApiModel("应用版本分发关系")
public class AppVersionDistribution {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    Long id;
    
    @TableField("version_id")
    @ApiModelProperty("版本ID")
    Long versionId;
    
    @TableField("target_type")
    @ApiModelProperty("目标类型：USER-用户，DEVICE-设备，GROUP-用户组")
    String targetType;
    
    @TableField("target_id")
    @ApiModelProperty("目标ID")
    String targetId;
    
    @TableField("target_name")
    @ApiModelProperty("目标名称")
    String targetName;
    
    @TableField("assign_time")
    @ApiModelProperty("分配时间")
    LocalDateTime assignTime;
    
    @TableField("is_active")
    @ApiModelProperty("是否激活")
    Boolean isActive;
    
    @TableField("created_by")
    @ApiModelProperty("创建人")
    String createdBy;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;
    
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("逻辑删除标志")
    Integer deleted;
}
