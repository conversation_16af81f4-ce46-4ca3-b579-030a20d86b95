package com.hightop.benyin.storage.api.controller;

import com.hightop.benyin.storage.api.dto.query.StorageOutWarehouseQuery;
import com.hightop.benyin.storage.application.service.StorageOutWarehouseService;
import com.hightop.benyin.storage.infrastructure.entity.StorageInventoryBatch;
import com.hightop.benyin.storage.infrastructure.entity.StorageOutWarehouse;
import com.hightop.benyin.storage.infrastructure.entity.StorageOutWarehouseGoods;
import com.hightop.benyin.storage.infrastructure.entity.StorageWarehouseFlow;
import com.hightop.benyin.storage.infrastructure.enums.OutStatusEnum;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 仓储管理-出库管理rest接口
 * <AUTHOR>
 * @date 2023-11-03 16:26:37
 */
@RequestMapping("/storage-out-warehouse")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "仓储管理-出库管理")
public class StorageOutWarehouseController {
    StorageOutWarehouseService storageOutWarehouseService;

    @GetMapping("/out-status-list")
    @ApiOperation("出库状态下拉")
    public RestResponse<List<OutStatusEnum>> outStatusList() {
        return RestResponse.ok(Stream.of(OutStatusEnum.values()).collect(Collectors.toList()));
    }

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public RestResponse<List<StorageOutWarehouse>> list() {
        return RestResponse.ok(this.storageOutWarehouseService.list());
    }

    @GetMapping("/page")
    @ApiOperation("分页查询")
    public RestResponse<DataGrid<StorageOutWarehouse>> page(StorageOutWarehouseQuery pageQuery) {
        return RestResponse.ok(this.storageOutWarehouseService.page(pageQuery));
    }

    @GetMapping("/getOne")
    @ApiOperation("根据出库单id查询数据")
    public RestResponse<StorageOutWarehouse> getOne(String id) {
        return RestResponse.ok(this.storageOutWarehouseService.getOne(id));
    }

    @PutMapping
    @ApiOperation("审核")
    public RestResponse<Void> updateStatus(@Validated @RequestBody StorageOutWarehouseGoods item) {
        return Operation.UPDATE.response(this.storageOutWarehouseService.updateStatus(item));
    }

    @PutMapping("/update")
    @ApiOperation("修改")
    public RestResponse<Void> updateData(@RequestBody StorageOutWarehouse storageOutWarehouse) {
        return Operation.UPDATE.response(this.storageOutWarehouseService.updateData(storageOutWarehouse));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") String id) {
        return Operation.DELETE.response(this.storageOutWarehouseService.removeById(id));
    }

    @GetMapping("/batchList")
    @ApiOperation("批次号下拉")
    public RestResponse<List<StorageInventoryBatch>> batchList(@RequestParam @ApiParam("code") String code,
                                                               @RequestParam @ApiParam("warehouseId") String warehouseId,
                                                               @RequestParam(required = false) @ApiParam("batchCode") String batchCode) {
        return RestResponse.ok(this.storageOutWarehouseService.batchList(code, warehouseId, batchCode));
    }

    @GetMapping("/goodsOutWareDetail/{id}")
    @ApiOperation("审核明细")
    public RestResponse<List<StorageWarehouseFlow>> goodsInWareDetail(@PathVariable @ApiParam("id") Long id) {
        return RestResponse.ok(this.storageOutWarehouseService.goodsInWareDetail(id));
    }

    @PutMapping("/update/{id}")
    @ApiOperation("出库单修改备注")
    public RestResponse<Void> updateRemark(@PathVariable Long id, @RequestBody String remark) {
        return Operation.UPDATE.response(this.storageOutWarehouseService.updateRemarks(id, remark));
    }

    /*****************************移动端-出库帮助******************************/


    @PostMapping("/pageWechart")
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<StorageOutWarehouse>> pageWechart(@RequestBody StorageOutWarehouseQuery pageQuery) {
        return RestResponse.ok(this.storageOutWarehouseService.pageWechart(pageQuery));
    }

}
