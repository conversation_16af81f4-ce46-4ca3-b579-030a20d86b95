package com.hightop.benyin.purchase.api.dto.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.purchase.infrastructure.enums.ManufactureOrderStatusEnum;
import com.hightop.benyin.purchase.infrastructure.enums.ManufactureReturnStatusEnum;
import com.hightop.benyin.purchase.infrastructure.enums.ReceiveStatusEnum;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购表
 *
 * <AUTHOR>
 * @date 2023-12-06 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("供应商订单表查询DTO")
public class ManufacterOrderQuery extends PageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("状态")
    ManufactureOrderStatusEnum status;


    @ApiModelProperty("订单号")
    String code;

    @ApiModelProperty("公司编码")
    String companyCode;

    @ApiModelProperty("采购公司")
    String receiveCompany;

    @ApiModelProperty("采购单编号")
    String purchaseCode;

    @ApiModelProperty("供应商发货单编号")
    String manufacturerDeliveryCode;

    @ApiModelProperty("供应商")
    String manufacturerName;

    @ApiModelProperty("供应商订单code")
    private String manufacturerOrderCode;

    @ApiModelProperty("结算方式")
    String settleMethod;

    @ApiModelProperty("采购人")
    String initiatorName;

    @ApiModelProperty("采购人电话")
    String initiatorPhone;

    @ApiModelProperty("期望发货时间-开始")
    String deliveryTimeStart;

    @ApiModelProperty("期望发货时间-结束")
    String deliveryTimeEnd;

    @ApiModelProperty("开票状态0未开票1已开票")
    private Integer invoiceStatus;

    @ApiModelProperty("结算状态")
    private String settleStatus;

    @ApiModelProperty("退货状态多选")
    private List<ManufactureReturnStatusEnum> refundStatus;

    @ApiModelProperty("收货人")
    private Long receiveBy;


    @ApiModelProperty("收货状态")
    private ReceiveStatusEnum receiveStatus;
    @ApiModelProperty("物流方式")
    private String trackingType;
    @ApiModelProperty("物流单号")
    private String trackingNumber;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("收货人")
    private String receiveByName;

    @ApiModelProperty("收货时间-开始")
    String receiveTimeStart;

    @ApiModelProperty("收货时间-结束")
    String receiveTimeEnd;

    @ApiModelProperty("退货人ID")
    private Long createdByID;
    
    @ApiModelProperty("退货人姓名")
    private String createdBy;

    @ApiModelProperty("退货日期-开始")
    String startDate;

    @ApiModelProperty("退货日期-结束")
    String endDate;

}
