package com.hightop.benyin.logcontrol.infrastructure.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 日志控制模块配置
 * <AUTHOR>
 * @date 2025-01-21
 */
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "logcontrol")
public class LogControlConfig {

    /**
     * 是否启用日志控制模块
     */
    private boolean enabled = true;

    /**
     * 默认日志级别
     */
    private String defaultLogLevel = "INFO";

    /**
     * 默认位置日志间隔（秒）
     */
    private int defaultLocationLogInterval = 300;

    /**
     * 默认日志上传间隔（秒）
     */
    private int defaultLogUploadInterval = 3600;

    /**
     * 默认最大日志文件数量
     */
    private int defaultMaxLogFiles = 5;

    /**
     * 数据清理保留天数
     */
    private int dataRetentionDays = 90;

    /**
     * 批量操作大小
     */
    private int batchSize = 100;

    /**
     * 查询结果限制数量
     */
    private int queryLimit = 1000;

    /**
     * 缓存过期时间（秒）
     */
    private int cacheExpireSeconds = 3600;

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDefaultLogLevel() {
        return defaultLogLevel;
    }

    public void setDefaultLogLevel(String defaultLogLevel) {
        this.defaultLogLevel = defaultLogLevel;
    }

    public int getDefaultLocationLogInterval() {
        return defaultLocationLogInterval;
    }

    public void setDefaultLocationLogInterval(int defaultLocationLogInterval) {
        this.defaultLocationLogInterval = defaultLocationLogInterval;
    }

    public int getDefaultLogUploadInterval() {
        return defaultLogUploadInterval;
    }

    public void setDefaultLogUploadInterval(int defaultLogUploadInterval) {
        this.defaultLogUploadInterval = defaultLogUploadInterval;
    }

    public int getDefaultMaxLogFiles() {
        return defaultMaxLogFiles;
    }

    public void setDefaultMaxLogFiles(int defaultMaxLogFiles) {
        this.defaultMaxLogFiles = defaultMaxLogFiles;
    }

    public int getDataRetentionDays() {
        return dataRetentionDays;
    }

    public void setDataRetentionDays(int dataRetentionDays) {
        this.dataRetentionDays = dataRetentionDays;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public int getQueryLimit() {
        return queryLimit;
    }

    public void setQueryLimit(int queryLimit) {
        this.queryLimit = queryLimit;
    }

    public int getCacheExpireSeconds() {
        return cacheExpireSeconds;
    }

    public void setCacheExpireSeconds(int cacheExpireSeconds) {
        this.cacheExpireSeconds = cacheExpireSeconds;
    }
}
