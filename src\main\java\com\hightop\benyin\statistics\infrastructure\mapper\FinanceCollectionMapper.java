package com.hightop.benyin.statistics.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.statistics.api.dto.FinanceCollectionQuery;
import com.hightop.benyin.statistics.api.dto.FinancePayQuery;
import com.hightop.benyin.statistics.application.vo.*;
import com.hightop.benyin.statistics.infrastructure.entity.FinanceCollection;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应收账款统计表mapper
 *
 * <AUTHOR>
 * @date 2024-06-25 16:29:01
 */
public interface FinanceCollectionMapper extends MPJBaseMapper<FinanceCollection> {

    /**
     * 获取当月应收账款数据
     *
     * @param query 查询条件
     * @return
     */
    public List<FinanceCollection> getCurrMonthFinanceCollection(@Param("qo") FinancePayQuery query);


    /**
     * 获取当月应收账款数据
     *
     * @param query 查询条件
     * @return
     */
    public List<FinanceCollection> getFinanceCollection(@Param("qo") FinancePayQuery query);


    /**
     * 应收款机器明细
     *
     * @param query 查询条件
     * @return
     */
    public List<MachineCollectionVO> machineCollectionList(@Param("qo") FinanceCollectionQuery query);


    /**
     * 应收款耗材明细
     *
     * @param query 查询条件
     * @return
     */
    public List<MaterialCollectionVO> materialCollectionList(@Param("qo") FinanceCollectionQuery query);

    /**
     * 应收款抄表费用明细
     *
     * @param query 查询条件
     * @return
     */
    public List<OperationCollectionVO> operationCollectionList(@Param("qo") FinanceCollectionQuery query);

    /**
     * 应收款维修费用明细
     *
     * @param query 查询条件
     * @return
     */
    public List<RepairCollectionVO> repairCollectionList(@Param("qo") FinanceCollectionQuery query);

    /**
     * 获取当月新增应收账款数据
     *
     * @param yearMonth 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCurrMonthNewCollection(@Param("yearMonths") String yearMonth, @Param("customerIds") List<Long> customerIds);

    /**
     * 获取当月已收账款数据
     *
     * @param yearMonth 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCurrMonthPayCollection(@Param("lastMonths") String lastMonths, @Param("yearMonths") String yearMonth, @Param("customerIds") List<Long> customerIds);


    /**
     * 应收款耗材汇总和维修工单中换的零件
     * @param yearMonths 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCustomerOrderSummary(@Param("yearMonths") String yearMonths, @Param("customerIds") List<Long> customerIds);

    /**
     * 应收款机器汇总
     * @param yearMonths 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCustomerMachineSummary(@Param("yearMonths") String yearMonths, @Param("customerIds") List<Long> customerIds);

    /**
     * 应收款工单中的人工费用汇总
     * @param yearMonths 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCustomerRepairSummary(@Param("yearMonths") String yearMonths, @Param("customerIds") List<Long> customerIds);

    /**
     * 应收款抄表费用汇总
     * @param yearMonths 查询条件
     * @param customerIds 查询条件
     * @return
     */
    public List<FinanceCustomerSummaryVO> getCustomerOperationSummary(@Param("yearMonths") String yearMonths, @Param("customerIds") List<Long> customerIds);

}
