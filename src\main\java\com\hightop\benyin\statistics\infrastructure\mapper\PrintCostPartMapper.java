package com.hightop.benyin.statistics.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.statistics.api.dto.RepairReportQuery;
import com.hightop.benyin.statistics.infrastructure.entity.PrintCostPart;
import com.hightop.magina.standard.task.log.JobLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 零件打印成本统计mapper
 *
 * <AUTHOR>
 * @date 2024-06-05 16:29:01
 */
public interface PrintCostPartMapper extends MPJBaseMapper<PrintCostPart> {

    /**
     * 查询机器基础耗材成本数据
     * @param productId
     * @return
     */
    @Select("select t.product_id,t.part_id,tss.id sale_sku_id,t.ch part_name,t.corrected_lifespan lifespan, tss.sale_unit_price, t.position" +
            ",t.position, t3.name brand,t2.name machine from b_product_part_bom t join b_storage_article sa on sa.part_id = t.part_id join benyin.tb_sale_sku tss on sa.part_id = tss.part_id and sa.code = tss.article_code " +
            "left join b_product_tree t2 on t2.id = t.product_id left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)" +
            " where t.product_id =#{productId} and t.is_pm = 1 and t.deleted = false and tss.deleted = false")
    public List<PrintCostPart> getBasePrintCostPart(@Param("productId") Long productId);

    @Select("SELECT t1.* " +
            "FROM st_job_log t1 " +
            "INNER JOIN (" +
            "    SELECT MAX(id) id " +
            "    FROM st_job_log " +
            "    WHERE triggered_at < NOW() " +
            "    AND state = 'pending' " +
            "    GROUP BY job_id " +
            ") t2 ON t1.id = t2.id " +
            "WHERE t1.state = 'pending' " +
            "ORDER BY t1.triggered_at DESC ")
    List<JobLog> getAllotedLogs(LocalDateTime now);
}


