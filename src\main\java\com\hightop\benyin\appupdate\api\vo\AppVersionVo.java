package com.hightop.benyin.appupdate.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 应用版本VO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel("应用版本")
public class AppVersionVo {
    
    @ApiModelProperty("主键ID")
    Long id;
    
    @ApiModelProperty("版本名称")
    String versionName;
    
    @ApiModelProperty("版本号")
    Integer versionCode;
    
    @ApiModelProperty("APK文件名")
    String apkFileName;
    
    @ApiModelProperty("文件大小")
    Long fileSize;
    
    @ApiModelProperty("文件MD5")
    String fileMd5;
    
    @ApiModelProperty("更新说明")
    String updateLog;
    
    @ApiModelProperty("是否强制更新")
    Boolean isForce;
    
    @ApiModelProperty("管理员强制标志")
    Boolean adminForce;
    
    @ApiModelProperty("是否启用")
    Boolean isActive;
    
    @ApiModelProperty("下载次数")
    Integer downloadCount;
    
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;
    
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;
}
