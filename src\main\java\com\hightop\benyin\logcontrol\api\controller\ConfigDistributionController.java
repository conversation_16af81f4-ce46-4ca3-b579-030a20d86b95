package com.hightop.benyin.logcontrol.api.controller;

import com.hightop.benyin.logcontrol.application.service.ConfigDistributionService;
import com.hightop.benyin.logcontrol.application.service.LogConfigService;
import com.hightop.fario.base.web.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 配置分发关系管理控制器
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@RestController
@RequestMapping("/logcontrol/config/distribution")
@Api(tags = "配置分发关系管理")
public class ConfigDistributionController {

    @Autowired
    private ConfigDistributionService configDistributionService;

    @Autowired
    private LogConfigService logConfigService;

    /**
     * 更新分发关系的激活状态
     */
    @PutMapping("/{distributionId}/status")
    @ApiOperation("更新分发关系激活状态")
    public RestResponse<Void> updateActiveStatus(
            @PathVariable Long distributionId,
            @RequestBody Map<String, Boolean> request) {
        
        try {
            Boolean isActive = request.get("isActive");
            if (isActive == null) {
                log.warn("更新分发关系激活状态失败，参数isActive为空");
                return RestResponse.ok(null);
            }

            boolean success = configDistributionService.updateActiveStatus(distributionId, isActive);
            if (success) {
                log.info("分发关系激活状态更新成功，ID: {}, 状态: {}", distributionId, isActive);
                return RestResponse.ok(null);
            } else {
                log.warn("分发关系激活状态更新失败，ID: {}", distributionId);
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("更新分发关系激活状态异常，ID: {}", distributionId, e);
            return RestResponse.ok(null);
        }
    }

    /**
     * 更新分发关系的配置ID
     */
    @PutMapping("/{distributionId}/config")
    @ApiOperation("更新分发关系的配置")
    public RestResponse<Void> updateConfigId(
            @PathVariable Long distributionId,
            @RequestBody Map<String, Long> request) {
        
        try {
            Long configId = request.get("configId");
            if (configId == null) {
                log.warn("更新分发关系配置失败，参数configId为空");
                return RestResponse.ok(null);
            }

            // 验证新配置是否存在
            if (logConfigService.getConfigById(configId) == null) {
                log.warn("更新分发关系配置失败，配置不存在，ID: {}", configId);
                return RestResponse.ok(null);
            }

            boolean success = configDistributionService.updateConfigId(distributionId, configId);
            if (success) {
                log.info("分发关系配置更新成功，ID: {}, 新配置ID: {}", distributionId, configId);
                return RestResponse.ok(null);
            } else {
                log.warn("分发关系配置更新失败，ID: {}", distributionId);
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("更新分发关系配置异常，ID: {}", distributionId, e);
            return RestResponse.ok(null);
        }
    }

    /**
     * 根据ID删除分发关系
     */
    @DeleteMapping("/{distributionId}")
    @ApiOperation("删除分发关系")
    public RestResponse<Void> deleteDistribution(@PathVariable Long distributionId) {
        try {
            boolean success = configDistributionService.removeDistribution(distributionId);
            if (success) {
                log.info("分发关系删除成功，ID: {}", distributionId);
                return RestResponse.ok(null);
            } else {
                log.warn("分发关系删除失败，ID: {}", distributionId);
                return RestResponse.ok(null);
            }
        } catch (Exception e) {
            log.error("删除分发关系异常，ID: {}", distributionId, e);
            return RestResponse.ok(null);
        }
    }
}
