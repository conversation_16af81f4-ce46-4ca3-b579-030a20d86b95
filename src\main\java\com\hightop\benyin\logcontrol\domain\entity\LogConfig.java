package com.hightop.benyin.logcontrol.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 日志配置实体（简化版）
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("b_log_config")
public class LogConfig {

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 日志级别
     */
    @TableField("log_level")
    private String logLevel;

    /**
     * 是否启用位置日志
     */
    @TableField("enable_location_log")
    private Boolean enableLocationLog;

    /**
     * 位置日志间隔(秒)
     */
    @TableField("location_log_interval")
    private Integer locationLogInterval;

    /**
     * 上传间隔(秒)
     */
    @TableField("log_upload_interval")
    private Integer logUploadInterval;

    /**
     * 最大日志文件数量
     */
    @TableField("max_log_files")
    private Integer maxLogFiles;

    /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;

    /**
     * 是否激活
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
