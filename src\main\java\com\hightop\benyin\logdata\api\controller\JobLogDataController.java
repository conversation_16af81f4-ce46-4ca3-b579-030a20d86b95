package com.hightop.benyin.logdata.api.controller;

import com.hightop.benyin.logdata.application.service.JobLogDataService;
import com.hightop.benyin.logistics.application.vo.JdTraceNotifyVo;
import com.hightop.benyin.logistics.infrastructure.restful.jd.JdConstants;
import com.hightop.benyin.logistics.infrastructure.restful.jd.JdResponse;
import com.hightop.benyin.statistics.api.dto.FinancePayQuery;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.annotation.Anonymous;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 定时任务日志备份
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RequestMapping("/jobLogData")
@Api(tags = "定时任务日志备份")
public class JobLogDataController {

    JobLogDataService jobLogDataService;

    @GetMapping("/everyDaySyncJobLogDataTask")
    @IgnoreOperationLog
    @Anonymous
    public RestResponse<Void> everyDaySyncJobLogDataTask(@RequestParam("day") String day) {
        Boolean b = jobLogDataService.everyDaySyncJobLogDataTask(day);
        if (!b) {
            return new RestResponse<>(500, "同步失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("同步完成！");
    }
}
