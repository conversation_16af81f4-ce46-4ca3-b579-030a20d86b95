package com.hightop.benyin.storage.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.items.store.domain.service.ApplyOrderServiceDomain;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyOrder;
import com.hightop.benyin.logistics.infrastructure.model.LogisticsContact;
import com.hightop.benyin.order.application.manager.TradeOrderManager;
import com.hightop.benyin.order.domain.service.TradeOrderDetailDomainService;
import com.hightop.benyin.order.domain.service.TradeOrderDomainService;
import com.hightop.benyin.order.infrastructure.entity.TradeOrder;
import com.hightop.benyin.order.infrastructure.entity.TradeOrderDetail;
import com.hightop.benyin.purchase.infrastructure.entity.ManufacturerReturn;
import com.hightop.benyin.purchase.infrastructure.entity.ManufacturerReturnGoods;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.type.LbsLocation;
import com.hightop.benyin.storage.api.dto.query.StorageOutWarehouseQuery;
import com.hightop.benyin.storage.domain.event.InventoryAlarmEvent;
import com.hightop.benyin.storage.domain.service.*;
import com.hightop.benyin.storage.infrastructure.constant.StorageConstants;
import com.hightop.benyin.storage.infrastructure.entity.*;
import com.hightop.benyin.storage.infrastructure.enums.InOutTypeEnum;
import com.hightop.benyin.storage.infrastructure.enums.OutStatusEnum;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓储管理-出库管理服务
 *
 * <AUTHOR>
 * @date 2023-11-03 16:26:37
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class StorageOutWarehouseService {

    StorageOutWarehouseServiceDomain storageOutWarehouseServiceDomain;

    /**
     * 出库管理-商品信息
     */
    StorageOutWarehouseGoodsServiceDomain storageOutWarehouseGoodsServiceDomain;

    /**
     * 流水记录
     */
    StorageWarehouseFlowServiceDomain storageWarehouseFlowServiceDomain;

    /**
     * 批次记录
     */
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;

    /**
     * 序列号服务
     */
    SequenceDomainService sequenceDomainService;

    /**
     * 库品主表
     */
    StorageInventoryServiceDomain storageInventoryServiceDomain;

    /**
     * 仓库服务
     */
    WarehouseServiceDomain warehouseServiceDomain;

    /**
     * 订单服务
     */
    TradeOrderDomainService tradeOrderDomainService;
    /**
     * 订单明细服务
     */
    TradeOrderDetailDomainService tradeOrderDetailDomainService;

    /**
     * 订单服务2
     */
    TradeOrderManager tradeOrderManager;

    StorageArticleServiceDomain storageArticleServiceDomain;

    /**
     * 领料单服务
     */
    ApplyOrderServiceDomain applyOrderServiceDomain;

    /**
     * 预发货单
     */
    AdvanceInvoiceServiceDomain advanceInvoiceServiceDomain;
    AdvanceInvoiceDetailServiceDomain advanceInvoiceDetailServiceDomain;

    CustomerDomainService customerDomainService;
    ApplicationEventPublisher applicationEventPublisher;

    /**
     * 仓储管理-出库管理列表查询
     *
     * @return {@link List}
     */
    public List<StorageOutWarehouse> list() {
        return this.storageOutWarehouseServiceDomain.list();
    }

    /**
     * 仓储管理-出库管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<StorageOutWarehouse> page(StorageOutWarehouseQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.storageOutWarehouseServiceDomain.selectJoinList(StorageOutWarehouse.class,
                MPJWrappers.lambdaJoin().selectAll(StorageOutWarehouse.class)
                        .leftJoin(ApplyOrder.class, ApplyOrder::getCode, StorageOutWarehouse::getShopWaybill)
                        .leftJoin(UserBasic.class, UserBasic::getId, ApplyOrder::getEngineerId)
                        .selectAs(UserBasic::getName, StorageOutWarehouse::getConsignee)
                        .eq(Objects.nonNull(pageQuery.getOutType()), StorageOutWarehouse::getOutType, pageQuery.getOutType())
                        .eq(Objects.nonNull(pageQuery.getOutStatus()), StorageOutWarehouse::getOutStatus, pageQuery.getOutStatus())
                        .like(
                                StringUtils.isNotBlank(pageQuery.getMainWaybill()), StorageOutWarehouse::getMainWaybill,
                                pageQuery.getMainWaybill()
                        )
                        .like(
                                StringUtils.isNotBlank(pageQuery.getShopWaybill()), StorageOutWarehouse::getShopWaybill,
                                pageQuery.getShopWaybill()
                        )
                        .like(
                                StringUtils.isNotBlank(pageQuery.getSecondlyWaybill()), StorageOutWarehouse::getSecondlyWaybill,
                                pageQuery.getSecondlyWaybill()
                        ).like(
                                StringUtils.isNotBlank(pageQuery.getConsignee()), UserBasic::getName,
                                pageQuery.getConsignee()
                        )
                        .eq(
                                StringUtils.isNotBlank(pageQuery.getOutWarehouseId()), StorageOutWarehouse::getOutWarehouseId,
                                pageQuery.getOutWarehouseId()
                        )
                        .orderByDesc(StorageOutWarehouse::getCreatedAt))
        ).peek(p -> {
            Warehouse warehouse = this.warehouseServiceDomain.getById(p.getWarehouseId());
            Optional.of(warehouse).ifPresent(i -> {
                p.setWarehouseName(i.getName());
            });
        });
    }

    /**
     * 审核
     *
     * @param goods
     * @return true/false
     * <p>
     * StorageInventoryBatch 和仓储的入库单单 批次编号
     * <p>
     * 修改
     * 出库商品     StorageOutWarehouseGoods
     * 出库行为     StorageOutWarehouse
     * 出库批次      StorageInventoryBatch  数量扣减
     * 库存数量   出库增加 -- 库存减少
     * 处理库流水    StorageWarehouseFlow 登记
     * 处理预发货    AdvanceInvoice   数量处理
     * 处理预发货明细 AdvanceInvoiceDetail  数量处理
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(StorageOutWarehouseGoods goods) {
        //出库商品信息
        StorageOutWarehouseGoods existGoods = this.storageOutWarehouseGoodsServiceDomain.getById(goods.getId());
        // 存在的出库数量-审核实际出库数量
        int actual = existGoods.getAuidtOutWarehouseNumber() + goods.getAuidtOutWarehouseNumber();
        if (existGoods.getOutWarehouseNumber() - existGoods.getCancelOutWarehouseNumber() < actual) {
            int total = existGoods.getOutWarehouseNumber() - existGoods.getCancelOutWarehouseNumber() - existGoods.getAuidtOutWarehouseNumber();
            throw new MaginaException("审核出库数量不能大于可出库数量" + total);
        }
        // 库品批次信息
        StorageInventoryBatch inWarehouseRecord = this.storageInventoryBatchServiceDomain.lambdaQuery()
                .eq(StorageInventoryBatch::getId, goods.getBatchId())
                .one();
        if (Objects.isNull(inWarehouseRecord)) {
            throw new MaginaException("未找到对应批次,请先完成入库!");
        }
        //如果该批次下剩余库品数量小于审核出库数量，则没有库存可出，需要先入库
        if ((inWarehouseRecord.getRemWarehouseNumber() - goods.getAuidtOutWarehouseNumber()) < 0) {
            throw new MaginaException("库存数不够,请先入库!");
        }
        // 出库单信息
        StorageOutWarehouse outWarehouse = this.storageOutWarehouseServiceDomain.lambdaQuery()
                .eq(StorageOutWarehouse::getOutWarehouseId, existGoods.getOutWarehouseId())
                .one();
        //库品信息
        StorageInventory storageInventory = this.storageInventoryServiceDomain.lambdaQuery()
                .eq(StorageInventory::getId, existGoods.getInventoryId())
                .eq(StorageInventory::getWarehouseId, existGoods.getWarehouseId())
                .one();
        Integer auditOutNumber = outWarehouse.getAuidtOutWarehouseNumber() + goods.getAuidtOutWarehouseNumber() + existGoods.getCancelOutWarehouseNumber();
        //出库主单的总审核出库数量+每次库品审核出库数量=出库单应出库数量?已出库:部分出库
        OutStatusEnum status = Objects.equals(outWarehouse.getOutWarehouseNumber(), auditOutNumber)
                || outWarehouse.getOutWarehouseNumber().intValue() <= auditOutNumber.intValue()
                ? OutStatusEnum.YCK : OutStatusEnum.BFCK;
        outWarehouse.setOutStatus(status);
        // 设置审核出库数量
        existGoods.setAuidtOutWarehouseNumber(actual);
        if (existGoods.getAuidtOutWarehouseNumber() < existGoods.getOutWarehouseNumber() - existGoods.getCancelOutWarehouseNumber()) {
            existGoods.setOutStatus(OutStatusEnum.BFCK);
        }
        if (existGoods.getAuidtOutWarehouseNumber() == existGoods.getOutWarehouseNumber() - existGoods.getCancelOutWarehouseNumber()) {
            existGoods.setOutStatus(OutStatusEnum.YCK);
        }
        // 出库库品表修改已出库数量
        this.storageOutWarehouseGoodsServiceDomain.addAuidtOutNumber(existGoods);

        // 库品批次剩余库存扣除
        this.storageInventoryBatchServiceDomain.subtractSumNumber(
                goods.getBatchId(), goods.getAuidtOutWarehouseNumber());
        //库品表-出库量增加
        storageInventory.setOutWarehouseNumber(storageInventory.getOutWarehouseNumber() + goods.getAuidtOutWarehouseNumber());
//        this.storageInventoryServiceDomain.addOutNumber(storageInventory, goods.getAuidtOutWarehouseNumber());
        //库品表-库存量减少
//        this.storageInventoryServiceDomain.subtractSumNumber(storageInventory, goods.getAuidtOutWarehouseNumber());
        storageInventory.setSumWarehouseNumber(storageInventory.getSumWarehouseNumber() - goods.getAuidtOutWarehouseNumber());
        storageInventoryServiceDomain.updateById(storageInventory);

        //出库单主表修改已出库数量、出库状态
        long count = storageOutWarehouseGoodsServiceDomain.lambdaQuery()
                .eq(StorageOutWarehouseGoods::getOutWarehouseId, goods.getOutWarehouseId())
                .ne(StorageOutWarehouseGoods::getId, goods.getId())
                .notIn(StorageOutWarehouseGoods::getOutStatus, Lists.newArrayList(OutStatusEnum.YCK, OutStatusEnum.GB))
                .count();
        if (count == 0L && existGoods.getOutStatus().equals(OutStatusEnum.YCK)) {
            outWarehouse.setOutStatus(OutStatusEnum.YCK);
        }
        outWarehouse.setAuidtOutWarehouseNumber(outWarehouse.getAuidtOutWarehouseNumber() + goods.getAuidtOutWarehouseNumber());
        storageOutWarehouseServiceDomain.updateById(outWarehouse);

        //流水记录
        StorageWarehouseFlow flow = new StorageWarehouseFlow();
        flow.setInOutType(StorageConstants.OUTBOUND);
        flow.setFlowId(existGoods.getOutWarehouseId());
        flow.setBatchCode(goods.getBatchCode());
        flow.setCode(storageInventory.getCode());
        flow.setName(storageInventory.getName());
        flow.setWarehouseId(existGoods.getWarehouseId());
        flow.setNumber(goods.getAuidtOutWarehouseNumber());
        flow.setTime(LocalDateTime.now());
        // 批次记录出库类型
        flow.setType(outWarehouse.getOutType());
        flow.setOperatorId(ApplicationSessions.id());
        storageWarehouseFlowServiceDomain.save(flow);

        //采购退货不发货 只出库
        if (outWarehouse.getOutType().equals(InOutTypeEnum.PURCHASE_RETURN)) {
            return Boolean.TRUE;
        }
        if (outWarehouse.getOutType().equals(InOutTypeEnum.WITHOUT)) {
            return Boolean.TRUE;
        }
        //预发货单逻辑=====
        AdvanceInvoice invoice = advanceInvoiceServiceDomain.lambdaQuery()
                .eq(AdvanceInvoice::getOutboundOrderCode, existGoods.getOutWarehouseId())
                .one();
        invoice.setExpectedNumber(invoice.getExpectedNumber() + goods.getAuidtOutWarehouseNumber());
        advanceInvoiceServiceDomain.updateById(invoice);
        AdvanceInvoiceDetail invoiceDetail = advanceInvoiceDetailServiceDomain.lambdaQuery()
                .eq(AdvanceInvoiceDetail::getAdvanceInvoiceId, invoice.getId())
                .eq(AdvanceInvoiceDetail::getArticleCode, goods.getInventoryCode())
                .one();
        advanceInvoiceDetailServiceDomain.lambdaUpdate()
                .set(
                        AdvanceInvoiceDetail::getExpectedNumber,
                        invoiceDetail.getExpectedNumber() + goods.getAuidtOutWarehouseNumber()
                )
                .eq(AdvanceInvoiceDetail::getAdvanceInvoiceId, invoice.getId())
                .eq(AdvanceInvoiceDetail::getArticleCode, goods.getInventoryCode())
                .update();


        // 事务提交时发布回调事件
        ExecutorUtils.doAfterCommit(
                () ->
                        this.applicationEventPublisher.publishEvent(
                                new InventoryAlarmEvent(storageInventory.getCode(), storageInventory.getWarehouseId())
                        )
        );
        return Boolean.TRUE;
    }

    /**
     * 仓储管理-出库管理删除
     *
     * @param id id
     * @return true/false
     */
    public boolean removeById(String id) {
        return this.storageOutWarehouseServiceDomain.removeById(id);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    public StorageOutWarehouse getOne(String id) {
        LambdaQueryWrapper<StorageOutWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageOutWarehouse::getId, id);
        StorageOutWarehouse outWarehouse = storageOutWarehouseServiceDomain.getOne(wrapper);

        //查询订单商品信息
        LambdaQueryWrapper<StorageOutWarehouseGoods> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(StorageOutWarehouseGoods::getOutWarehouseId, outWarehouse.getOutWarehouseId());
        List<StorageOutWarehouseGoods> itemList = storageOutWarehouseGoodsServiceDomain.list(itemWrapper);
        for (StorageOutWarehouseGoods item : itemList) {
            LambdaQueryWrapper<StorageInventory> storageInventoryWrapper = new LambdaQueryWrapper<>();
            storageInventoryWrapper.eq(StorageInventory::getId, item.getInventoryId());
            StorageInventory storageInventory = storageInventoryServiceDomain.getOne(storageInventoryWrapper);
            item.setInventoryCode(storageInventory.getCode());
            item.setInventoryName(storageInventory.getName());
            item.setLocation(storageInventory.getLocation());
            StorageArticle storageArticle = storageArticleServiceDomain.getOne(
                    new LambdaQueryWrapper<StorageArticle>().eq(StorageArticle::getCode, storageInventory.getCode()));
            item.setStorageArticle(storageArticle);
            item.setManufacturerGoodsName(storageArticle.getManufacturerGoodsName());
            item.setManufacturerGoodsCode(storageArticle.getManufacturerGoodsCode());
        }

        outWarehouse.setItemList(itemList);

        ////查询-出库货品
        //LambdaQueryWrapper<StorageOutWarehouseGoods> wrapper2 = new LambdaQueryWrapper<>();
        //wrapper2.eq(StorageOutWarehouseGoods::getOutWarehouseId, outWarehouse.getOutWarehouseId());
        //List<StorageOutWarehouseGoods> list = storageOutWarehouseGoodsServiceDomain.list(wrapper2);
        //for (StorageOutWarehouseGoods goods : list) {
        //    //物品基本信息
        //    LambdaQueryWrapper<StorageInventoryBatch> goodsWrapper = new LambdaQueryWrapper<>();
        //    goodsWrapper.eq(StorageInventoryBatch::getId, goods.getBatchId());
        //    StorageInventoryBatch inWarehouseRecord = storageInventoryBatchServiceDomain.getOne(goodsWrapper);
        //    goods.setInWarehouseTime(inWarehouseRecord.getInWarehouseTime());
        //    //goods.setSumWarehouseNumber(inWarehouseRecord.getRemWarehouseNumber());
        //    StorageArticle storageArticle = storageArticleServiceDomain.getOne(new LambdaQueryWrapper<StorageArticle>().eq(StorageArticle::getCode, goods.getCode()).last(" limit 1 "));
        //    goods.setStorageArticle(storageArticle);
        //}
        //outWarehouse.setGoodsDataList(list);
        return outWarehouse;
    }

    /**
     * 修改
     *
     * @param storageOutWarehouse
     * @return
     */
    public boolean updateData(StorageOutWarehouse storageOutWarehouse) {
        boolean update = this.storageOutWarehouseServiceDomain.updateById(storageOutWarehouse);
        ////出库货品:先删除再保存
        //storageOutWarehouseGoodsServiceDomain.remove(Wrappers.<StorageOutWarehouseGoods>query().lambda().eq(StorageOutWarehouseGoods::getOutWarehouseId, storageOutWarehouse.getId()));
        //List<StorageOutWarehouseGoods> goodsDataList = storageOutWarehouse.getGoodsDataList();
        //if (goodsDataList.size() > 0) {
        //    ArrayList<StorageOutWarehouseGoods> list = new ArrayList<>();
        //    for (StorageOutWarehouseGoods goods : goodsDataList) {
        //        goods.setOutWarehouseId(storageOutWarehouse.getOutWarehouseId());
        //        list.add(goods);
        //    }
        //    storageOutWarehouseGoodsServiceDomain.saveBatch(list);
        //}
        return update;
    }


    /**
     * 根据订单号，创建出库单
     *
     * @param orderId 订单id
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createByOrderNum(Long orderId) {
        TradeOrder tradeOrder = this.tradeOrderDomainService.getFullOrderById(orderId);
        List<TradeOrderDetail> tradeOrderDetailList = tradeOrder.getTradeOrderDetailList();
        //根据仓库，对明细分组
        Map<Long, List<TradeOrderDetail>> result = getWarehouseIdMap(tradeOrderDetailList);

        Iterator<Map.Entry<Long, List<TradeOrderDetail>>> iterator = result.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, List<TradeOrderDetail>> entry = iterator.next();
            String outWarehouseId = sequenceDomainService.nextDateSequence("CKID", 6);
            //存出库明细
            List<AdvanceInvoiceDetail> advanceInvoiceDetailList = new ArrayList<>();
            int detailOutNumber = 0;
            List<StorageOutWarehouseGoods> storageOutWarehouseGoodsList = new ArrayList<>();
            for (TradeOrderDetail detail : entry.getValue()) {
                StorageOutWarehouseGoods storageOutWarehouseGoods = new StorageOutWarehouseGoods();
                storageOutWarehouseGoods.setOutWarehouseId(outWarehouseId);
                storageOutWarehouseGoods.setWarehouseId(entry.getKey());
                detailOutNumber = detailOutNumber + detail.getItemNum();
                storageOutWarehouseGoods.setOutWarehouseNumber(detail.getItemNum());
                storageOutWarehouseGoods.setInventoryId(detail.getInvSkuId());
                storageOutWarehouseGoods.setOutStatus(OutStatusEnum.DCK);
                storageOutWarehouseGoods.setArticleCode(detail.getArticleCode());
                //预发货明细
                AdvanceInvoiceDetail invoiceDetail = new AdvanceInvoiceDetail();
                StorageInventory storageInventory = storageInventoryServiceDomain.getById(detail.getInvSkuId());
                invoiceDetail.setArticleCode(storageInventory.getCode());
                invoiceDetail.setTotalNumber(detail.getItemNum());
                advanceInvoiceDetailList.add(invoiceDetail);
                storageOutWarehouseGoods.setArticleCode(storageInventory.getCode());
                storageOutWarehouseGoodsList.add(storageOutWarehouseGoods);
            }
            storageOutWarehouseGoodsServiceDomain.saveBatch(storageOutWarehouseGoodsList);
            //存出库主表
            saveOutWarehouse(tradeOrder, entry, outWarehouseId, detailOutNumber);
            //生成 预发货单
            saveInvoice(tradeOrder, entry, outWarehouseId, advanceInvoiceDetailList, detailOutNumber);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据采购退货单，创建出库单
     *
     * @param manufacturerReturn
     * @param manufacturerReturnGoodsList
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createByPurchaseReturn(ManufacturerReturn manufacturerReturn, List<ManufacturerReturnGoods> manufacturerReturnGoodsList) {
        String outWarehouseId = sequenceDomainService.nextDateSequence("CKID", 6);
        //存出库明细
        Long detailOutNumber = 0L;
        List<StorageOutWarehouseGoods> storageOutWarehouseGoodsList = new ArrayList<>();
        for (ManufacturerReturnGoods detail : manufacturerReturnGoodsList) {
            StorageOutWarehouseGoods storageOutWarehouseGoods = new StorageOutWarehouseGoods();
            storageOutWarehouseGoods.setOutWarehouseId(outWarehouseId);
            storageOutWarehouseGoods.setWarehouseId(manufacturerReturn.getWarehouseId());
            detailOutNumber = detailOutNumber + detail.getReturnBackNum();
            storageOutWarehouseGoods.setOutWarehouseNumber(detail.getReturnBackNum().intValue());
            StorageInventory storageInventory = storageInventoryServiceDomain.lambdaQuery()
                    .eq(StorageInventory::getCode, detail.getArticleCode())
                    .eq(StorageInventory::getWarehouseId, manufacturerReturn.getWarehouseId()).one();
            storageOutWarehouseGoods.setInventoryId(storageInventory.getId());
            storageOutWarehouseGoods.setOutStatus(OutStatusEnum.DCK);
            storageOutWarehouseGoods.setArticleCode(detail.getArticleCode());
            storageOutWarehouseGoods.setArticleCode(detail.getArticleCode());
            storageOutWarehouseGoodsList.add(storageOutWarehouseGoods);
        }
        StorageOutWarehouse storageOutWarehouse = new StorageOutWarehouse();
        storageOutWarehouse.setOutWarehouseId(outWarehouseId);
        storageOutWarehouse.setOutStatus(OutStatusEnum.DCK);
        storageOutWarehouse.setOutType(InOutTypeEnum.PURCHASE_RETURN);
        storageOutWarehouse.setShopWaybill(manufacturerReturn.getCode());
        storageOutWarehouse.setOutWarehouseNumber(detailOutNumber.intValue());
        storageOutWarehouse.setWarehouseId(manufacturerReturn.getWarehouseId());
        storageOutWarehouseServiceDomain.save(storageOutWarehouse);
        storageOutWarehouseGoodsServiceDomain.saveBatch(storageOutWarehouseGoodsList);
        return Boolean.TRUE;
    }

    /**
     * 根据仓库，对明细分组
     *
     * @param tradeOrderDetailList
     * @return
     */
    private Map<Long, List<TradeOrderDetail>> getWarehouseIdMap(List<TradeOrderDetail> tradeOrderDetailList) {
        Map<Long, List<TradeOrderDetail>> result = new HashMap<>(tradeOrderDetailList.size());
        for (TradeOrderDetail detail : tradeOrderDetailList) {
            if (detail.getItemNum() == 0) {
                continue;
            }
            Long invSkuId = detail.getInvSkuId();
            StorageInventory storageInventory = storageInventoryServiceDomain.getById(invSkuId);
            List<TradeOrderDetail> detailList = result.get(storageInventory.getWarehouseId());
            if (detailList == null) {
                detailList = new ArrayList<>();
            }
            result.put(storageInventory.getWarehouseId(), detailList);
            detailList.add(detail);
        }
        return result;
    }

    /**
     * 存出库主表
     *
     * @param tradeOrder
     * @param entry
     * @param outWarehouseId
     * @param detailOutNumber
     */
    private void saveOutWarehouse(TradeOrder tradeOrder, Map.Entry<Long, List<TradeOrderDetail>> entry,
                                  String outWarehouseId, int detailOutNumber) {
        StorageOutWarehouse storageOutWarehouse = new StorageOutWarehouse();
        storageOutWarehouse.setOutWarehouseId(outWarehouseId);
        storageOutWarehouse.setOutStatus(OutStatusEnum.DCK);
        storageOutWarehouse.setOutType(InOutTypeEnum.SHOPPING_MALL);
        storageOutWarehouse.setShopWaybill(tradeOrder.getOrderNum());
        storageOutWarehouse.setOutWarehouseNumber(detailOutNumber);
        storageOutWarehouse.setWarehouseId(entry.getKey());
        storageOutWarehouse.setLogisticsType(tradeOrder.getLogisticsProvider());
        storageOutWarehouseServiceDomain.save(storageOutWarehouse);
    }

    /**
     * 生成预发货单操作
     *
     * @param tradeOrder
     * @param entry
     * @param outWarehouseId
     * @param advanceInvoiceDetailList
     * @param detailOutNumber
     */
    private void saveInvoice(TradeOrder tradeOrder, Map.Entry<Long, List<TradeOrderDetail>> entry,
                             String outWarehouseId, List<AdvanceInvoiceDetail> advanceInvoiceDetailList,
                             int detailOutNumber) {
        //预发货单
        AdvanceInvoice invoice = new AdvanceInvoice();
        invoice.setOutboundOrderCode(outWarehouseId);
        invoice.setOutType(InOutTypeEnum.SHOPPING_MALL);
        //物流商
        invoice.setLogisticsProvider(tradeOrder.getLogisticsProvider());
        Warehouse warehouse = warehouseServiceDomain.getById(entry.getKey());
        LbsLocation location = warehouse.getLocation();
        //发货人
        LogisticsContact sender = LogisticsContact.builder()
                .name(warehouse.getName())
                .company(warehouse.getCompany())
                .mobile(warehouse.getPhone())
                .location(warehouse.getLocation())
                .address(warehouse.getAddr())
                .addressCode(warehouse.getCounty())
                .build();
        invoice.setSender(sender);
        Customer customer = customerDomainService.getById(tradeOrder.getCustomerId());
        LogisticsContact receiver = LogisticsContact.builder()
                .name(tradeOrder.getConsigneeName())
                .company(customer.getName())
                .mobile(tradeOrder.getConsigneePhone())
                .location(tradeOrder.getLocation())
                .address(tradeOrder.getConsigneeAddress())
                .addressCode(tradeOrder.getConsigneeRegionCode())
                .build();
        invoice.setReceiver(receiver);
        invoice.setTotalNumber(detailOutNumber);
        advanceInvoiceServiceDomain.save(invoice);

        //存预发货明细  相同的物品合成一个
        Map<String, List<AdvanceInvoiceDetail>> invoiceDetailMap = advanceInvoiceDetailList.stream().collect(
                Collectors.groupingBy(AdvanceInvoiceDetail::getArticleCode));
        List<String> articleCodeList = advanceInvoiceDetailList.stream().map(AdvanceInvoiceDetail::getArticleCode)
                .distinct().collect(Collectors.toList());
        List<AdvanceInvoiceDetail> invoiceDetails = new ArrayList<>();
        for (String code : articleCodeList) {
            List<AdvanceInvoiceDetail> invoiceDetailList = invoiceDetailMap.get(code);
            if (invoiceDetailList.size() == 1) {
                invoiceDetails.add(invoiceDetailList.get(0));
            }
            if (invoiceDetailList.size() > 1) {
                AdvanceInvoiceDetail detail = invoiceDetailList.get(0);
                int totalNumber = 0;
                for (AdvanceInvoiceDetail invoiceDetail : invoiceDetailList) {
                    totalNumber = invoiceDetail.getTotalNumber() + totalNumber;
                }
                detail.setTotalNumber(totalNumber);
                invoiceDetails.add(detail);
            }
        }
        invoiceDetails.forEach(element -> element.setAdvanceInvoiceId(invoice.getId()));
        advanceInvoiceDetailServiceDomain.saveBatch(invoiceDetails);
    }

    /**
     * 根据关联单据，关闭出库单
     *
     * @param shopWaybill
     * @return
     */
    @Deprecated
    public Boolean closeOutWarehouse(String shopWaybill) {
        if (StringUtils.isBlank(shopWaybill)) {
            throw new MaginaException("出库单的关联单号不能为空");
        }
        StorageOutWarehouse storageOutWarehouse = storageOutWarehouseServiceDomain.getOne(
                new LambdaQueryWrapper<StorageOutWarehouse>()
                        .eq(StorageOutWarehouse::getShopWaybill, shopWaybill)
                        // 状态不对
                        .ne(StorageOutWarehouse::getOutStatus, "0")
        );
        if (storageOutWarehouse == null) {
            return Boolean.TRUE;
        }
        StorageOutWarehouse updateOutWarehouse = new StorageOutWarehouse();
        updateOutWarehouse.setId(storageOutWarehouse.getId());
        updateOutWarehouse.setOutStatus(OutStatusEnum.GB);
        //关闭明细
        storageOutWarehouseGoodsServiceDomain.lambdaUpdate()
                .set(StorageOutWarehouseGoods::getOutStatus, OutStatusEnum.GB)
                .eq(StorageOutWarehouseGoods::getOutWarehouseId, storageOutWarehouse.getOutWarehouseId())
                .update();
        return storageOutWarehouseServiceDomain.updateById(updateOutWarehouse);
    }

    /**
     * 批次号下拉
     *
     * @param code
     * @param warehouseId
     * @return
     */
    public List<StorageInventoryBatch> batchList(String code, String warehouseId, String batchCode) {
        return
                storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, code)
                        .eq(StringUtils.isNotBlank(batchCode),StorageInventoryBatch::getBatchCode, batchCode)
                        .eq(StorageInventoryBatch::getWarehouseId, warehouseId)
                        // 查询库存大于0的批次
                        .gt(StorageInventoryBatch::getRemWarehouseNumber, 0)
                        .orderByAsc(StorageInventoryBatch::getCreatedAt)
                        .list();
    }

    /**
     * 出库明细
     *
     * @param id
     * @return: {@link List< StorageWarehouseFlow>}
     */
    public List<StorageWarehouseFlow> goodsInWareDetail(Long id) {
        StorageOutWarehouseGoods exist = this.storageOutWarehouseGoodsServiceDomain.getById(id);
        if (null == exist) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<StorageInventory> storageInventoryWrapper = new LambdaQueryWrapper<>();
        storageInventoryWrapper.eq(StorageInventory::getId, exist.getInventoryId());
        StorageInventory storageInventory = storageInventoryServiceDomain.getOne(storageInventoryWrapper);
        // 根据入库编号+库品编号来查询流水明细
        return this.storageWarehouseFlowServiceDomain.selectJoinList(
                StorageWarehouseFlow.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(StorageWarehouseFlow.class)
                        .selectAs(UserBasic::getName, StorageWarehouseFlow::getOperatorName)
                        .leftJoin(UserBasic.class, UserBasic::getId, StorageWarehouseFlow::getOperatorId)
                        .eq(StorageWarehouseFlow::getFlowId, exist.getOutWarehouseId())
                        .eq(StorageWarehouseFlow::getCode, storageInventory.getCode())
                        .eq(StorageWarehouseFlow::getInOutType, 2)
                        .orderByAsc(StorageWarehouseFlow::getCreatedAt)
        );
    }

    /**
     * 列表-备注修改
     *
     * @param id     出库单id
     * @param remark 备注信息
     * @return true/false
     */
    public boolean updateRemarks(Long id, String remark) {
        return
                this.storageOutWarehouseServiceDomain.lambdaUpdate()
                        .set(StorageOutWarehouse::getRemarks, remark)
                        .eq(StorageOutWarehouse::getId, id)
                        .update();
    }

    /**
     * 取消出库
     *
     * @param shopWaybill
     * @param invSkuId
     * @param num
     */
    public void cancelOutWarehouse(String shopWaybill, Long invSkuId, Integer num) {
        StorageOutWarehouse outWarehouse = this.storageOutWarehouseServiceDomain.lambdaQuery()
                .eq(StorageOutWarehouse::getShopWaybill, shopWaybill)
                .one();
        if (outWarehouse == null) {
            throw new MaginaException("出库单不存在");
        }

        StorageOutWarehouseGoods storageOutWarehouseGoods = this.storageOutWarehouseGoodsServiceDomain.lambdaQuery()
                .eq(StorageOutWarehouseGoods::getInventoryId, invSkuId)
                .eq(StorageOutWarehouseGoods::getOutWarehouseId, outWarehouse.getOutWarehouseId())
                .one();
        if (Objects.isNull(storageOutWarehouseGoods)) {
            throw new MaginaException("出库单未找到对应物品");
        }
        //待出库数量
        Integer waitNum = storageOutWarehouseGoods.getOutWarehouseNumber() - storageOutWarehouseGoods.getCancelOutWarehouseNumber() - storageOutWarehouseGoods.getAuidtOutWarehouseNumber();
        if (waitNum < num) {
            StorageInventory storageInventory = storageInventoryServiceDomain.getById(invSkuId);
            throw new MaginaException("物品[" + storageInventory.getName() + "]已出库[" + storageOutWarehouseGoods.getAuidtOutWarehouseNumber() + "]，无法取消");
        }
        storageOutWarehouseGoods.setCancelOutWarehouseNumber(storageOutWarehouseGoods.getCancelOutWarehouseNumber() + num);
        if (storageOutWarehouseGoods.getCancelOutWarehouseNumber() + storageOutWarehouseGoods.getAuidtOutWarehouseNumber() == storageOutWarehouseGoods.getOutWarehouseNumber()) {
            storageOutWarehouseGoods.setOutStatus(OutStatusEnum.YCK);
            if (storageOutWarehouseGoods.getAuidtOutWarehouseNumber() == 0) {
                storageOutWarehouseGoods.setOutStatus(OutStatusEnum.GB);
            }
        }
        storageOutWarehouseGoodsServiceDomain.updateById(storageOutWarehouseGoods);

        List<StorageOutWarehouseGoods> outWarehouseGoodsList = this.storageOutWarehouseGoodsServiceDomain.lambdaQuery().
                eq(StorageOutWarehouseGoods::getOutWarehouseId, outWarehouse.getOutWarehouseId())
                .ne(StorageOutWarehouseGoods::getId, storageOutWarehouseGoods.getId())
                .list();
        outWarehouseGoodsList.add(storageOutWarehouseGoods);

        List<StorageOutWarehouseGoods> completeOutWarehouseGoodsList = outWarehouseGoodsList.stream()
                .filter(item -> item.getOutStatus().equals(OutStatusEnum.YCK)).collect(Collectors.toList());
        List<StorageOutWarehouseGoods> closeOutWarehouseGoodsList = outWarehouseGoodsList.stream()
                .filter(item -> item.getOutStatus().equals(OutStatusEnum.GB)).collect(Collectors.toList());
        if (completeOutWarehouseGoodsList.size() + closeOutWarehouseGoodsList.size() == outWarehouseGoodsList.size()) {
            if (completeOutWarehouseGoodsList.size() > 0) {
                outWarehouse.setOutStatus(OutStatusEnum.YCK);
            } else {
                outWarehouse.setOutStatus(OutStatusEnum.GB);
            }
            storageOutWarehouseServiceDomain.updateById(outWarehouse);
        }
    }


    /**
     * 仓储管理-出库管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<StorageOutWarehouse> pageWechart(StorageOutWarehouseQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.storageOutWarehouseServiceDomain.selectJoinList(StorageOutWarehouse.class,
                MPJWrappers.lambdaJoin().selectAll(StorageOutWarehouse.class)
                        .selectAs(UserBasic::getName, StorageOutWarehouse::getConsignee)
                        .selectAs(Customer::getName, StorageOutWarehouse::getCustomerName)
                        .selectAs(TradeOrder::getOrderNum, StorageOutWarehouse::getOrderNum)
                        .leftJoin(ApplyOrder.class, ApplyOrder::getCode, StorageOutWarehouse::getShopWaybill)
                        .leftJoin(TradeOrder.class, TradeOrder::getOrderNum, StorageOutWarehouse::getShopWaybill)
                        .leftJoin(Customer.class, Customer::getId, TradeOrder::getCustomerId)
                        .leftJoin(UserBasic.class, UserBasic::getId, ApplyOrder::getEngineerId)
                        .eq(Objects.nonNull(pageQuery.getOutType()), StorageOutWarehouse::getOutType, pageQuery.getOutType())
                        .eq(Objects.nonNull(pageQuery.getOutStatus()), StorageOutWarehouse::getOutStatus, pageQuery.getOutStatus())
                        .like(
                                StringUtils.isNotBlank(pageQuery.getMainWaybill()), StorageOutWarehouse::getMainWaybill,
                                pageQuery.getMainWaybill()
                        )
                        .like(
                                StringUtils.isNotBlank(pageQuery.getShopWaybill()), StorageOutWarehouse::getShopWaybill,
                                pageQuery.getShopWaybill()
                        )
                        .like(
                                StringUtils.isNotBlank(pageQuery.getSecondlyWaybill()), StorageOutWarehouse::getSecondlyWaybill,
                                pageQuery.getSecondlyWaybill()
                        ).like(
                                StringUtils.isNotBlank(pageQuery.getConsignee()), UserBasic::getName,
                                pageQuery.getConsignee()
                        )
                        .eq(
                                StringUtils.isNotBlank(pageQuery.getOutWarehouseId()), StorageOutWarehouse::getOutWarehouseId,
                                pageQuery.getOutWarehouseId()
                        )
                        .orderByDesc(StorageOutWarehouse::getCreatedAt))
        ).peek(p -> {
            //如果该物品之前已经有入库的储位信息，那储位这里，自动带出该物品最近保存的储位信息。可修改
            List<StorageOutWarehouseGoods> storageOutWarehouseGoods = storageOutWarehouseGoodsServiceDomain.selectJoinList(StorageOutWarehouseGoods.class,
                    MPJWrappers.lambdaJoin().selectAll(StorageOutWarehouseGoods.class)
                            .selectAs(StorageInventory::getLocation, StorageOutWarehouseGoods::getLocation)
                            .selectAs(StorageInventory::getName, StorageOutWarehouseGoods::getInventoryName)
                            .selectAs(StorageArticle::getImageFiles, StorageOutWarehouseGoods::getImageFiles)
                            .selectAs(StorageArticle::getManufacturerChannel, StorageOutWarehouseGoods::getManufacturerChannel)
                            .selectAs(StorageArticle::getNumberOem, StorageOutWarehouseGoods::getNumberOem)
                            .selectAs(StorageInventory::getCode, StorageOutWarehouseGoods::getInventoryCode)
                            .selectAs(StorageInventory::getLocation, StorageOutWarehouseGoods::getLocation)
                            .leftJoin(StorageInventory.class, StorageInventory::getId, StorageOutWarehouseGoods::getInventoryId)
                            .leftJoin(StorageArticle.class, StorageArticle::getCode, StorageInventory::getCode)
                            .eq(StorageOutWarehouseGoods::getOutWarehouseId, p.getOutWarehouseId())
            );
            p.setItemList(storageOutWarehouseGoods);
        });
    }


}
