package com.hightop.benyin.statistics.application.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.hightop.benyin.logistics.infrastructure.enums.LogisticsProvider;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 财务报表-应收款耗材表
 *
 * <AUTHOR>
 * @Date 2024/5/20 15:38
 */
@ApiModel("财务报表-应收款耗材表")
@Data
public class MaterialCollectionVO {

    @ApiModelProperty("id;")
    Long id;

    @Excel(name = "订单号", width = 30, orderNum = "1")
    @ApiModelProperty("订单号")
    String code;

    @Excel(name = "订单状态", width = 15, orderNum = "2")
    @ApiModelProperty("订单状态")
    String status;

    @Excel(name = "客户编码", width = 15, orderNum = "3")
    @ApiModelProperty("客户编码")
    String customerCode;

    @Excel(name = "客户名称", width = 30, orderNum = "4")
    @ApiModelProperty("客户名称")
    String customerName;

    @ApiModelProperty("营业执照名称")
    @Excel(name = "营业执照名称", width = 25, orderNum = "5")
    String license;

    @Excel(name = "物品编码", width = 20, orderNum = "6")
    @ApiModelProperty("物品编码")
    String articleCode;

    @Excel(name = "物品名称", width = 30, orderNum = "7")
    @ApiModelProperty("物品名称")
    String articleName;

    @Excel(name = "制造商渠道", enumExportField = "label", width = 20, orderNum = "8")
    @ApiModelProperty("制造商渠道(字典项码)")
    @DictItemBind(StorageArticle.CHANNEL)
    DictItemEntry manufacturerChannel;

    @Excel(name = "单位", width = 15, orderNum = "9")
    @ApiModelProperty("单位")
    String unit;

    @Excel(name = "数量", width = 15, orderNum = "10")
    @ApiModelProperty("数量")
    Integer num;

    @Excel(name = "单价", width = 15, orderNum = "11")
    @ApiModelProperty("单价")
    BigDecimal price;

    @Excel(name = "商品金额", width = 15, orderNum = "12")
    @ApiModelProperty("商品金额")
    BigDecimal amount;

    @Excel(name = "配送费", width = 15, orderNum = "13")
    @ApiModelProperty("配送费")
    BigDecimal shippingFee;

    @Excel(name = "订单金额", width = 15, orderNum = "14")
    @ApiModelProperty("订单金额")
    BigDecimal orderAmount;

    @Excel(name = "配送方式", width = 20, orderNum = "15", enumExportField = "name")
    @ApiModelProperty("配送方式")
    LogisticsProvider logisticsProvider;

    @Excel(name = "下单时间", width = 20, orderNum = "16", format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("下单时间")
    LocalDateTime createdAt;

    @ApiModelProperty("客户id")
    Long customerId;

    @Excel(name = "类型", width = 15, orderNum = "17")
    @ApiModelProperty("类型：0-销售订单，1-维修工单")
    Integer type;
}
