package com.hightop.benyin.appupdate.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 应用版本发布DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel("应用版本发布")
public class AppVersionPublishDto {
    
    @ApiModelProperty("版本名称")
    @NotBlank(message = "版本名称不能为空")
    String versionName;
    
    @ApiModelProperty("版本号")
    @NotNull(message = "版本号不能为空")
    Integer versionCode;
    
    @ApiModelProperty("APK文件名")
    @NotBlank(message = "APK文件名不能为空")
    String apkFileName;

    @ApiModelProperty("COS存储key")
    @NotBlank(message = "COS存储key不能为空")
    String cosKey;

    @ApiModelProperty("COS访问URL")
    @NotBlank(message = "COS访问URL不能为空")
    String cosUrl;

    @ApiModelProperty("文件大小")
    @NotNull(message = "文件大小不能为空")
    Long fileSize;

    @ApiModelProperty("文件MD5")
    @NotBlank(message = "文件MD5不能为空")
    String fileMd5;
    
    @ApiModelProperty("更新说明")
    String updateLog;
    
    @ApiModelProperty("是否强制更新")
    Boolean isForce = false;
    
    @ApiModelProperty("是否启用")
    Boolean isActive = true;
}
