package com.hightop.benyin.logdata.domain.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.magina.standard.task.log.JobState;
import com.hightop.magina.standard.task.log.TriggerType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@TableName("st_job_log_data")
@ApiModel
public class JobLogData {
    @TableId(
        value = "id",
        type = IdType.ASSIGN_ID
    )
    @ApiModelProperty("id")
    private Long id;
    @TableField("job_id")
    @ApiModelProperty("任务id")
    private Long jobId;
    @TableField("job_version")
    @ApiModelProperty("任务版本时间戳")
    @JsonIgnore
    private LocalDateTime jobVersion;
    @TableField("retry")
    @ApiModelProperty("可重试次数")
    private Integer retry;
    @TableField("retried")
    @ApiModelProperty("已重试次数")
    private Integer retried;
    @TableField("timeout")
    @ApiModelProperty("超时时间")
    private Integer timeout;
    @TableField("scheduled_at")
    @ApiModelProperty("计划时间")
    private LocalDateTime scheduledAt;
    @TableField("trigger_type")
    @ApiModelProperty("触发方式")
    private TriggerType triggerType;
    @TableField("triggered_at")
    @ApiModelProperty("触发时间")
    private LocalDateTime triggeredAt;
    @TableField("triggered_by")
    @ApiModelProperty("触发用户")
    private Long triggeredBy;
    @TableField("started_at")
    @ApiModelProperty("开始时间")
    private LocalDateTime startedAt;
    @TableField("finished_at")
    @ApiModelProperty("结束时间")
    private LocalDateTime finishedAt;
    @TableField("elapse")
    @ApiModelProperty("耗时ms")
    private Long elapse;
    @TableField("state")
    @ApiModelProperty("执行状态")
    private JobState state;
    @TableField("message")
    @ApiModelProperty("执行错误信息")
    private String message;
}