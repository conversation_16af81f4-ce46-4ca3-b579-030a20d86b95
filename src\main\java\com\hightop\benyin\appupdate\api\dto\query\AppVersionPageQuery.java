package com.hightop.benyin.appupdate.api.dto.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * 应用版本分页查询DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("应用版本分页查询")
public class AppVersionPageQuery extends PageQuery {
    
    @ApiModelProperty("版本名称")
    String versionName;
    
    @ApiModelProperty("是否启用")
    Boolean isActive;
    
    @ApiModelProperty("是否强制更新")
    Boolean isForce;
}
