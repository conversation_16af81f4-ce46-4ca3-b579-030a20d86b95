package com.hightop.benyin.purchase.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.product.domain.service.PartProductTreeDomainService;
import com.hightop.benyin.purchase.api.dto.PurchasePaymentItemVo;
import com.hightop.benyin.purchase.api.dto.PurchasePaymentVo;
import com.hightop.benyin.purchase.api.dto.query.PurchasePaymentQuery;
import com.hightop.benyin.purchase.api.param.PurchaseMergePayParam;
import com.hightop.benyin.purchase.api.param.PurchasePayAuditParam;
import com.hightop.benyin.purchase.api.param.PurchasePayVoucherParam;
import com.hightop.benyin.purchase.domain.service.*;
import com.hightop.benyin.purchase.infrastructure.entity.*;
import com.hightop.benyin.purchase.infrastructure.enums.ManufactureOrderStatusEnum;
import com.hightop.benyin.purchase.infrastructure.enums.ManufactureReturnStatusEnum;
import com.hightop.benyin.purchase.infrastructure.enums.PurchasePayStatusEnum;
import com.hightop.benyin.purchase.infrastructure.enums.SettleStatusEnum;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2023/12/5 21:52
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class PurchasePaymentService {

    PurchasePaymentServiceDomain purchasePaymentServiceDomain;
    PurchaseOrderServiceDomain purchaseOrderServiceDomain;
    PurchasePayVoucherServiceDomain purchasePayVoucherServiceDomain;
    PurchaseOrderGoodsServiceDomain purchaseOrderGoodsServiceDomain;
    ManufacturerServiceDomain manufacturerServiceDomain;
    ManufacterOrderGoodsServiceDomain manufacterOrderGoodsServiceDomain;
    SequenceDomainService sequenceDomainService;
    ManufacterDeliveryService manufacterDeliveryService;
    ManufacterOrderServiceDomain manufacterOrderServiceDomain;
    ManufacterReturnServiceDomain manufacterReturnServiceDomain;
    PurchasePaymentDetailServiceDomain purchasePaymentDetailServiceDomain;
    ManufacterOrderService manufacterOrderService;

    public DataGrid<PurchasePayment> page(PurchasePaymentQuery query) {
        return PageHelper.startPage(query, p ->
                purchasePaymentServiceDomain.selectJoinList(PurchasePayment.class, MPJWrappers.lambdaJoin()
                        .selectAll(PurchasePayment.class)
                        .selectAs(Manufacturer::getName, PurchasePayment::getManufacturerName)
                        .leftJoin(Manufacturer.class, Manufacturer::getId, PurchasePayment::getManufacturerId)
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), PurchasePayment::getPurchaseCodes, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getCode()), PurchasePayment::getCode, query.getCode())
                        .like(StringUtils.isNotBlank(query.getPaymentName()), PurchasePayment::getPaymentName, query.getPaymentName())
                        .like(StringUtils.isNotBlank(query.getManufacturerName()), Manufacturer::getName, query.getManufacturerName())
                        .eq(query.getInvoiceStatus() != null, PurchasePayment::getInvoiceStatus, query.getInvoiceStatus())
                        .eq(query.getRefundStatus() != null, PurchaseOrder::getRefundStatus, query.getRefundStatus())
                        .eq(StringUtils.isNotBlank(query.getSettleMethod()), PurchasePayment::getSettleMethod, query.getSettleMethod())
                        .like(StringUtils.isNotBlank(query.getManufacterOrderCodes()), PurchasePayment::getManufacterOrderCodes, query.getManufacterOrderCodes())
                        .eq(query.getStatus() != null, PurchasePayment::getStatus, query.getStatus())
                        .ge(query.getInitiatorTimeStart() != null, PurchasePayment::getPaymentTime, query.getInitiatorTimeStart() + " 00:00:00")
                        .le(query.getInitiatorTimeEnd() != null, PurchasePayment::getPaymentTime, query.getInitiatorTimeEnd() + " 23:59:59")
                        .orderByDesc(PurchasePayment::getCreatedAt)
                )
        );
    }

    public PurchasePaymentVo getById(Long id) {
        PurchasePaymentVo purchasePaymentVo = purchasePaymentServiceDomain.selectJoinOne(PurchasePaymentVo.class, MPJWrappers.lambdaJoin()
                .selectAll(PurchasePayment.class)
                .selectAs(UserBasic::getName, PurchasePaymentVo::getAuditName)
                .selectAs(PurchasePayment::getCode, PurchasePaymentVo::getCode)
                .selectAs(PurchasePayment::getAmount, PurchasePaymentVo::getAmount)
                .selectAs(PurchasePayment::getStatus, PurchasePaymentVo::getStatus)
                .selectAs(Manufacturer::getName, PurchasePaymentVo::getManufacturerName)
                .selectAs(Manufacturer::getLicense, PurchasePaymentVo::getLicense)
                .selectAs(Manufacturer::getBank, PurchasePaymentVo::getBank)
                .selectAs(Manufacturer::getBankClient, PurchasePaymentVo::getBankClient)
                .selectAs(Manufacturer::getAccount, PurchasePaymentVo::getAccount)
                .selectAs(Manufacturer::getBankAccount, PurchasePaymentVo::getBankAccount)
                .selectAs(Manufacturer::getTicketType, PurchasePaymentVo::getTicketType)
                .selectAs(Manufacturer::getId, PurchasePaymentVo::getManufacturerId)
                .leftJoin(Manufacturer.class, Manufacturer::getId, PurchasePayment::getManufacturerId)
                .leftJoin(UserBasic.class, UserBasic::getId, PurchasePayment::getAuditId)
                .eq(PurchasePayment::getId, id));

        List<PurchasePayVoucher> purchasePayVoucherList = purchasePayVoucherServiceDomain.lambdaQuery()
                .eq(PurchasePayVoucher::getPaymentCode, purchasePaymentVo.getCode()).list();
        purchasePaymentVo.setPurchasePayVouchers(purchasePayVoucherList);

        String[] manufacterOrderCodes = purchasePaymentVo.getManufacterOrderCodes().split(",");

        List<ManufacturerOrderGoods> manufacturerOrderGoods = manufacterOrderGoodsServiceDomain.selectJoinList(ManufacturerOrderGoods.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerOrderGoods.class)
                        .selectAs(PurchaseOrderGoods::getPurchaseOrderCode, ManufacturerOrderGoods::getPurchaseOrderCode)
                        .selectAs(PurchaseOrderGoods::getReceiptCode, ManufacturerOrderGoods::getReceiptCode)
                        .selectAs(StorageArticle::getName, ManufacturerOrderGoods::getArticleName)
                        .selectAs(StorageArticle::getNumberOem, ManufacturerOrderGoods::getOemNumber)
                        .selectAs(StorageArticle::getUnit, ManufacturerOrderGoods::getUnit)
                        .selectAs(StorageArticle::getPartId, ManufacturerOrderGoods::getPartId)
                        .leftJoin(StorageArticle.class, StorageArticle::getId, ManufacturerOrderGoods::getArticleId)
                        .leftJoin(PurchaseOrderGoods.class, PurchaseOrderGoods::getId, ManufacturerOrderGoods::getPurchaseOrderGoodsId)
                        .in(ManufacturerOrderGoods::getManufacturerOrderCode, manufacterOrderCodes)
        );
        Map<String, List<ManufacturerOrderGoods>> listMap = manufacturerOrderGoods.stream().collect(Collectors.groupingBy(ManufacturerOrderGoods::getManufacturerOrderCode));
        List<PurchasePaymentItemVo> purchasePaymentItemVos = Lists.newArrayList();
        listMap.forEach((k, v) -> {
            PurchasePaymentItemVo purchasePaymentItemVo = new PurchasePaymentItemVo();
            purchasePaymentItemVo.setManufacturerOrderCode(k);
            purchasePaymentItemVo.setPurchaseOrderGoods(v);
            purchasePaymentItemVos.add(purchasePaymentItemVo);
        });
        purchasePaymentVo.setPurchasePaymentItemVos(purchasePaymentItemVos);
        return purchasePaymentVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean mergePayment(PurchaseMergePayParam param) {
        List<Long> ids = param.getIds();

        if (CollectionUtils.isEmpty(ids)) {
            throw new MaginaException("请选择销售单");
        }

        List<ManufacturerOrder> manufacturerOrders = manufacterOrderServiceDomain.lambdaQuery().in(ManufacturerOrder::getId, ids).list();
        if (CollectionUtils.isEmpty(manufacturerOrders)) {
            throw new MaginaException("未找到订单");
        }
        Long manufacturerId = manufacturerOrders.get(0).getManufacturerId();
        for (ManufacturerOrder manufacturerOrder : manufacturerOrders) {
//            if (!manufacturerOrder.getSettleMethod().getValue().equals(ManufacturerOrder.SETTLEMENT_MONTHLY)) {
//                throw new MaginaException("订单" + manufacturerOrder.getCode() + "非月结不能合并付款");
//            }
            if (manufacturerOrder.getStatus().getCode().equals(ManufactureOrderStatusEnum.WAIT_AUDIT.getCode())
                ||manufacturerOrder.getStatus().getCode().equals(ManufactureOrderStatusEnum.REJECT.getCode())
                ||manufacturerOrder.getStatus().getCode().equals(ManufactureOrderStatusEnum.CLOSED.getCode())
            ) {
                throw new MaginaException("订单" + manufacturerOrder.getCode() + "当前状态不能付款");
            }
            if (!manufacturerOrder.getSettleStatus().getCode().equals(SettleStatusEnum.NO_SETTLE.getCode())) {
                throw new MaginaException("订单" + manufacturerOrder.getCode() + "已在结算状态中");
            }
            if (manufacturerId.compareTo(manufacturerOrder.getManufacturerId()) != 0) {
                throw new MaginaException("只能针对同一供应商订单合并付款");
            }
            Long count = manufacterReturnServiceDomain.lambdaQuery().eq(ManufacturerReturn::getManufacturerOrderCode, manufacturerOrder.getCode())
                    .eq(ManufacturerReturn::getRefundStatus,ManufactureReturnStatusEnum.WAIT_CONFIRM).count();
            if (count > 0L) {
                throw new MaginaException("订单" + manufacturerOrder.getCode() + "待确认退货单，不能付款");
            }
            manufacturerOrder.setSettleStatus(SettleStatusEnum.PAYING);
        }
        DictItemEntry settleMethod = manufacturerOrders.get(0).getSettleMethod();
        List<String> purchaseCodes = manufacturerOrders.stream().map(ManufacturerOrder::getPurchaseCode).distinct().collect(Collectors.toList());
        List<String> manufacterOrderCodes = manufacturerOrders.stream().map(ManufacturerOrder::getCode).distinct().collect(Collectors.toList());
        Long amount = manufacturerOrders.stream().mapToLong(ManufacturerOrder::getAmount).sum();
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList =
                manufacterOrderGoodsServiceDomain.lambdaQuery().in(ManufacturerOrderGoods::getManufacturerOrderCode, manufacterOrderCodes).list();
        Long num = manufacturerOrderGoodsList.stream().mapToLong(ManufacturerOrderGoods::getNumber).sum();
        Long refundAmount = manufacturerOrderGoodsList.stream().mapToLong(ManufacturerOrderGoods::getRefundAmount).sum();
        String code = sequenceDomainService.nextDateSequence("FK", 6);

        PurchasePayment purchasePayment = new PurchasePayment();
        purchasePayment.setCode(code);
        purchasePayment.setSettleMethod(settleMethod);
        purchasePayment.setPurchaseCodes(purchaseCodes.stream().map(String::valueOf).collect(Collectors.joining(",")));
        purchasePayment.setManufacterOrderCodes(manufacterOrderCodes.stream().map(String::valueOf).collect(Collectors.joining(",")));
        purchasePayment.setManufacturerId(manufacturerId);
        purchasePayment.setNumber(num);
        purchasePayment.setAmount(amount);
        purchasePayment.setActureAmount(amount-refundAmount);
        purchasePayment.setRefundAmount(refundAmount);
        purchasePayment.setStatus(PurchasePayStatusEnum.WAIT_AUDIT);
        purchasePayment.setCreatedAt(LocalDateTime.now());

        // 抵扣资金
        Manufacturer manufacturer = manufacturerServiceDomain.getById(manufacturerId);
        if(manufacturer.getFundBalance()>0L){
            Long drawAmount = manufacturer.getFundBalance()>= purchasePayment.getActureAmount()?
                    purchasePayment.getActureAmount():manufacturer.getFundBalance();
            purchasePayment.setDeductionAmount(drawAmount);
            purchasePayment.setActureAmount(purchasePayment.getActureAmount()-drawAmount);
            manufacturer.setFundBalance(manufacturer.getFundBalance()-drawAmount);
            manufacturerServiceDomain.updateById(manufacturer);
        }
        purchasePaymentServiceDomain.save(purchasePayment);

        //添加明细
        List<PurchasePaymentDetail> purchasePaymentDetails = Lists.newArrayList();
        for (Long id : ids){
            purchasePaymentDetails.add(new PurchasePaymentDetail(purchasePayment.getId(), id));
        }
        purchasePaymentDetailServiceDomain.saveBatch(purchasePaymentDetails);
        return manufacterOrderServiceDomain.updateBatchById(manufacturerOrders);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean buildPurchasePayment(ManufacturerOrder manufacturerOrder) {
        Long count = purchasePaymentServiceDomain.lambdaQuery()
                .like(PurchasePayment::getPurchaseCodes, manufacturerOrder.getPurchaseCode())
                .like(PurchasePayment::getManufacterOrderCodes, manufacturerOrder.getCode()
                ).count();
        if (count > 0) {
            throw new MaginaException("供应商订单" + manufacturerOrder.getCode() + "，付款单已存在");
        }
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = manufacturerOrder.getManufacturerOrderGoodsList();
        String code = sequenceDomainService.nextDateSequence("FK", 6);
        PurchasePayment purchasePayment = new PurchasePayment();
        purchasePayment.setCode(code);
        purchasePayment.setSettleMethod(manufacturerOrder.getSettleMethod());
        purchasePayment.setPurchaseCodes(manufacturerOrder.getPurchaseCode());
        purchasePayment.setManufacterOrderCodes(manufacturerOrder.getCode());
        purchasePayment.setManufacturerId(manufacturerOrder.getManufacturerId());
        Long num = manufacturerOrderGoodsList.stream().mapToLong(ManufacturerOrderGoods::getNumber).sum();
        purchasePayment.setNumber(num);
        purchasePayment.setAmount(manufacturerOrder.getAmount());
        purchasePayment.setStatus(PurchasePayStatusEnum.WAIT_AUDIT);
        purchasePayment.setCreatedAt(LocalDateTime.now());

        //更新确认采购数量
        manufacturerOrderGoodsList.forEach(item -> {
            PurchaseOrderGoods purchaseOrderGoods = new PurchaseOrderGoods();
            purchaseOrderGoods.setId(item.getPurchaseOrderGoodsId());
            purchaseOrderGoods.setNumber(item.getNumber());
            purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);
        });

        return purchasePaymentServiceDomain.save(purchasePayment);
    }

    /**
     * 更新
     *
     * @return
     */
    public Boolean audit(PurchasePayAuditParam auditParam) {
        PurchasePayment purchasePayment = this.purchasePaymentServiceDomain.getById(auditParam.getId());
        if (purchasePayment == null) {
            throw new MaginaException("付款单不存在");
        }
        purchasePayment.setStatus(auditParam.getStatus());
        purchasePayment.setAuditId(ApplicationSessions.id());
        purchasePayment.setRemark(auditParam.getRemark());
        
        String[] manufacterOrderCodes = purchasePayment.getManufacterOrderCodes().split(",");
        
        // 若审核被驳回，需要退回已抵扣的供应商余额并恢复订单状态
        if (PurchasePayStatusEnum.REJECT.equals(auditParam.getStatus())) {
            refundDeduction(purchasePayment);
            // 驳回时将订单状态恢复为未结算
            manufacterOrderServiceDomain.lambdaUpdate()
                    .set(ManufacturerOrder::getSettleStatus, SettleStatusEnum.NO_SETTLE)
                    .in(ManufacturerOrder::getCode, manufacterOrderCodes)
                    .update();
        }
        
        // 检查是否因抵扣而自动完成
        if(purchasePayment.getActureAmount()==0L){
            purchasePayment.setStatus( PurchasePayStatusEnum.COMPLETED);
            purchasePayment.setPaymentId(ApplicationSessions.id());
            purchasePayment.setPaymentName(ApplicationSessions.name());
            purchasePayment.setPaymentTime(LocalDateTime.now());
        }
        
        // 根据付款单最终状态更新订单结算状态
        if (!PurchasePayStatusEnum.REJECT.equals(auditParam.getStatus())) {
            SettleStatusEnum orderStatus = PurchasePayStatusEnum.COMPLETED.equals(purchasePayment.getStatus()) 
                    ? SettleStatusEnum.SETTLED 
                    : SettleStatusEnum.PAYING;
            manufacterOrderServiceDomain.lambdaUpdate()
                    .set(ManufacturerOrder::getSettleStatus, orderStatus)
                    .in(ManufacturerOrder::getCode, manufacterOrderCodes)
                    .update();
        }
        
        // 更新采购订单状态 - 根据供应商订单状态变化同步采购订单状态
        List<String> purchaseCodes = Arrays.stream(purchasePayment.getPurchaseCodes().split(","))
                .distinct()
                .collect(Collectors.toList());
        for (String purchaseCode : purchaseCodes) {
            // 查询该采购单下的所有供应商订单
            List<ManufacturerOrder> relatedOrders = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getPurchaseCode, purchaseCode)
                    .list();
            // 调用已有的状态同步方法
            manufacterOrderService.updatePurchaseOrderStatusByManufacturerOrders(purchaseCode, relatedOrders);
        }

        return this.purchasePaymentServiceDomain.updateById(purchasePayment);
    }

    public Boolean close(Long id) {
        PurchasePayment purchasePayment = this.purchasePaymentServiceDomain.getById(id);
        if (purchasePayment == null) {
            throw new MaginaException("付款单不存在");
        }
        if(!purchasePayment.getStatus().equals(PurchasePayStatusEnum.WAIT_AUDIT)
        && !purchasePayment.getStatus().equals(PurchasePayStatusEnum.WAIT_PAY)){
            throw new MaginaException("当前付款单不能关闭");
        }
        purchasePayment.setStatus(PurchasePayStatusEnum.CLOSED);

        // 付款单关闭时退回抵扣金额
        refundDeduction(purchasePayment);

//        更新订单结算状态
        manufacterOrderServiceDomain.lambdaUpdate()
                .set(ManufacturerOrder::getSettleStatus, SettleStatusEnum.NO_SETTLE)
                .in(ManufacturerOrder::getCode, purchasePayment.getManufacterOrderCodes().split(","))
                .update();
                
        // 更新采购订单状态 - 关闭付款单后同步采购订单状态
        List<String> purchaseCodes = Arrays.stream(purchasePayment.getPurchaseCodes().split(","))
                .distinct()
                .collect(Collectors.toList());
        for (String purchaseCode : purchaseCodes) {
            // 查询该采购单下的所有供应商订单
            List<ManufacturerOrder> relatedOrders = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getPurchaseCode, purchaseCode)
                    .list();
            // 调用已有的状态同步方法
            manufacterOrderService.updatePurchaseOrderStatusByManufacturerOrders(purchaseCode, relatedOrders);
        }
        return this.purchasePaymentServiceDomain.updateById(purchasePayment);
    }

    /**
     * 保存凭证
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveVoucher(PurchasePayVoucherParam voucherParam) {
        PurchasePayment purchasePayment = this.purchasePaymentServiceDomain.lambdaQuery().eq(PurchasePayment::getCode, voucherParam.getCode()).one();
        if (purchasePayment == null) {
            throw new MaginaException("未找到付款单" + voucherParam.getCode());
        }
        purchasePayment.setRemark(voucherParam.getRemark());
        voucherParam.getPurchasePayVoucherList().forEach(v -> {
            v.setPaymentCode(voucherParam.getCode());
        });
        purchasePayVoucherServiceDomain.saveOrUpdateBatch(voucherParam.getPurchasePayVoucherList());

        Long payAmount = voucherParam.getPurchasePayVoucherList().stream().mapToLong(PurchasePayVoucher::getAmount).sum();

        //设置付款单状态
        purchasePayment.setStatus(payAmount.compareTo(purchasePayment.getActureAmount()) == 0 ? PurchasePayStatusEnum.COMPLETED : PurchasePayStatusEnum.PART_PAY);
        purchasePayment.setPaymentId(ApplicationSessions.id());
        purchasePayment.setPaymentName(ApplicationSessions.name());
        purchasePayment.setPaymentTime(LocalDateTime.now());
        purchasePaymentServiceDomain.updateById(purchasePayment);

        //支付完成且
        if (PurchasePayStatusEnum.COMPLETED.equals(purchasePayment.getStatus())) {
            //更新订单结算状态
            String[] manufacterOrderCodes = purchasePayment.getManufacterOrderCodes().split(",");
            manufacterOrderServiceDomain.lambdaUpdate()
                    .set(ManufacturerOrder::getSettleStatus, SettleStatusEnum.SETTLED)
                    .in(ManufacturerOrder::getCode, manufacterOrderCodes)
                    .update();

            //为现结再生成发货单
            if (purchasePayment.getSettleMethod().getValue().equals(ManufacturerOrder.SETTLEMENT_NOW)) {
                ExecutorUtils.doAfterCommit(() -> {
                    manufacterDeliveryService.createManufacterDelivery(purchasePayment.getManufacterOrderCodes());
                });
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 退回已抵扣的供应商余额
     * @param payment 采购付款单
     */
    private void refundDeduction(PurchasePayment payment) {
        Long deduction = payment.getDeductionAmount();
        if (deduction != null && deduction > 0) {
            Manufacturer manufacturer = manufacturerServiceDomain.getById(payment.getManufacturerId());
            manufacturer.setFundBalance(manufacturer.getFundBalance() + deduction);
            manufacturerServiceDomain.updateById(manufacturer);
            payment.setActureAmount(payment.getActureAmount() + deduction);
            payment.setDeductionAmount(0L);
        }
    }
}
