package com.hightop.benyin.logcontrol.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 配置分发关系Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper
public interface ConfigDistributionMapper extends BaseMapper<ConfigDistribution> {
    
    /**
     * 根据目标类型和ID查找激活的分发关系（优先返回分发关系中的配置，不检查配置是否激活）
     */
    @Select("SELECT cd.*, lc.config_version as assignedVersion, lc.config_name as configName, " +
            "di.current_config_version as currentVersion, " +
            "CASE " +
            "  WHEN di.current_config_version = lc.config_version THEN 'APPLIED' " +
            "  WHEN di.current_config_version IS NULL THEN 'PENDING' " +
            "  ELSE 'ASSIGNED' " +
            "END as distributionStatus " +
            "FROM b_config_distribution cd " +
            "LEFT JOIN b_log_config lc ON cd.config_id = lc.id " +
            "LEFT JOIN b_device_info di ON cd.target_id = di.device_id AND cd.target_type = 'DEVICE' " +
            "WHERE cd.target_type = #{targetType} AND cd.target_id = #{targetId} " +
            "AND cd.is_active = 1 AND cd.deleted = 0 AND lc.deleted = 0 " +
            "ORDER BY cd.assign_time DESC")
    List<ConfigDistribution> findActiveByTarget(
        @Param("targetType") String targetType,
        @Param("targetId") String targetId
    );
    
    /**
     * 查找所有分发关系（带状态计算）
     */
    @Select("<script>" +
            "SELECT cd.*, lc.config_version as assignedVersion, lc.config_name as configName, " +
            "di.current_config_version as currentVersion, " +
            "CASE " +
            "  WHEN di.current_config_version = lc.config_version THEN 'APPLIED' " +
            "  WHEN di.current_config_version IS NULL THEN 'PENDING' " +
            "  ELSE 'ASSIGNED' " +
            "END as distributionStatus " +
            "FROM b_config_distribution cd " +
            "LEFT JOIN b_log_config lc ON cd.config_id = lc.id " +
            "LEFT JOIN b_device_info di ON cd.target_id = di.device_id AND cd.target_type = 'DEVICE' " +
            "WHERE cd.deleted = 0 AND lc.deleted = 0 " +
            "<if test='targetType != null and targetType != \"\"'>" +
            "AND cd.target_type = #{targetType} " +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (cd.target_id LIKE CONCAT('%', #{keyword}, '%') OR cd.target_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY cd.assign_time DESC" +
            "</script>")
    List<ConfigDistribution> findDistributionsWithStatus(
        @Param("targetType") String targetType,
        @Param("keyword") String keyword
    );
    
    /**
     * 检查配置分发关系是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM b_config_distribution " +
            "WHERE config_id = #{configId} AND target_type = #{targetType} " +
            "AND target_id = #{targetId} AND deleted = 0")
    boolean existsByConfigAndTarget(
        @Param("configId") Long configId,
        @Param("targetType") String targetType,
        @Param("targetId") String targetId
    );
}
