package com.hightop.benyin.appupdate.application.service;

import com.hightop.benyin.appupdate.api.dto.UpdateCheckRequest;
import com.hightop.benyin.appupdate.api.dto.UpdateCheckResponse;
import com.hightop.benyin.appupdate.domain.service.AppVersionDomainService;
import com.hightop.benyin.appupdate.domain.service.AppVersionDistributionDomainService;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 应用更新服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional(propagation = Propagation.NOT_SUPPORTED)
@Slf4j
public class AppUpdateService {

    AppVersionDomainService appVersionDomainService;
    AppVersionDistributionDomainService distributionDomainService;
    
    /**
     * 检查应用更新（改造后的核心方法）
     * @param request 更新检查请求
     * @return 更新检查响应
     */
    public UpdateCheckResponse checkUpdate(UpdateCheckRequest request) {
        // 1. 优先检查用户专属版本（定向发布）
        AppVersion targetVersion = getTargetVersionForUser(request);

        // 2. 如果没有专属版本，获取全局最新版本
        if (targetVersion == null) {
            targetVersion = appVersionDomainService.getLatestActiveGlobalVersion();
        }

        if (targetVersion == null) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }

        // 3. 检查是否需要更新
        boolean needUpdate = shouldUpdate(request.getCurrentVersionCode(), targetVersion);

        if (!needUpdate) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }

        // 4. 构建更新响应
        UpdateCheckResponse response = new UpdateCheckResponse()
                .setHasUpdate(true)
                .setVersionName(targetVersion.getVersionName())
                .setVersionCode(targetVersion.getVersionCode())
                .setDownloadUrl(targetVersion.getCosUrl())  // 直接使用COS URL
                .setUpdateLog(targetVersion.getUpdateLog())
                .setIsForce(determineForceUpdate(request.getCurrentVersionCode(), targetVersion))
                .setFileSize(targetVersion.getFileSize())
                .setFileMd5(targetVersion.getFileMd5());

        return response;
    }

    /**
     * 获取用户的目标版本（新增方法）
     * @param request 更新检查请求
     * @return 目标版本，如果没有则返回null
     */
    private AppVersion getTargetVersionForUser(UpdateCheckRequest request) {
        // 如果没有用户ID或设备ID，跳过定向检查
        if (request.getUserId() == null && request.getDeviceId() == null) {
            return null;
        }

        AppVersion userVersion = null;

        // 1. 优先检查用户专属版本
        if (request.getUserId() != null) {
            userVersion = getVersionByTarget("USER", request.getUserId());
        }

        // 2. 如果没有用户专属版本，检查设备专属版本
        if (userVersion == null && request.getDeviceId() != null) {
            userVersion = getVersionByTarget("DEVICE", request.getDeviceId());
        }

        return userVersion;
    }

    /**
     * 根据目标类型和ID获取版本
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 版本信息
     */
    private AppVersion getVersionByTarget(String targetType, String targetId) {
        List<AppVersionDistribution> distributions =
            distributionDomainService.getActiveDistributionsByTarget(targetType, targetId);

        if (distributions.isEmpty()) {
            return null;
        }

        // 获取最新的分发版本
        Long versionId = distributions.get(0).getVersionId();
        AppVersion version = appVersionDomainService.getById(versionId);

        // 确保版本是激活状态
        return (version != null && version.getIsActive()) ? version : null;
    }

    /**
     * 下载APK文件
     * @param versionId 版本ID
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @Transactional
    public void downloadApk(Long versionId, HttpServletRequest request, HttpServletResponse response) {
        AppVersion version = appVersionDomainService.getById(versionId);
        if (version == null || !version.getIsActive()) {
            throw new MaginaException("版本不存在或已禁用");
        }
        
        try {
            // 增加下载计数
            appVersionDomainService.incrementDownloadCount(versionId);

            // 重定向到COS下载链接
            response.sendRedirect(version.getCosUrl());

        } catch (IOException e) {
            log.error("下载APK文件失败", e);
            throw new MaginaException("下载失败");
        }
    }
    
    /**
     * 判断是否需要更新
     * @param currentVersionCode 当前版本号
     * @param latestVersion 最新版本
     * @return 是否需要更新
     */
    private boolean shouldUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新标志优先级最高
        if (latestVersion.getAdminForce()) {
            return true;
        }
        
        // 版本号比较
        return currentVersionCode < latestVersion.getVersionCode();
    }
    
    /**
     * 确定是否强制更新
     * @param currentVersionCode 当前版本号
     * @param latestVersion 最新版本
     * @return 是否强制更新
     */
    private Boolean determineForceUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新
        if (latestVersion.getAdminForce()) {
            return true;
        }
        
        // 版本配置的强制更新
        return latestVersion.getIsForce();
    }
    
    /**
     * 构建下载URL
     * @param versionId 版本ID
     * @return 下载URL
     */
    private String buildDownloadUrl(Long versionId) {
        return String.format("/app/download/%s", versionId.toString());
    }
    

}
