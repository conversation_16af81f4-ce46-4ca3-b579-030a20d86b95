package com.hightop.benyin.configurer;

import com.hightop.fario.base.exception.FarioException;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.configuration.GlobalExceptionAdvice;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Objects;
import java.util.concurrent.CompletionException;

/**
 * 扩展异常拦截
 * <AUTHOR>
 * @date 2024/01/30 10:44
 */
@RestControllerAdvice
public class ExpandExceptionAdvice extends GlobalExceptionAdvice {
    /**
     * 处理数据库唯一约束冲突异常
     * @param e {@link DuplicateKeyException}
     * @return {@link RestResponse}
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.OK)
    public RestResponse<?> handleDuplicateKeyException(DuplicateKeyException e) {
        String message = e.getMessage();

        // 处理应用版本相关的唯一约束冲突
        if (message != null && message.contains("b_app_version")) {
            if (message.contains("uk_version_name_deleted")) {
                return new RestResponse<>(400, "版本名称已存在", null, null);
            } else if (message.contains("uk_version_code_deleted")) {
                return new RestResponse<>(400, "版本号已存在", null, null);
            }
        }

        // 其他唯一约束冲突
        return new RestResponse<>(400, "数据重复，请检查输入信息", null, null);
    }

    /**
     * 处理{@link java.util.concurrent.CompletableFuture}异常
     * @param e {@link CompletionException}
     * @return {@link RestResponse}
     */
    @ExceptionHandler(CompletionException.class)
    @ResponseStatus(HttpStatus.OK)
    public RestResponse<?> handleCompletionException(CompletionException e) {
        Throwable cause = e.getCause();
        if (Objects.isNull(cause)) {
            return super.handleException(e);
        }

        if (cause instanceof FarioException) {
            return super.handleFarioException((FarioException) cause);
        }

        return super.handleException((Exception) cause);
    }
}
