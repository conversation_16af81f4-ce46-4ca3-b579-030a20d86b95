package com.hightop.benyin.appupdate.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 定向发布DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("定向发布")
public class TargetedReleaseDto {

    @ApiModelProperty("目标用户ID列表（兼容旧版本）")
    List<String> userIds;

    @ApiModelProperty("目标设备ID列表（兼容旧版本）")
    List<String> deviceIds;

    @ApiModelProperty("目标用户组ID列表（兼容旧版本）")
    List<String> groupIds;

    @ApiModelProperty("目标用户详细信息列表")
    List<TargetUserInfo> users;

    @ApiModelProperty("目标设备详细信息列表")
    List<TargetDeviceInfo> devices;

    @ApiModelProperty("目标用户组详细信息列表")
    List<TargetGroupInfo> groups;

    @ApiModelProperty("是否覆盖现有分发关系")
    Boolean overrideExisting = false;

    /**
     * 目标用户信息
     */
    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @ApiModel("目标用户信息")
    public static class TargetUserInfo {
        @ApiModelProperty("用户ID")
        String id;

        @ApiModelProperty("用户名称")
        String name;
    }

    /**
     * 目标设备信息
     */
    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @ApiModel("目标设备信息")
    public static class TargetDeviceInfo {
        @ApiModelProperty("设备ID")
        String id;

        @ApiModelProperty("设备名称")
        String name;
    }

    /**
     * 目标用户组信息
     */
    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @ApiModel("目标用户组信息")
    public static class TargetGroupInfo {
        @ApiModelProperty("用户组ID")
        String id;

        @ApiModelProperty("用户组名称")
        String name;
    }
}
