package com.hightop.benyin.statistics.api.dto;

import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Builder
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("财务账款-批量核销DTO")
public class FinanceVerifyCancelBatchDto {

    @ApiModelProperty("核销至少有一个")
    @Size(min = 1, message = "核销至少有一个")
    List<FinanceVerifyCancel> financeVerifyCancelList;

    @ApiModelProperty("客户id")
    Long customerId;


    @ApiModelProperty("供应商名称(应付款明细)")
    String manufacturerName;

    @ApiModelProperty("供应商编码(应付款明细)")
    String manufacturerCode;

    @ApiModelProperty("凭证图片")
    CosObjectList voucherImg;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    String remark;
}
