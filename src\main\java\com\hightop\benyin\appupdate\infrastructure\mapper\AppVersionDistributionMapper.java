package com.hightop.benyin.appupdate.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应用版本分发关系Mapper
 * <AUTHOR>
 * @date 2025-01-29
 */
@Mapper
public interface AppVersionDistributionMapper extends MPJBaseMapper<AppVersionDistribution> {
    
    /**
     * 根据目标类型和ID查询激活的分发关系
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 分发关系列表
     */
    @Select("SELECT * FROM b_app_version_distribution " +
            "WHERE target_type = #{targetType} AND target_id = #{targetId} " +
            "AND is_active = 1 AND deleted = 0 " +
            "ORDER BY assign_time DESC")
    List<AppVersionDistribution> findActiveByTarget(@Param("targetType") String targetType, 
                                                   @Param("targetId") String targetId);
    
    /**
     * 根据版本ID查询所有分发关系（包括未激活的）
     * @param versionId 版本ID
     * @return 分发关系列表
     */
    @Select("SELECT * FROM b_app_version_distribution " +
            "WHERE version_id = #{versionId} AND deleted = 0 " +
            "ORDER BY is_active DESC, created_at DESC")
    List<AppVersionDistribution> findByVersionId(@Param("versionId") Long versionId);

    /**
     * 根据版本ID查询激活状态的分发关系
     * @param versionId 版本ID
     * @return 激活状态的分发关系列表
     */
    @Select("SELECT * FROM b_app_version_distribution " +
            "WHERE version_id = #{versionId} AND is_active = 1 AND deleted = 0 " +
            "ORDER BY created_at DESC")
    List<AppVersionDistribution> findActiveByVersionId(@Param("versionId") Long versionId);
}
