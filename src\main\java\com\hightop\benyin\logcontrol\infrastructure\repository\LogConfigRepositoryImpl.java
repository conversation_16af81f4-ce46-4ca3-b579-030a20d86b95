package com.hightop.benyin.logcontrol.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hightop.benyin.logcontrol.domain.entity.LogConfig;
import com.hightop.benyin.logcontrol.domain.repository.LogConfigRepository;
import com.hightop.benyin.logcontrol.infrastructure.mapper.LogConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 日志配置Repository实现
 * <AUTHOR>
 * @date 2025-01-21
 */
@Repository
public class LogConfigRepositoryImpl implements LogConfigRepository {

    @Autowired
    private LogConfigMapper logConfigMapper;

    @Override
    public boolean save(LogConfig logConfig) {
        return logConfigMapper.insert(logConfig) > 0;
    }

    @Override
    public boolean update(LogConfig logConfig) {
        return logConfigMapper.updateById(logConfig) > 0;
    }

    @Override
    public LogConfig findById(Long id) {
        return logConfigMapper.selectById(id);
    }

    @Override
    public LogConfig findByIdIgnoreStatus(Long id) {
        // 直接根据ID查询，不检查激活状态和删除状态
        LambdaQueryWrapper<LogConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogConfig::getId, id);
        wrapper.eq(LogConfig::getDeleted, false); // 只检查是否被删除，不检查激活状态
        return logConfigMapper.selectOne(wrapper);
    }

    @Override
    public LogConfig findActiveConfig() {
        return logConfigMapper.findActiveConfig();
    }

    @Override
    public LogConfig findByConfigName(String configName) {
        return logConfigMapper.findByConfigName(configName);
    }

    @Override
    public LogConfig findByConfigVersion(String configVersion) {
        return logConfigMapper.findByConfigVersion(configVersion);
    }

    @Override
    public List<LogConfig> findAll() {
        LambdaQueryWrapper<LogConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogConfig::getDeleted, false);
        wrapper.orderByDesc(LogConfig::getCreatedAt);
        return logConfigMapper.selectList(wrapper);
    }

    @Override
    public int deactivateAllConfigs() {
        return logConfigMapper.deactivateAllConfigs();
    }

    @Override
    public int activateConfig(Long id) {
        return logConfigMapper.activateConfig(id);
    }

    @Override
    public boolean deleteById(Long id) {
        return logConfigMapper.deleteById(id) > 0;
    }
}
