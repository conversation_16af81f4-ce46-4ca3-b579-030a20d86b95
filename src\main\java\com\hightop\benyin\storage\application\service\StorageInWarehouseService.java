package com.hightop.benyin.storage.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.items.store.domain.service.ApplyReturnOrderServiceDomain;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.order.application.manager.TradeOrderManager;
import com.hightop.benyin.order.application.service.TradeOrderService;
import com.hightop.benyin.order.domain.service.TradeOrderDomainService;
import com.hightop.benyin.order.infrastructure.enmu.TradeOrderCloseReasonEnum;
import com.hightop.benyin.order.infrastructure.enmu.TradeOrderStatusEnum;
import com.hightop.benyin.order.infrastructure.entity.TradeOrder;
import com.hightop.benyin.purchase.api.dto.ManufacterDeliveryVo;
import com.hightop.benyin.purchase.api.param.ManufacteReceiveParam;
import com.hightop.benyin.purchase.api.param.ManufacterDeliveryParam;
import com.hightop.benyin.purchase.domain.service.PurchaseOrderGoodsServiceDomain;
import com.hightop.benyin.purchase.domain.service.PurchaseOrderServiceDomain;
import com.hightop.benyin.purchase.infrastructure.entity.ManufacturerDeliveryRecord;
import com.hightop.benyin.purchase.infrastructure.entity.PurchaseOrder;
import com.hightop.benyin.purchase.infrastructure.entity.PurchaseOrderGoods;
import com.hightop.benyin.reverse.application.manager.ReverseOrderManager;
import com.hightop.benyin.reverse.domain.service.ReverseOrderServiceDomain;
import com.hightop.benyin.reverse.infrastructure.entity.ReverseOrder;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.storage.api.dto.excel.StorageInWarehouseExcel;
import com.hightop.benyin.storage.api.dto.excel.TakeDetailExcel;
import com.hightop.benyin.storage.api.dto.excel.TakeDetailExportExcel;
import com.hightop.benyin.storage.api.dto.query.StorageInWarehouseQuery;
import com.hightop.benyin.storage.api.dto.query.TakeStockQuery;
import com.hightop.benyin.storage.domain.event.InboundOrderAssociatedOrderCompleteEvent;
import com.hightop.benyin.storage.domain.event.InboundOrderCompletedEvent;
import com.hightop.benyin.storage.domain.service.*;
import com.hightop.benyin.storage.infrastructure.constant.StorageConstants;
import com.hightop.benyin.storage.infrastructure.entity.*;
import com.hightop.benyin.storage.infrastructure.enums.InOutTypeEnum;
import com.hightop.benyin.storage.infrastructure.enums.InStatusEnum;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓储管理-入库管理服务
 *
 * <AUTHOR>
 * @date 2023-11-13 20:05:11
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class StorageInWarehouseService {
    ApplicationEventPublisher applicationEventPublisher;
    /**
     * 入库管理
     */
    StorageInWarehouseServiceDomain storageInWarehouseServiceDomain;

    /**
     * 入库货品
     */
    StorageInWarehouseGoodsServiceDomain storageInWarehouseGoodsServiceDomain;

    /**
     * 流水记录
     */
    StorageWarehouseFlowServiceDomain storageWarehouseFlowServiceDomain;

    /**
     * 物品管理
     */
    StorageArticleServiceDomain storageArticleServiceDomain;

    /**
     * 库品管理
     */
    StorageInventoryServiceDomain storageInventoryServiceDomain;

    /**
     * 库品批次记录
     */
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;

    /**
     * 序列号服务
     */
    SequenceDomainService sequenceDomainService;
    /**
     * 组合商品配置
     */
    AssemblyArticleConfigServiceDomain assemblyArticleConfigServiceDomain;

    /**
     * 售后单-主表
     */
    ReverseOrderServiceDomain reverseOrderServiceDomain;

    /**
     * 订单服务
     */
    TradeOrderDomainService tradeOrderDomainService;

    /**
     * 订单详情服务
     */
    TradeOrderService tradeOrderService;

    /**
     * 订单服务2
     */
    TradeOrderManager tradeOrderManager;


    ReverseOrderManager reverseOrderManager;

    /**
     * 采购单-商品信息服务
     */
    PurchaseOrderGoodsServiceDomain purchaseOrderGoodsServiceDomain;
    PurchaseOrderServiceDomain purchaseOrderServiceDomain;

    TakeDetailDomainService takeDetailDomainService;

    /**
     * 列表查询
     *
     * @return {@link List}
     */
    public List<StorageInWarehouse> list() {
        return this.storageInWarehouseServiceDomain.list();
    }

    /**
     * 分页查询
     *
     * @param pageQuery {@link StorageInWarehouseQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<StorageInWarehouse> page(StorageInWarehouseQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.storageInWarehouseServiceDomain.selectJoinList(
                        StorageInWarehouse.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(StorageInWarehouse.class)
                                .selectAs(Warehouse::getType, StorageInWarehouse::getInType)
                                .selectAs(Warehouse::getName, StorageInWarehouse::getWarehouseName)
                                .selectAs(UserBasic::getName, StorageInWarehouse::getOperatorName)
                                .leftJoin(Warehouse.class, Warehouse::getId, StorageInWarehouse::getWarehouseId)
                                .leftJoin(UserBasic.class, UserBasic::getId, StorageInWarehouse::getOperatorId)
                                .eq(Objects.nonNull(pageQuery.getInType()), StorageInWarehouse::getInType, pageQuery.getInType())
                                .eq(Objects.nonNull(pageQuery.getInStatus()), StorageInWarehouse::getInStatus, pageQuery.getInStatus())
                                .eq(Objects.nonNull(pageQuery.getWarehouseId()), Warehouse::getId, pageQuery.getWarehouseId())
                                .like(
                                        StringUtils.isNotBlank(pageQuery.getInWarehouseId()), StorageInWarehouse::getInWarehouseId,
                                        pageQuery.getInWarehouseId()
                                )
                                .eq(Objects.nonNull(pageQuery.getOperatorId()), UserBasic::getId, pageQuery.getOperatorId())
                                .eq(StringUtils.isNotBlank(pageQuery.getShopWaybill()), StorageInWarehouse::getShopWaybill, pageQuery.getShopWaybill())
                                .ge(
                                        Objects.nonNull(pageQuery.getMinInTime()), StorageInWarehouse::getInWarehouseTime,
                                        pageQuery.getMinInTime()
                                )
                                .le(
                                        Objects.nonNull(pageQuery.getMaxInTime()), StorageInWarehouse::getInWarehouseTime,
                                        pageQuery.getMaxInTime()
                                )
                                .orderByDesc(StorageInWarehouse::getId)
                )
        );
    }

    /**
     * 添加
     *
     * @param storageInWarehouse {@link StorageInWarehouse}
     * @return true/false
     */
    public boolean save(StorageInWarehouse storageInWarehouse) {
        List<StorageInWarehouseGoods> goodsList = storageInWarehouse.getInWarehouseGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new MaginaException("入库货品不能为空");
        }
        String rkid = this.sequenceDomainService.nextDateSequence(
                StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
        storageInWarehouse.setInWarehouseId(rkid);
        // 为整个入库单生成一个批次号
        String batchCode = this.sequenceDomainService.nextDateSequence("", 3);
        // 默认添加为初始库存
        storageInWarehouse.setInType(InOutTypeEnum.ORIGINALLY);
        storageInWarehouse.setInStatus(InStatusEnum.DRK);
        storageInWarehouse.setAuditInWarehouseNumber(0);
        int sum = 0;
        List<StorageInWarehouseGoods> saveList = new ArrayList<>();
        for (StorageInWarehouseGoods goods : goodsList) {
            if (goods.getArticleType().equals(1)) {
                List<AssemblyArticleConfig> assemblyArticleConfigs = assemblyArticleConfigServiceDomain.getAssemblyArticleConfigByParentArticleCode(goods.getCode());
                for (AssemblyArticleConfig assemblyArticleConfig : assemblyArticleConfigs) {
                    StorageInWarehouseGoods storageInWarehouseGoods = new StorageInWarehouseGoods();
                    storageInWarehouseGoods.setInWarehouseId(rkid);
                    storageInWarehouseGoods.setWarehouseId(goods.getWarehouseId());
                    storageInWarehouseGoods.setAuditInWarehouseNumber(0);
                    storageInWarehouseGoods.setPrice(assemblyArticleConfig.getPrice());
                    storageInWarehouseGoods.setCode(assemblyArticleConfig.getArticleCode());
                    storageInWarehouseGoods.setName(assemblyArticleConfig.getArticleName());
                    // 使用入库单的统一批次号
                    storageInWarehouseGoods.setBatchCode(batchCode);
                    storageInWarehouseGoods.setInWarehouseNumber(goods.getInWarehouseNumber());
                    storageInWarehouseGoods.setWarehouseId(storageInWarehouse.getWarehouseId());
                    sum += goods.getInWarehouseNumber();
                    saveList.add(storageInWarehouseGoods);
                }
            } else {
                // 自动生成批次号（如果为空，使用入库单的统一批次号）
                if (StringUtils.isBlank(goods.getBatchCode())) {
                    goods.setBatchCode(batchCode);
                }
                goods.setInWarehouseId(rkid);
                goods.setAuditInWarehouseNumber(0);
                goods.setWarehouseId(storageInWarehouse.getWarehouseId());
                sum += goods.getInWarehouseNumber();
                saveList.add(goods);
            }
        }
        storageInWarehouseGoodsServiceDomain.saveBatch(saveList);
        storageInWarehouse.setInWarehouseNumber(sum);

        return storageInWarehouseServiceDomain.save(storageInWarehouse);
    }

    /**
     * 导入库品
     *
     * @param file
     * @return
     */
    public String impor(MultipartFile file, Long ckid) {
        List<StorageInWarehouseExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            excels = ExcelImportUtil.importExcel(in, StorageInWarehouseExcel.class, params);
        } catch (Exception e) {
            return "解析失败";
        }
        if (excels.size() > 0) {
            String inWarehouseId = sequenceDomainService.nextDateSequence(
                    StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
            //存--入库货品
            ArrayList<StorageInWarehouseGoods> inWarehouseGoodsList = new ArrayList<>();
            Integer inNumber = 0;
            for (StorageInWarehouseExcel data : excels) {
                //检查编号是否存在物品表里
                LambdaQueryWrapper<StorageArticle> articleWrapper = new LambdaQueryWrapper<>();
                articleWrapper.eq(StorageArticle::getCode, data.getCode());
                StorageArticle article = storageArticleServiceDomain.getOne(articleWrapper);
                if (article == null) {
                    throw new MaginaException(
                            "导入失败,物品编号:" + data.getCode() + ",在[物品管理]不存在,请添加后再导入");
                }
                StorageInWarehouseGoods inWarehouseGoods = new StorageInWarehouseGoods();
                //赋值
                BeanUtils.copyProperties(data, inWarehouseGoods);
                inWarehouseGoods.setInWarehouseId(inWarehouseId);
                inWarehouseGoods.setWarehouseId(ckid);
                inWarehouseGoods.setAuditInWarehouseNumber(0);
                inWarehouseGoodsList.add(inWarehouseGoods);
                inNumber = inNumber + data.getInWarehouseNumber();
            }
            storageInWarehouseGoodsServiceDomain.saveBatch(inWarehouseGoodsList);

            //存--入库主表
            StorageInWarehouse inWarehouse = new StorageInWarehouse();
            inWarehouse.setInWarehouseId(inWarehouseId);
            inWarehouse.setInType(InOutTypeEnum.ORIGINALLY);
            inWarehouse.setWarehouseId(ckid);
            inWarehouse.setInStatus(InStatusEnum.DRK);
            inWarehouse.setOperatorId(ApplicationSessions.id());
            //入库时间根据审核的来
            inWarehouse.setInWarehouseNumber(inNumber);
            inWarehouse.setAuditInWarehouseNumber(0);
            storageInWarehouseServiceDomain.save(inWarehouse);
        }
        return "导入成功";
    }


    /**
     * 下载库品模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<StorageInWarehouseExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "入库模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    StorageInWarehouseExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    public StorageInWarehouse getOne(Integer id) {
        StorageInWarehouse inWarehouse = storageInWarehouseServiceDomain.getById(id);
        // 补充oem编号
        List<StorageInWarehouseGoods> list = this.storageInWarehouseGoodsServiceDomain.selectJoinList(
                StorageInWarehouseGoods.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(StorageInWarehouseGoods.class)
                        .selectAs(StorageArticle::getNumberOem, StorageInWarehouseGoods::getNumberOem)
                        .selectAs(StorageArticle::getId, StorageInWarehouseGoods::getStorageArticleId)
                        // 物品图片
                        .selectAs(StorageArticle::getImageFiles, StorageInWarehouseGoods::getArticleImages)
                        .innerJoin(StorageArticle.class, StorageArticle::getCode, StorageInWarehouseGoods::getCode)
                        .eq(StorageInWarehouseGoods::getInWarehouseId, inWarehouse.getInWarehouseId())
        );
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                //如果该物品之前已经有入库的储位信息，那储位这里，自动带出该物品最近保存的储位信息。可修改
                StorageInventory inventory =
                        this.storageInventoryServiceDomain.getByWarehouseAndCode(e.getWarehouseId(), e.getCode());
                // 默认库存量
                e.setSumWarehouseNumber(0);

                Optional.ofNullable(inventory)
                        // 库存存在 设置储位和真实库存量
                        .ifPresent(it -> {
                            e.setLocation(it.getLocation());
                            e.setSumWarehouseNumber(it.getSumWarehouseNumber());
                        });
            });
        }
        inWarehouse.setInWarehouseGoodsList(list);
        return inWarehouse;
    }

    /**
     * 修改库存的储位信息
     *
     * @param siwg
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2023/12/20 14:51
     */
    public boolean fixLocation(StorageInWarehouseGoods siwg) {
        StorageInWarehouseGoods exist = this.storageInWarehouseGoodsServiceDomain.getById(siwg.getId());
        StorageInventory inventory = storageInventoryServiceDomain.getByWarehouseAndCode(
                exist.getWarehouseId(), exist.getCode());
        if (inventory == null) {
            throw new MaginaException("当前仓库没有所属物品,请先入库");
        }
        if (null != exist) {
            // 快速修改该库品在指定仓库的储位
            return this.storageInventoryServiceDomain.lambdaUpdate()
                    .set(StorageInventory::getLocation, siwg.getLocation())
                    .eq(StorageInventory::getWarehouseId, exist.getWarehouseId())
                    .eq(StorageInventory::getCode, exist.getCode())
                    .update();
        }
        return false;
    }

    /**
     * 库品入库审核
     *
     * @param siwg
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2023/12/19 17:36
     * <p>
     * 入库审核 --> StorageInWarehouse 入库行为 + StorageInWarehouseGoods 入库商品
     * StorageInWarehouse 注意有部分入库和全量入库行为
     * 审核生成入库
     * StorageInventoryBatch  生成批次
     * StorageWarehouseFlow   生成出入库流水
     * StorageInventory   修改生成库存数量
     * if  全量入库
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean goodsAudit(StorageInWarehouseGoods siwg) throws Exception {
        StorageInWarehouseGoods exist = this.storageInWarehouseGoodsServiceDomain.getById(siwg.getId());
        // 审核实际入库数量
        int actual = exist.getAuditInWarehouseNumber() + siwg.getAuditInWarehouseNumber();
        if (exist.getInWarehouseNumber() < actual) {
            throw new MaginaException("审核入库数量不能大于应入库数量");
        }
        Long operator = ApplicationSessions.id();
        LocalDateTime now = LocalDateTime.now();
        exist.setBatchCode(siwg.getBatchCode());
        exist.setPrice(siwg.getPrice());
        // 审核入库数量追加
        exist.setAuditInWarehouseNumber(actual);
        exist.setInWarehouseTime(now);
        exist.setOperatorId(operator);
        this.storageInWarehouseGoodsServiceDomain.updateById(exist);
        //根据主表id查询库品表  并 修改状态
        StorageInWarehouse inWarehouse = storageInWarehouseServiceDomain.getByCode(siwg.getInWarehouseId());
        inWarehouse.setInWarehouseTime(now);
        // 实际入库总数
        int actualTotal = inWarehouse.getAuditInWarehouseNumber() + siwg.getAuditInWarehouseNumber();
        inWarehouse.setAuditInWarehouseNumber(actualTotal);
        inWarehouse.setReviewerId(operator);
        // 根据库品的入库数量更新入库单的出库状态
        inWarehouse.setInStatus(Objects.equals(0, actualTotal) ?
                InStatusEnum.DRK : (Objects.equals(inWarehouse.getInWarehouseNumber(), actualTotal)) ?
                InStatusEnum.YRK : InStatusEnum.BFRK);
        storageInWarehouseServiceDomain.updateById(inWarehouse);
        // 入库批次、流水记录、库存修改
        this.extracted(exist, siwg.getAuditInWarehouseNumber(), inWarehouse.getInType(), now, operator);
        if (InStatusEnum.YRK == inWarehouse.getInStatus()) {
            // 发送入库单完成通知
            StorageConstants.doAfterCommit(() ->
                    this.applicationEventPublisher.publishEvent(
                            InboundOrderCompletedEvent.builder()
                                    .inOutType(inWarehouse.getInType())
                                    .associatedOrderNumber(inWarehouse.getShopWaybill())
                                    .reverseOrderNumber(inWarehouse.getReverseOrderId())
                                    .build()
                    )
            );

            // 检查是否关联订单、逆向单下的所有入库单都完成
            List<StorageInWarehouse> orders =
                    this.storageInWarehouseServiceDomain.getByAssociatedOrder(
                            inWarehouse.getShopWaybill(),
                            inWarehouse.getReverseOrderId(),
                            inWarehouse.getInType()
                    );

            boolean isAllFinished = true;
            for (StorageInWarehouse order : orders) {
                if (order.getInStatus() != InStatusEnum.YRK) {
                    isAllFinished = false;
                    break;
                }
            }

            // 所有入库单都完成发送关联单号入库完成事件
            if (isAllFinished) {
                StorageConstants.doAfterCommit(() ->
                        this.applicationEventPublisher.publishEvent(
                                InboundOrderAssociatedOrderCompleteEvent.builder()
                                        .associatedOrderNumber(inWarehouse.getShopWaybill())
                                        .reverseOrderNumber(inWarehouse.getReverseOrderId())
                                        .inOutType(inWarehouse.getInType())
                                        .build()
                        )
                );
            }
        }
        return true;
    }

    /**
     * 入库明细
     *
     * @param id
     * @return: {@link List< StorageWarehouseFlow>}
     * @Author: xhg
     * @Date: 2023/12/20 10:16
     */
    public List<StorageWarehouseFlow> goodsInWareDetail(Long id) {
        StorageInWarehouseGoods exist = this.storageInWarehouseGoodsServiceDomain.getById(id);
        if (null == exist) {
            return new ArrayList<>();
        }
        // 根据入库编号+库品编号来查询流水明细
        return this.storageWarehouseFlowServiceDomain.selectJoinList(
                StorageWarehouseFlow.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(StorageWarehouseFlow.class)
                        .selectAs(UserBasic::getName, StorageWarehouseFlow::getOperatorName)
                        .leftJoin(UserBasic.class, UserBasic::getId, StorageWarehouseFlow::getOperatorId)
                        .eq(StorageWarehouseFlow::getFlowId, exist.getInWarehouseId())
                        .eq(StorageWarehouseFlow::getCode, exist.getCode())
                        .eq(StorageWarehouseFlow::getInOutType, 1)
                        .orderByAsc(StorageWarehouseFlow::getCreatedAt)
        );
    }


    /**
     * 退货入库
     *
     * @param inWarehouse
     * @return:
     * @Author: xhg
     * @Date: 2023/12/19 17:01
     */
    private void returnGoods(StorageInWarehouseGoods goods,
                             StorageInWarehouse inWarehouse) throws Exception {
        // TODO xiehai 考虑所有入库单是否都需要计算在途
        //售后管理-在途量-计算
        LambdaQueryWrapper<StorageInventory> storageInventoryWrapper = new LambdaQueryWrapper<>();
        storageInventoryWrapper.eq(StorageInventory::getCode, goods.getCode());
        storageInventoryWrapper.eq(StorageInventory::getWarehouseId, inWarehouse.getWarehouseId());
        StorageInventory storageInventory = storageInventoryServiceDomain.getOne(storageInventoryWrapper);
        if (storageInventory != null) {
            //更新在途量
            storageInventoryServiceDomain.subtractRunNumber(storageInventory, goods.getInWarehouseNumber());
        }
        //通知售后单，已入库
        ReverseOrder reverseOrder = reverseOrderServiceDomain.getById(inWarehouse.getReverseOrderId());
        reverseOrderManager.returnBackConfirmEntry(reverseOrder.getCode());

        //修改订单售后中状态
        tradeOrderManager.setTradeReverseStatus(reverseOrder.getTradeOrderNum(), false);
        //关闭订单-只有当订单没有完成，且为全部退款的时候
        TradeOrder tradeOrder = tradeOrderDomainService.getById(reverseOrder.getTradeOrderId());
        Boolean allReverseSuccess = reverseOrderManager.isAllReverseSuccess(reverseOrder.getTradeOrderId(), null);
        if (!tradeOrder.getOrderStatus().equals(TradeOrderStatusEnum.SUCCESS) && allReverseSuccess) {
            tradeOrderService.closeTradeOrder(tradeOrder.getOrderNum(), TradeOrderCloseReasonEnum.REVERSE.getValue());
        }
    }

    /**
     * 审核时,对库品数据操作
     *
     * @param inWarehouse
     * @param warehouseGoodsList
     */
    @Deprecated
    private void extracted2(StorageInWarehouse inWarehouse, List<StorageInWarehouseGoods> warehouseGoodsList) {
        if (warehouseGoodsList.size() > 0) {
            //存--批次表
            ArrayList<StorageInventoryBatch> inventoryBatchList = new ArrayList<>();
            ArrayList<StorageWarehouseFlow> flows = new ArrayList<>();
            for (StorageInWarehouseGoods data : warehouseGoodsList) {
                StorageInventoryBatch inventoryBatch = new StorageInventoryBatch();
                BeanUtils.copyProperties(data, inventoryBatch);
                inventoryBatch.setId(null);
                inventoryBatch.setWarehouseId(data.getWarehouseId());
                inventoryBatch.setPrice(data.getPrice());
                inventoryBatch.setSumWarehouseNumber(data.getInWarehouseNumber());
                inventoryBatch.setRemWarehouseNumber(data.getInWarehouseNumber());
                inventoryBatch.setInWarehouseTime(LocalDateTime.now());
                inventoryBatch.setInWarehouseType(inWarehouse.getInType());
                inventoryBatchList.add(inventoryBatch);

                //流水表
                StorageWarehouseFlow flow = new StorageWarehouseFlow();
                flow.setInOutType(1);
                flow.setBatchCode(data.getBatchCode());
                flow.setFlowId(data.getInWarehouseId());
                flow.setCode(data.getCode());
                flow.setName(data.getName());
                flow.setWarehouseId(data.getWarehouseId());
                flow.setNumber(data.getInWarehouseNumber());
                flow.setTime(LocalDateTime.now());
                flow.setType(inWarehouse.getInType());
                flow.setOperatorId(inWarehouse.getOperatorId());
                flows.add(flow);
            }
            storageInventoryBatchServiceDomain.saveBatch(inventoryBatchList);
            storageWarehouseFlowServiceDomain.saveBatch(flows);

            //存--库品表
            //根据物品code分组
            Map<String, List<StorageInWarehouseGoods>> inventoryExcelList = warehouseGoodsList.stream().collect(
                    Collectors.groupingBy(StorageInWarehouseGoods::getCode));
            //获取导入的code
            List<String> codeList = warehouseGoodsList.stream().map(StorageInWarehouseGoods::getCode).distinct()
                    .collect(Collectors.toList());

            //根据 物品code/仓库id 查询库品表
            for (String code : codeList) {
                LambdaQueryWrapper<StorageInventory> storageInventoryWrapper = new LambdaQueryWrapper<>();
                storageInventoryWrapper.eq(StorageInventory::getCode, code);
                storageInventoryWrapper.eq(StorageInventory::getWarehouseId, inWarehouse.getWarehouseId());
                StorageInventory storageInventory = storageInventoryServiceDomain.getOne(storageInventoryWrapper);

                List<StorageInWarehouseGoods> goodsList = inventoryExcelList.get(code);
                BigDecimal sumNumber = new BigDecimal(0);
                for (StorageInWarehouseGoods goods : goodsList) {
                    sumNumber = sumNumber.add(new BigDecimal(goods.getInWarehouseNumber()));
                }
                //有,更新     没有 ,新增一条数据
                if (storageInventory != null) {
                    //有,更新 库存量
                    storageInventoryServiceDomain.addSumNumber(storageInventory, sumNumber.intValue());
                } else {
                    //没有 ,新增一条数据
                    //通过 code 查询 物品基本信息
                    LambdaQueryWrapper<StorageArticle> articleWrapper = new LambdaQueryWrapper<>();
                    articleWrapper.eq(StorageArticle::getCode, code);
                    StorageArticle article = storageArticleServiceDomain.getOne(articleWrapper);

                    //封装 物品/仓库/初始化库存
                    StorageInventory inventory = new StorageInventory();
                    inventory.setCode(article.getCode());
                    inventory.setName(article.getName());
                    inventory.setArticleType(article.getArticleType());
                    inventory.setWarehouseId(inWarehouse.getWarehouseId());
                    //库存计算
                    inventory.setSumWarehouseNumber(sumNumber.intValue());
                    inventory.setOutWarehouseNumber(0);
                    inventory.setRunWarehouseNumber(0);
                    storageInventoryServiceDomain.save(inventory);
                }
            }
        }
    }

    /**
     * 单库品审核操作
     *
     * @param data        库品实体
     * @param auditNumber 审核入库数量
     * @param type        入库类型
     * @param now         时间
     * @param operator    操作人
     * @return:
     * @Author: xhg
     * @Date: 2023/12/20 9:54
     */
    private void extracted(StorageInWarehouseGoods data,
                           int auditNumber,
                           InOutTypeEnum type,
                           LocalDateTime now,
                           Long operator) {
        //批次表
        StorageInventoryBatch inventoryBatch = new StorageInventoryBatch();
        BeanUtils.copyProperties(data, inventoryBatch);
        inventoryBatch.setId(null);
        inventoryBatch.setWarehouseId(data.getWarehouseId());
        inventoryBatch.setPrice(data.getPrice());
        inventoryBatch.setSumWarehouseNumber(auditNumber);
        inventoryBatch.setRemWarehouseNumber(auditNumber);
        inventoryBatch.setInWarehouseTime(now);
        inventoryBatch.setInWarehouseType(type);
        inventoryBatch.setTax(data.getTax());
        //流水表
        StorageWarehouseFlow flow = new StorageWarehouseFlow();
        flow.setInOutType(1);
        flow.setBatchCode(data.getBatchCode());
        flow.setFlowId(data.getInWarehouseId());
        flow.setCode(data.getCode());
        flow.setName(data.getName());
        flow.setWarehouseId(data.getWarehouseId());
        flow.setNumber(auditNumber);
        flow.setTime(now);
        flow.setType(type);
        flow.setOperatorId(operator);

        storageWarehouseFlowServiceDomain.save(flow);

        boolean isNewInStock = type.equals(InOutTypeEnum.PURCHASE) || type.equals(InOutTypeEnum.ORIGINALLY);
        StorageInventoryBatch closest =
                this.storageInventoryBatchServiceDomain.getOneClosest(
                        data.getWarehouseId(), data.getCode(), data.getBatchCode(),isNewInStock, data.getInWarehouseId()
                );
        if (Objects.isNull(closest)) {
            this.storageInventoryBatchServiceDomain.save(inventoryBatch);
        } else {
            inventoryBatch = storageInventoryBatchServiceDomain.getById(closest.getId());
            inventoryBatch.setRemWarehouseNumber(inventoryBatch.getRemWarehouseNumber() + auditNumber);
            //判断是采购 初始入库的时候批次不同则要新增批次
            if(isNewInStock){
                inventoryBatch.setSumWarehouseNumber(inventoryBatch.getSumWarehouseNumber() + auditNumber);
            }
            this.storageInventoryBatchServiceDomain.updateById(inventoryBatch);
        }

        //根据 物品code/仓库id 查询库品表
        StorageInventory storageInventory =
                this.storageInventoryServiceDomain.getByWarehouseAndCode(data.getWarehouseId(), data.getCode());
        //有,更新     没有 ,新增一条数据
        if (storageInventory != null) {
            //有,更新 库存量
            storageInventory.setSumWarehouseNumber(storageInventory.getSumWarehouseNumber() + auditNumber);
            storageInventoryServiceDomain.updateById(storageInventory);
        } else {
            //没有 ,新增一条数据
            //通过 code 查询 物品基本信息
            StorageArticle article = storageArticleServiceDomain.lambdaQuery()
                    .eq(StorageArticle::getCode, data.getCode()).one();
            //封装 物品/仓库/初始化库存
            StorageInventory inventory = new StorageInventory();
            inventory.setCode(article.getCode());
            inventory.setName(article.getName());
            inventory.setWarehouseId(data.getWarehouseId());
            inventory.setArticleType(article.getArticleType());
            //库存计算
            inventory.setSumWarehouseNumber(auditNumber);
            inventory.setOutWarehouseNumber(0);
            inventory.setRunWarehouseNumber(0);
            storageInventoryServiceDomain.save(inventory);
        }
    }


    /**
     * 列表-备注修改
     *
     * @param storageInWarehouse
     * @return
     */
    public boolean updateRemarks(StorageInWarehouse storageInWarehouse) {
        LambdaQueryWrapper<StorageInWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageInWarehouse::getId, storageInWarehouse.getId());
        StorageInWarehouse inWarehouse = storageInWarehouseServiceDomain.getOne(wrapper);
        inWarehouse.setRemarks(storageInWarehouse.getRemarks());
        boolean update = storageInWarehouseServiceDomain.updateById(inWarehouse);
        return update;
    }

    /**
     * 采购生成入库单
     *
     * @param manufacteReceiveParam
     * @return
     */
    public Boolean createStorageInWarehouse(ManufacteReceiveParam manufacteReceiveParam) {
        // ---------- 生成入库单 ----------
        String rkid = sequenceDomainService.nextDateSequence(StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
        //入库单货品
        ArrayList<StorageInWarehouseGoods> inGoodsList = new ArrayList<>();

        String orderCode = manufacteReceiveParam.getDeliveryDetails().get(0).getManufacturerOrderCode();
        for (ManufacturerDeliveryRecord manufacterDeliveryVo : manufacteReceiveParam.getDeliveryDetails()) {

            PurchaseOrderGoods goods = purchaseOrderGoodsServiceDomain.selectJoinOne(PurchaseOrderGoods.class,MPJWrappers.lambdaJoin()
                    .selectAll(PurchaseOrderGoods.class)
                    .selectAs(Manufacturer::getTax, PurchaseOrderGoods::getTax)
                    .leftJoin(Manufacturer.class, Manufacturer::getId, PurchaseOrderGoods::getManufacturerId)
                    .eq(PurchaseOrderGoods::getId,manufacterDeliveryVo.getPurchaseOrderGoodsId()));

            int quantity = manufacterDeliveryVo.getCurrNum().intValue();
            // 若采购复核时数量小于等于0 则不生成入库单
            if (quantity <= 0) {
                continue;
            }
            //套装商品入明细
            if (goods.getArticleType().equals(1)) {
                JSONArray priceDistributionJson = goods.getPriceDistribution();
                List<ArticlePrice> priceDistribution = priceDistributionJson != null ? JSONArray.parseArray(priceDistributionJson.toJSONString(), ArticlePrice.class) : Lists.newArrayList();
                priceDistribution.forEach(price -> {
                    StorageInWarehouseGoods storageInWarehouseGoods = build(price.getPrice(), rkid, manufacteReceiveParam.getWarehouseId(), price.getArticleCode(), quantity);
                    storageInWarehouseGoods.setBatchCode(manufacterDeliveryVo.getBatchCode());
                    inGoodsList.add(storageInWarehouseGoods);
                });
            } else {
                StorageInWarehouseGoods storageInWarehouseGoods = build(goods.getPrice(), rkid, manufacteReceiveParam.getWarehouseId(), goods.getArticleCode(), quantity);
                storageInWarehouseGoods.setBatchCode(manufacterDeliveryVo.getBatchCode());
                storageInWarehouseGoods.setTax(goods.getTax());
                inGoodsList.add(storageInWarehouseGoods);
            }
        }
        storageInWarehouseGoodsServiceDomain.saveBatch(inGoodsList);
        Integer quantity = inGoodsList.stream().mapToInt(StorageInWarehouseGoods::getInWarehouseNumber).sum();
        //入库单主表
        StorageInWarehouse inWarehouse = new StorageInWarehouse();
        inWarehouse.setInWarehouseId(rkid);
        inWarehouse.setWarehouseId(manufacteReceiveParam.getWarehouseId());
        // 入库类型为采购入库
        inWarehouse.setInType(InOutTypeEnum.PURCHASE);
        // 添加采购订单号
        inWarehouse.setShopWaybill(orderCode);
        // 入库状态为待入库
        inWarehouse.setInStatus(InStatusEnum.DRK);
        inWarehouse.setInWarehouseNumber(quantity);
        inWarehouse.setAuditInWarehouseNumber(0);

        return storageInWarehouseServiceDomain.save(inWarehouse);
    }

    private StorageInWarehouseGoods build(Long price, String rkid, Long warehouseId, String articleCode, Integer num) {
        StorageInWarehouseGoods inGoods = new StorageInWarehouseGoods();
        inGoods.setInWarehouseId(rkid);
        LambdaQueryWrapper<StorageArticle> articleWrapper = new LambdaQueryWrapper<>();
        articleWrapper.eq(StorageArticle::getCode, articleCode);
        StorageArticle article = storageArticleServiceDomain.getOne(articleWrapper);
        inGoods.setCode(article.getCode());
        inGoods.setName(article.getName());
        inGoods.setWarehouseId(warehouseId);
        inGoods.setInWarehouseNumber(num);
        inGoods.setAuditInWarehouseNumber(0);
        inGoods.setCode(article.getCode());
        inGoods.setName(article.getName());
        inGoods.setPrice(price);
        return inGoods;
    }

    /**
     * 导出耗材盘点
     *
     * @param response
     * @return
     */
    public Boolean downloadArticleData(HttpServletResponse response, TakeStockQuery pageQuery) throws IOException {
        try {
            //查询数据
            TakeDetailExportExcel totalDetailExportExcel = new TakeDetailExportExcel();
            List<TakeDetailExportExcel> excelList = this.takeDetailDomainService.selectJoinList(TakeDetailExportExcel.class, MPJWrappers.lambdaJoin()
                    .selectAll(TakeDetail.class)
                    .selectAs(TakeStock::getCode, TakeDetailExcel::getTakeStockCode)
                    .selectAs(StorageArticle::getManufacturerGoodsCode, TakeDetailExcel::getManufacturerGoodsCode)
                    .selectAs(StorageArticle::getManufacturerGoodsName, TakeDetailExcel::getManufacturerGoodsName)
                    .leftJoin(TakeStock.class, TakeStock::getId, TakeDetail::getTakeStockId)
                    .leftJoin(StorageArticle.class, StorageArticle::getCode, TakeDetail::getArticleCode)
                    .eq(TakeDetail::getTakeStockId, pageQuery.getTakeStockId())
            ).stream().peek(item -> {
                if(item.getPrice() == null || item.getPrice() == 0){
                    item.setRealPrice(BigDecimal.valueOf(0));
                }else {
                    item.setRealPrice(BigDecimal.valueOf(item.getPrice()));
                }
                item.setInventoryAmountDecimal(item.getRealPrice(), item.getInventoryNum());
                item.setStockAmountDecimal(item.getRealPrice(), item.getStockNum());
                totalDetailExportExcel.setInventoryAmountDecimal(totalDetailExportExcel.getInventoryAmountDecimal() == null? BigDecimal.valueOf(0) : totalDetailExportExcel.getInventoryAmountDecimal().add(item.getInventoryAmountDecimal()));
                totalDetailExportExcel.setStockAmountDecimal(totalDetailExportExcel.getStockAmountDecimal() == null? BigDecimal.valueOf(0) : totalDetailExportExcel.getStockAmountDecimal().add(item.getStockAmountDecimal()));
                totalDetailExportExcel.setInventoryNum((totalDetailExportExcel.getInventoryNum() == null? 0 : totalDetailExportExcel.getInventoryNum()) + item.getInventoryNum());
                totalDetailExportExcel.setStockNum((totalDetailExportExcel.getStockNum() == null? 0 : totalDetailExportExcel.getStockNum()) + item.getStockNum());
            }).collect(Collectors.toList());
            totalDetailExportExcel.setTakeStockCode("总计");
            excelList.add(0,totalDetailExportExcel);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "盘点明细数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), TakeDetailExportExcel.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public List<String> updateinwarehouse(ArrayList<String> wpList) {
        ArrayList<String> result = new ArrayList<>();
        for (String wp : wpList) {
            List<StorageInWarehouseGoods> list = storageInWarehouseGoodsServiceDomain.lambdaQuery().
                    eq(StorageInWarehouseGoods::getCode, wp).
                    orderByDesc(StorageInWarehouseGoods::getCreatedAt).
                    eq(StorageInWarehouseGoods::getWarehouseId, 1731282648590000130L).list();
            if (CollectionUtils.isEmpty(list)) {
                result.add(wp);
                continue;
            }
            StorageInWarehouseGoods storageInWarehouseGoods = new StorageInWarehouseGoods();
            for (StorageInWarehouseGoods warehouseGoods : list) {
                if (warehouseGoods.getPrice() != null && warehouseGoods.getPrice() > 0) {
                    storageInWarehouseGoods.setPrice(warehouseGoods.getPrice());
                    break;
                }
            }
            if (storageInWarehouseGoods.getPrice() == null) {
                result.add(wp);
                continue;
            }
            storageInWarehouseGoodsServiceDomain.update(new LambdaUpdateWrapper<StorageInWarehouseGoods>().
                    set(StorageInWarehouseGoods::getPrice, storageInWarehouseGoods.getPrice()).
                    eq(StorageInWarehouseGoods::getCode, wp).
                    eq(StorageInWarehouseGoods::getWarehouseId, 1731282648590000130L).
                    isNull(StorageInWarehouseGoods::getPrice).
                    or().
                    eq(StorageInWarehouseGoods::getPrice, 0L));
        }
        return result;
    }

}
