package com.hightop.benyin.work.order.schedule;

import com.hightop.benyin.customer.domain.service.CustomerDeviceGroupDomainService;
import com.hightop.benyin.customer.infrastructure.enums.SerTypeEnums;
import com.hightop.benyin.work.order.domain.service.WorkOrderDomainService;
import com.hightop.benyin.work.order.infrastructure.entity.WorkOrder;
import com.hightop.benyin.work.order.infrastructure.enums.WorkOrderStatus;
import com.hightop.magina.standard.task.job.ScheduleJob;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单定时任务
 *
 * <AUTHOR>
 * @date 2023/11/22 11:03
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Component
@Slf4j
public class WorkOrderSchedule {

    WorkOrderDomainService workOrderDomainService;
    CustomerDeviceGroupDomainService customerDeviceGroupDomainService;

    @ScheduleJob(id = 20013L, cron = "0 0/5 * * * ? *", description = "自动确认工单")
    public void autoConfirm() {
        List<WorkOrder> list = workOrderDomainService.lambdaQuery()
                .isNotNull(WorkOrder::getSendReportTime)
                .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_CONFIRMED_REPORT)
                .eq(WorkOrder::getTotalPay, 0L)
                .ne(WorkOrder::getSerType, SerTypeEnums.SCATTERED)
                .apply(" TIMESTAMPDIFF(HOUR, send_report_time, now()) >= {0}", 24)
                .isNull(WorkOrder::getConfirmReportTime).list();
        if(CollectionUtils.isEmpty( list)){
            return;
        }
        log.info("-----------------自动确认工单开始");
        List<Long> ids = list.stream().map(WorkOrder::getId).collect(Collectors.toList());
        workOrderDomainService.lambdaUpdate()
                .set(WorkOrder::getStatus, WorkOrderStatus.COMPLETED)
                .set(WorkOrder::getConfirmReportTime, LocalDateTime.now())
                .in(WorkOrder::getId, ids)
                .update();
        log.info("自动确认工单成功,数量:{}, 工单id:{}", list.size(), ids);
//        List<WorkOrder> workOrders = workOrderDomainService.lambdaQuery()
//                .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_CONFIRMED_REPORT)
//                .ne(WorkOrder::getSerType, SerTypeEnums.SCATTERED).list();
//        workOrders.forEach(workOrder -> {
//            if (workOrder.getSendReportTime().plusHours(24).isBefore(LocalDateTime.now())) {
//                workOrder.setStatus(WorkOrderStatus.COMPLETED);
//                workOrderDomainService.updateById(workOrder);
//
//                //设备组状态 正常
//                this.customerDeviceGroupDomainService.updateDeviceStatus(
//                        workOrder.getDeviceGroupId(), CustomerDeviceGroup.DEVICE_STATUS_NORMAL
//                );
//            }
//        });
    }
}
