package com.hightop.benyin.machine.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.machine.api.dto.excel.MachineExcel;
import com.hightop.benyin.machine.api.dto.query.MachinePageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineTotalVo;
import com.hightop.benyin.machine.application.handler.MachineExcelVerifyHandler;
import com.hightop.benyin.machine.domain.service.MachineDomainService;
import com.hightop.benyin.machine.domain.service.MachineInoutFlowDomainService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachineInoutFlow;
import com.hightop.benyin.machine.infrastructure.enums.MachineInOutType;
import com.hightop.benyin.machine.infrastructure.enums.MachineSource;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.product.domain.service.ProductAccessoryDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeAccessoryDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductAccessory;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.product.infrastructure.entity.ProductTreeAccessory;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/1/2 17:47
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MachineService {
    MachineDomainService machineDomainService;
    SequenceDomainService sequenceDomainService;
    ProductTreeDomainService productTreeDomainService;
    ProductAccessoryDomainService productAccessoryDomainService;
    ProductTreeAccessoryDomainService productTreeAccessoryDomainService;
    MachineInoutFlowDomainService machineInoutFlowDomainService;


    public DataGrid<Machine> pageList(MachinePageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.getMachineList(pageQuery))
                .peek(machine -> {
                    Optional.ofNullable(this.productTreeDomainService.getFullProductTree(machine.getProductId()))
                            .ifPresent(info -> {
                                machine.setBrand(info.getBrand());
                            });
                    MachineInoutFlow machineInoutFlow = machineInoutFlowDomainService.lambdaQuery()
                            .eq(MachineInoutFlow::getMachineNum, machine.getMachineNum())
                            .eq(MachineInoutFlow::getSourceType, MachineInOutType.PURCHASE)
                            .orderByAsc(MachineInoutFlow::getCreatedAt)
                            .last("LIMIT 1")
                            .one();
                    if(machineInoutFlow!= null){

                    }
                });
    }

    private List<Machine> getMachineList(MachinePageQuery pageQuery) {
        //适配主机的选配件
        if (StringUtils.isNotBlank(pageQuery.getHostType()) && !pageQuery.getHostType().equals(Machine.HOST_TYPE_MACHINE)
                && CollectionUtils.isNotEmpty(pageQuery.getProductIds())) {
            List<ProductTreeAccessory> productAccessories = productTreeAccessoryDomainService.lambdaQuery()
                    .in(ProductTreeAccessory::getProductId, pageQuery.getProductIds())
                    .list();
            List<Long> accessoryIds = productAccessories.stream().map(ProductTreeAccessory::getAccessoryId).collect(Collectors.toList());
            pageQuery.setAccessoryIds(accessoryIds);
        }

        return machineDomainService.selectJoinList(Machine.class, MPJWrappers.lambdaJoin()
                .selectAll(Machine.class)
                .selectAs(Manufacturer::getName, Machine::getManufacturerName)
                .selectAs(Manufacturer::getCode, Machine::getManufacturerCode)
                .leftJoin(ProductTree.class, ProductTree::getId, Machine::getProductId)
                .leftJoin(Manufacturer.class, Manufacturer::getId, Machine::getManufacturerId)
                .like(StringUtils.isNotBlank(pageQuery.getManufacturerName()), Manufacturer::getName, pageQuery.getManufacturerName())
                .like(StringUtils.isNotBlank(pageQuery.getMachineNum()), Machine::getMachineNum, pageQuery.getMachineNum())
                .like(StringUtils.isNotBlank(pageQuery.getOriginCode()), Machine::getOriginCode, pageQuery.getOriginCode())
                .like(StringUtils.isNotBlank(pageQuery.getTagName()), Machine::getTagName, pageQuery.getTagName())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceSequence()), Machine::getDeviceSequence, pageQuery.getDeviceSequence())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceOn()), Machine::getDeviceOn, pageQuery.getDeviceOn())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceStatus()), Machine::getDeviceStatus, pageQuery.getDeviceStatus())
                .like(StringUtils.isNotBlank(pageQuery.getHostType()), Machine::getHostType, pageQuery.getHostType())
                .like(StringUtils.isNotBlank(pageQuery.getProductName()), Machine::getProductName, pageQuery.getProductName())
                .like(StringUtils.isNotBlank(pageQuery.getBindMachine()), Machine::getBindMachine, pageQuery.getBindMachine())
                .like(StringUtils.isNotBlank(pageQuery.getLocation()), Machine::getLocation, pageQuery.getLocation())
                .in(CollectionUtils.isNotEmpty(pageQuery.getAccessoryIds()), Machine::getProductId, pageQuery.getAccessoryIds())
                .notIn(CollectionUtils.isNotEmpty(pageQuery.getNotInStatus()), Machine::getStatus, pageQuery.getNotInStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getProductIds()), Machine::getProductId, pageQuery.getProductIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getDeviceOns()), Machine::getDeviceOn, pageQuery.getDeviceOns())
                .in(CollectionUtils.isNotEmpty(pageQuery.getDeviceStatusList()), Machine::getDeviceStatus, pageQuery.getDeviceStatusList())
                .in(CollectionUtils.isNotEmpty(pageQuery.getHostTypes()), Machine::getHostType, pageQuery.getHostTypes())
                .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), Machine::getStatus, pageQuery.getStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getPercentage()), Machine::getPercentage, pageQuery.getPercentage())
                .ne(StringUtils.isNotBlank(pageQuery.getExistType()), Machine::getHostType, pageQuery.getExistType())
                .eq(pageQuery.getProductId() != null, Machine::getProductId, pageQuery.getProductId())
                .isNotNull(pageQuery.getIsBind() != null && pageQuery.getIsBind(), Machine::getBindMachine)
                .isNull(pageQuery.getIsBind() != null && !pageQuery.getIsBind(), Machine::getBindMachine)
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), Machine::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), Machine::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(pageQuery.getStartBlackCounter()), Machine::getBlackWhiteCounter, pageQuery.getStartBlackCounter())
                .le(StringUtils.isNotBlank(pageQuery.getEndBlackCounter()), Machine::getBlackWhiteCounter, pageQuery.getEndBlackCounter())
                .ge(StringUtils.isNotBlank(pageQuery.getStartColorCounter()), Machine::getColorCounter, pageQuery.getStartColorCounter())
                .le(StringUtils.isNotBlank(pageQuery.getEndColorCounter()), Machine::getColorCounter, pageQuery.getEndColorCounter())
                .ge(StringUtils.isNotBlank(pageQuery.getStartFiveColourCounter()), Machine::getFiveColourCounter, pageQuery.getStartFiveColourCounter())
                .le(StringUtils.isNotBlank(pageQuery.getEndFiveColourCounter()), Machine::getFiveColourCounter, pageQuery.getEndFiveColourCounter())
                .eq(pageQuery.getIsSale() != null, Machine::getIsSale, pageQuery.getIsSale())
                .ne( Machine::getStatus, MachineStatus.WAIT_IN)
                .orderByDesc(Machine::getMachineNum)
        );
    }

    public MachineTotalVo getMachineTotal() {
        MachineTotalVo machineTotalVo = new MachineTotalVo();
        List<Machine> machines = machineDomainService.selectJoinList(Machine.class, MPJWrappers.lambdaJoin()
                .select(Machine::getId, Machine::getStatus, Machine::getPurchasePrice,Machine::getHostType)
        );
        Long totalAmount = machines.stream().filter(v -> v.getPurchasePrice() != null).map(Machine::getPurchasePrice).reduce(Long::sum).orElse(0L);
        machineTotalVo.setTotalAmount(totalAmount);
        Long stockAmount = machines.stream().filter(machine ->machine.getPurchasePrice() != null&& machine.getStatus() != null && (machine.getStatus().equals(MachineStatus.INVENTORY) || machine.getStatus().equals(MachineStatus.REPAIR))).mapToLong(Machine::getPurchasePrice).sum();
        Long stockNumber = machines.stream().filter(machine -> machine.getPurchasePrice() != null && machine.getStatus() != null && (machine.getStatus().equals(MachineStatus.INVENTORY) || machine.getStatus().equals(MachineStatus.REPAIR))).count();
        machineTotalVo.setStockAmount(stockAmount);
        machineTotalVo.setStockNumber(stockNumber);
        Long rentAmount = machines.stream().filter(machine -> machine.getPurchasePrice() != null&&machine.getStatus() != null && machine.getStatus().equals(MachineStatus.RENT)).mapToLong(Machine::getPurchasePrice).sum();
        Long rentNumber = machines.stream().filter(machine -> machine.getPurchasePrice() != null && machine.getStatus() != null && machine.getStatus().equals(MachineStatus.RENT)).count();
        machineTotalVo.setRentAmount(rentAmount);
        machineTotalVo.setRentNumber(rentNumber);
        Long machineAmount = machines.stream().filter(machine -> machine.getStatus() != null && (machine.getStatus().equals(MachineStatus.INVENTORY) || machine.getStatus().equals(MachineStatus.REPAIR)) &&
                machine.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)
                && machine.getPurchasePrice() != null).map(Machine::getPurchasePrice).reduce(Long::sum).orElse(0L);
        machineTotalVo.setMachineAmount(machineAmount);
        Long accessoryAmount = machines.stream().filter(machine -> machine.getStatus() != null && (machine.getStatus().equals(MachineStatus.INVENTORY) || machine.getStatus().equals(MachineStatus.REPAIR)) &&
                !machine.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)
                && machine.getPurchasePrice() != null).map(Machine::getPurchasePrice).reduce(Long::sum).orElse(0L);
        machineTotalVo.setAccessoryAmount(accessoryAmount);
        return machineTotalVo;
    }


    public Boolean updateLocation(Machine machine) {
        if (machine.getId() == null) {
            throw new MaginaException("机器ID不能为空");
        }
        return machineDomainService.lambdaUpdate()
                .set(Machine::getLocation, machine.getLocation())
                .eq(Machine::getId, machine.getId())
                .update();
    }

    /**
     * 添加机器
     *
     * @param machine
     * @return
     */
    public Boolean add(Machine machine) {
        if (machine.getStatus() == null) {
            machine.setStatus(MachineStatus.INVENTORY);
        }
        if (machine.getId() == null) {
            machine.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            machine.setMachineNum(sequenceDomainService.nextDateSequence("MC", 4));
            machine.setSource(MachineSource.MANUAL);
        } else {
            machine.setUpdatedBy(ApplicationSessions.id());
        }

        if (machine.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
            ProductTree productTree = productTreeDomainService.getById(machine.getProductId());
            machine.setProductName(productTree.getName());
        } else {
            ProductAccessory productAccessory = productAccessoryDomainService.getById(machine.getProductId());
            machine.setProductName(productAccessory.getModeType());
        }
        if (CollectionUtils.isNotEmpty(machine.getAccessories())) {
            List<Long> accessoryIds = machine.getAccessories().stream().map(Machine::getId).collect(Collectors.toList());
            machineDomainService.lambdaUpdate()
                    .set(Machine::getBindMachine, null)
                    .eq(Machine::getBindMachine, machine.getMachineNum())
                    .update();

            machineDomainService.lambdaUpdate()
                    .set(Machine::getBindMachine, machine.getMachineNum())
                    .in(Machine::getId, accessoryIds)
                    .update();
        }

        return machineDomainService.saveOrUpdate(machine);
    }

    /**
     * 添加机器
     *
     * @param machines
     * @return
     */
    public Boolean batchSave(List<Machine> machines) {
        machines.forEach(machine -> {
            machine.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            machine.setStatus(MachineStatus.INVENTORY);
        });
        return machineDomainService.saveBatch(machines);
    }


    /**
     * 根据ID获取明细
     *
     * @param id
     * @return
     */
    public Machine getById(Long id) {
        Machine machine = machineDomainService.selectJoinOne(Machine.class, MPJWrappers.lambdaJoin()
                .selectAll(Machine.class)
                .selectAs(Manufacturer::getName, Machine::getManufacturerName)
                .selectAs(ProductTree::getFullIdPath, Machine::getFullProductPath)
                .leftJoin(ProductTree.class, ProductTree::getId, Machine::getProductId)
                .leftJoin(Manufacturer.class, Manufacturer::getId, Machine::getManufacturerId)
                .eq(Machine::getId, id)
        );
        List<Machine> machines = machineDomainService.lambdaQuery().eq(Machine::getBindMachine, machine.getMachineNum()).list();
        machine.setAccessories(machines);
        return machine;
    }

    /**
     * 导出资产流程
     *
     * @param response
     * @return
     */
    public Boolean downloadData(HttpServletResponse response, MachinePageQuery pageQuery) throws IOException {
        try {
            //查询数据
            List<Machine> excelList = this.getMachineList(pageQuery);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "机器数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), Machine.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<MachineExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "机器导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    MachineExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 导入基站信息
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<MachineExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new MachineExcelVerifyHandler());
            ExcelImportResult<MachineExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, MachineExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<MachineExcel>> codeListMap = excels.stream().collect(Collectors.groupingBy(MachineExcel::getOriginCode));

            for (Map.Entry<String, List<MachineExcel>> entry : codeListMap.entrySet()) {
                List<MachineExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("机器编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }
        //存数据库
        if (com.hightop.fario.base.util.CollectionUtils.isNotEmpty(excels)) {
            List<Machine> machines = new ArrayList<>();
            for (MachineExcel data : excels) {
                Machine machine = data.getOriMachine() == null ? new Machine() : data.getOriMachine();
                BeanUtils.copyProperties(data, machine);
                if (StringUtils.isBlank(machine.getMachineNum())) {
                    machine.setMachineNum(sequenceDomainService.nextDateSequence("MC", 4));
                    machine.setSource(MachineSource.MANUAL);
                }
                machine.setHostType(new DictItemEntry().setValue(data.getHostType()));
                machine.setDeviceOn(new DictItemEntry().setValue(data.getDeviceOn()));
                machine.setPercentage(new DictItemEntry().setValue(data.getPercentage()));
                machine.setStatus(data.getMachineStatus());
                if (data.getPurchasePrice() != null) {
                    machine.setPurchasePrice(data.getPurchasePrice().multiply(DownloadResponseUtil.HUNDRED).longValue());
                }
                if (machine.getId() == null) {
                    machine.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                }
                machine.setUpdatedBy(ApplicationSessions.id());
                machines.add(machine);
            }
            this.machineDomainService.saveOrUpdateBatch(machines);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    public boolean removeById(Long id) {
        return this.machineDomainService.removeById(id);
    }
}
