package com.hightop.benyin.purchase.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.product.domain.service.PartProductTreeDomainService;
import com.hightop.benyin.purchase.api.dto.ManufacterDeliveryVo;
import com.hightop.benyin.purchase.api.dto.ManufacterOrderStatisticsVo;
import com.hightop.benyin.purchase.api.dto.ManufacterReturnDto;
import com.hightop.benyin.purchase.api.dto.PurchaseMechineVo;
import com.hightop.benyin.purchase.api.dto.query.ManufacterOrderQuery;
import com.hightop.benyin.purchase.api.dto.query.ManufacterReturnQuery;
import com.hightop.benyin.purchase.api.dto.query.OrderStatisticsPageQuery;
import com.hightop.benyin.purchase.api.param.ManufacterOrderAuditParam;
import com.hightop.benyin.purchase.domain.service.*;
import com.hightop.benyin.purchase.infrastructure.entity.*;
import com.hightop.benyin.purchase.infrastructure.enums.*;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.domain.service.StorageInWarehouseGoodsServiceDomain;
import com.hightop.benyin.storage.domain.service.StorageInWarehouseServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.entity.StorageInWarehouse;
import com.hightop.benyin.storage.infrastructure.entity.StorageInWarehouseGoods;
import com.hightop.benyin.storage.infrastructure.enums.InOutTypeEnum;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacyDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商订单服务
 *
 * <AUTHOR>
 * @date 2023-12-06 14:48:56
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class ManufacterOrderService {

    ManufacterOrderGoodsServiceDomain manufacterOrderGoodsServiceDomain;

    ManufacterOrderServiceDomain manufacterOrderServiceDomain;

    PartProductTreeDomainService partProductTreeDomainService;

    PurchaseOrderGoodsServiceDomain purchaseOrderGoodsServiceDomain;

    PurchaseOrderServiceDomain purchaseOrderServiceDomain;

    ManufacturerDeliveryServiceDomain manufacturerDeliveryServiceDomain;

    ManufacturerDeliveryRecordServiceDomain manufacturerDeliveryRecordServiceDomain;

    ManufacterReceiveServiceDomain manufacterReceiveServiceDomain;

    PurchasePaymentServiceDomain purchasePaymentServiceDomain;

    ManufacterDeliveryService manufacterDeliveryService;

    SequenceDomainService sequenceDomainService;

    UserPrivacyDomainService userPrivacyDomainService;

    ManufacturerServiceDomain manufacturerServiceDomain;

    StorageInWarehouseServiceDomain storageInWarehouseServiceDomain;
    StorageInWarehouseGoodsServiceDomain storageInWarehouseGoodsServiceDomain;
    //private final PurchaseOrderService purchaseOrderService;

    public DataGrid<ManufacturerOrder> page(ManufacterOrderQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterOrderServiceDomain.selectJoinList(ManufacturerOrder.class, MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerOrder.class)
                        .selectAs(Manufacturer::getName, ManufacturerOrder::getManufacturerName)
                        .leftJoin(Manufacturer.class, Manufacturer::getId, ManufacturerOrder::getManufacturerId)
                        .like(StringUtils.isNotBlank(query.getCompanyCode()), ManufacturerOrder::getCompanyCode, query.getCompanyCode())
                        .like(StringUtils.isNotBlank(query.getCode()), ManufacturerOrder::getCode, query.getCode())
                        .like(StringUtils.isNotBlank(query.getManufacturerName()), Manufacturer::getName, query.getManufacturerName())
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), ManufacturerOrder::getPurchaseCode, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getInitiatorName()), ManufacturerOrder::getInitiatorName, query.getInitiatorName())
                        .like(StringUtils.isNotBlank(query.getInitiatorPhone()), ManufacturerOrder::getInitiatorPhone, query.getInitiatorPhone())
                        .eq(StringUtils.isNotBlank(query.getSettleMethod()), ManufacturerOrder::getSettleMethod, query.getSettleMethod())
                        .eq(StringUtils.isNotBlank(query.getSettleStatus()), ManufacturerOrder::getSettleStatus, query.getSettleStatus())
                        .eq(query.getStatus() != null, ManufacturerOrder::getStatus, query.getStatus())
                        .eq(query.getInvoiceStatus() != null, ManufacturerOrder::getInvoiceStatus, query.getInvoiceStatus())
                        .in(query.getRefundStatus() != null, ManufacturerOrder::getRefundStatus, query.getRefundStatus())
                        .ge(query.getDeliveryTimeStart() != null, ManufacturerOrder::getDeliveryTime, query.getDeliveryTimeStart() + " 00:00:00")
                        .le(query.getDeliveryTimeEnd() != null, ManufacturerOrder::getDeliveryTime, query.getDeliveryTimeEnd() + " 23:59:59")
                        .orderByDesc(ManufacturerOrder::getCreatedAt)
                )

        );
    }

    /**
     * 创建供应商订单
     *
     * @param purchaseOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void createManufacterOrder(PurchaseOrder purchaseOrder) {
        Long count = manufacterOrderServiceDomain.lambdaQuery().eq(ManufacturerOrder::getPurchaseCode, purchaseOrder.getPurchaseCode()).count();
        if (count > 0L) {
            return;
        }
        List<PurchaseOrderGoods> goodsData = purchaseOrder.getPurchaseGoods();

        Map<Long, List<PurchaseOrderGoods>> purchaseOrderGoodsMap = goodsData
                .stream().collect(Collectors.groupingBy(PurchaseOrderGoods::getManufacturerId));
        for (Map.Entry<Long, List<PurchaseOrderGoods>> entry : purchaseOrderGoodsMap.entrySet()) {

            Manufacturer manufacturer = manufacturerServiceDomain.getById(entry.getKey());
            ManufacturerOrder manufacturerOrder = new ManufacturerOrder();
            String code = sequenceDomainService.nextDateSequence("CGID", 6);
            manufacturerOrder.setCode(code);
            manufacturerOrder.setReceiveCompany("四川至简智印科技有限公司");
            manufacturerOrder.setSettleMethod(manufacturer.getSettleMethod());
            manufacturerOrder.setWarehouseId(purchaseOrder.getWarehouseId());
            UserPrivacy privacy = this.userPrivacyDomainService.getById(purchaseOrder.getInitiatorId());
            manufacturerOrder.setInitiatorPhone(privacy.getMobileNumber().getValue());
            manufacturerOrder.setInitiatorId(purchaseOrder.getInitiatorId());
            manufacturerOrder.setStatus(ManufactureOrderStatusEnum.WAIT_AUDIT);
            BeanUtils.copyProperties(purchaseOrder, manufacturerOrder);
            manufacturerOrder.setManufacturerId(entry.getKey());
            manufacturerOrder.setId(null);
            manufacturerOrder.setRefundStatus(ManufactureReturnStatusEnum.NO);
            manufacturerOrder.setSettleStatus(SettleStatusEnum.NO_SETTLE);
            Long totalAmount = 0L;
            List<ManufacturerOrderGoods> manufacturerOrderGoodsList = Lists.newArrayList();
            for (PurchaseOrderGoods purchaseOrderGoods : entry.getValue()) {
                ManufacturerOrderGoods manufacturerOrderGoods = new ManufacturerOrderGoods();
                BeanUtils.copyProperties(purchaseOrderGoods, manufacturerOrderGoods);
                manufacturerOrderGoods.setManufacturerOrderCode(code);
                manufacturerOrderGoods.setNumber(purchaseOrderGoods.getApproveNum());
                manufacturerOrderGoods.setApproveNum(purchaseOrderGoods.getApproveNum());
                manufacturerOrderGoods.setPurchaseOrderGoodsId(purchaseOrderGoods.getId());
                Long amount = purchaseOrderGoods.getApproveNum() * purchaseOrderGoods.getPrice();
                manufacturerOrderGoods.setAmount(amount);
                totalAmount = totalAmount + amount;
                manufacturerOrderGoods.setId(null);
                manufacturerOrderGoodsList.add(manufacturerOrderGoods);
            }
            manufacturerOrder.setAmount(totalAmount);
            manufacterOrderServiceDomain.save(manufacturerOrder);
            manufacterOrderGoodsServiceDomain.saveBatch(manufacturerOrderGoodsList);
        }
    }

    public ManufacturerOrder getById(Long id) {
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.getById(id);
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = manufacterOrderGoodsServiceDomain.selectJoinList(ManufacturerOrderGoods.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerOrderGoods.class)
                        .selectAs(StorageArticle::getNumberOem, ManufacturerOrderGoods::getOemNumber)
                        .selectAs(StorageArticle::getName, ManufacturerOrderGoods::getArticleName)
                        .selectAs(StorageArticle::getUnit, ManufacturerOrderGoods::getUnit)
                        .selectAs(PurchaseOrderGoods::getManufacturerId, ManufacturerOrderGoods::getManufacturerId)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ManufacturerOrderGoods::getArticleCode)
                        .leftJoin(PurchaseOrderGoods.class, PurchaseOrderGoods::getId, ManufacturerOrderGoods::getPurchaseOrderGoodsId)
                        .eq(StorageArticle::getDeleted, false)
                        .eq(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode())
        );
        Manufacturer manufacturer = manufacturerServiceDomain.getById(manufacturerOrder.getManufacturerId());
        manufacturerOrder.setManufacturerName(manufacturer.getName());
        manufacturerOrderGoodsList = manufacturerOrderGoodsList.stream().filter(v -> v.getManufacturerId().equals(manufacturerOrder.getManufacturerId())).collect(Collectors.toList());
        manufacturerOrder.setManufacturerOrderGoodsList(manufacturerOrderGoodsList);
        return manufacturerOrder;
    }

    public ManufacturerOrder getByCode(String code) {
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                .eq(ManufacturerOrder::getCode, code).one();
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = manufacterOrderGoodsServiceDomain.selectJoinList(ManufacturerOrderGoods.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerOrderGoods.class)
                        .selectAs(StorageArticle::getNumberOem, ManufacturerOrderGoods::getOemNumber)
                        .selectAs(StorageArticle::getName, ManufacturerOrderGoods::getArticleName)
                        .selectAs(StorageArticle::getUnit, ManufacturerOrderGoods::getUnit)
                        .selectAs(PurchaseOrderGoods::getManufacturerId, ManufacturerOrderGoods::getManufacturerId)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ManufacturerOrderGoods::getArticleCode)
                        .leftJoin(PurchaseOrderGoods.class, PurchaseOrderGoods::getId, ManufacturerOrderGoods::getPurchaseOrderGoodsId)
                        .eq(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode())
        );
        Manufacturer manufacturer = manufacturerServiceDomain.getById(manufacturerOrder.getManufacturerId());
        manufacturerOrder.setManufacturerName(manufacturer.getName());
        manufacturerOrderGoodsList = manufacturerOrderGoodsList.stream().filter(v -> v.getManufacturerId().equals(manufacturerOrder.getManufacturerId())).collect(Collectors.toList());
        manufacturerOrder.setManufacturerOrderGoodsList(manufacturerOrderGoodsList);
        return manufacturerOrder;
    }

    /**
     * 审核
     *
     * @param auditParam
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(ManufacterOrderAuditParam auditParam) {
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.getById(auditParam.getId());
        if (manufacturerOrder == null) {
            throw new MaginaException("订单不存在");
        }

        if (!manufacturerOrder.getRefundStatus().equals(ManufactureReturnStatusEnum.NO)) {
            throw new MaginaException("当前订单退货状态不能审核");
        }

        manufacturerOrder.setStatus(auditParam.getStatus());
        if (!auditParam.getStatus().equals(ManufactureOrderStatusEnum.WAIT_PAY)) {
            manufacterOrderServiceDomain.updateById(manufacturerOrder);
            
            // 如果审核拒绝或关闭，检查是否需要关闭采购单
            if (auditParam.getStatus().equals(ManufactureOrderStatusEnum.REJECT) || 
                auditParam.getStatus().equals(ManufactureOrderStatusEnum.CLOSED)) {
                
                // 查询该采购单下的所有供应商订单
                List<ManufacturerOrder> relatedOrders = manufacterOrderServiceDomain.lambdaQuery()
                        .eq(ManufacturerOrder::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                        .list();
                
                // 判断是否都已关闭或拒绝
                boolean allClosed = relatedOrders.stream()
                        .allMatch(order -> 
                                ManufactureOrderStatusEnum.CLOSED.equals(order.getStatus()) ||
                                ManufactureOrderStatusEnum.REJECT.equals(order.getStatus()));
                
                // 如果所有供应商订单都已关闭或拒绝，则关闭采购单
                if (allClosed) {
                    PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                            .eq(PurchaseOrder::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                            .one();
                    
                    if (purchaseOrder != null) {
                        purchaseOrderServiceDomain.lambdaUpdate()
                                .set(PurchaseOrder::getOrderStatus, PurchaseOrderStatusEnum.CLOSE)
                                .eq(PurchaseOrder::getId, purchaseOrder.getId())
                                .update();
                    }
                } else {
                    // 如果不是所有供应商订单都已关闭，则根据剩余有效订单状态更新采购单状态
                    updatePurchaseOrderStatusByManufacturerOrders(manufacturerOrder.getPurchaseCode(), relatedOrders);
                }
            }
            
            return Boolean.TRUE;
        }
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = auditParam.getManufacturerOrderGoodsList();
        Long totalNum = 0L;
        for (ManufacturerOrderGoods manufacturerOrderGoods : manufacturerOrderGoodsList) {

            if (manufacturerOrderGoods.getNumber().compareTo(manufacturerOrderGoods.getApproveNum()) > 0) {
                throw new MaginaException("可采购数量不能大于计划数量");
            }
            manufacturerOrderGoods.setAmount(manufacturerOrderGoods.getPrice() * manufacturerOrderGoods.getNumber());

            PurchaseOrderGoods purchaseOrderGoods1 = new PurchaseOrderGoods();
            purchaseOrderGoods1.setId(manufacturerOrderGoods.getPurchaseOrderGoodsId());
            purchaseOrderGoods1.setNumber(manufacturerOrderGoods.getNumber());
            purchaseOrderGoods1.setSumPrice(manufacturerOrderGoods.getNumber() * manufacturerOrderGoods.getPrice());
            purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods1);
            totalNum = totalNum + manufacturerOrderGoods.getNumber();
        }
        manufacterOrderGoodsServiceDomain.updateBatchById(manufacturerOrderGoodsList);
        manufacturerOrder.setAmount(manufacturerOrderGoodsList.stream().map(ManufacturerOrderGoods::getAmount).reduce(Long::sum).orElse(0L));
        manufacturerOrder.setManufacturerOrderGoodsList(manufacturerOrderGoodsList);
//        if(manufacturerOrder.getSettleMethod().getValue().equals(ManufacturerOrder.SETTLEMENT_NOW)){
//            //生成付款单
//            purchasePaymentService.buildPurchasePayment(manufacturerOrder);
//            manufacturerOrder.setSettleStatus(SettleStatusEnum.PAYING);
//        }else{
        //生成发货单
        manufacterDeliveryService.createManufacterDelivery(manufacturerOrder.getCode());
        manufacturerOrder.setStatus(ManufactureOrderStatusEnum.WAIT_DELIVERY);
//        }
        manufacterOrderServiceDomain.updateById(manufacturerOrder);
        purchaseOrderServiceDomain.lambdaUpdate().set(PurchaseOrder::getOrderStatus, PurchaseOrderStatusEnum.WAIT_PAY)
                .setSql(" number =number+" + totalNum)
                .eq(PurchaseOrder::getPurchaseCode, manufacturerOrder.getPurchaseCode()).update();
        return Boolean.TRUE;
    }


    public Boolean delete(Long id) {
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.getById(id);
        PurchasePayment purchasePayment = purchasePaymentServiceDomain.lambdaQuery()
                //.eq(PurchasePayment::getManufacterOrderCodes, manufacturerOrder.getCode())
                .apply("FIND_IN_SET({0}, manufacter_order_codes)", manufacturerOrder.getCode())
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.REJECT)
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.CLOSED)
                .one();
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                .one();
        
        if (manufacturerOrder == null) {
            throw new MaginaException("订单不存在");
        }
        if (!manufacturerOrder.getStatus().equals(ManufactureOrderStatusEnum.WAIT_DELIVERY)) {
            throw new MaginaException("当前订单状态不能删除");
        }
        if (Objects.nonNull(purchasePayment)) {
            throw new MaginaException("当前订单已生成付款单，不能删除");
        }
        manufacturerOrder.setStatus(ManufactureOrderStatusEnum.CLOSED);
        
        // 关闭相关的发货单
        manufacturerDeliveryServiceDomain.lambdaUpdate()
                .set(ManufacturerDelivery::getStatus, ManufactureOrderStatusEnum.CLOSED)
                .eq(ManufacturerDelivery::getManufacturerOrderCode, manufacturerOrder.getCode())
                .update();
        
        // 查询该采购单下的所有供应商订单，判断是否都已关闭或拒绝
        List<ManufacturerOrder> relatedOrders = manufacterOrderServiceDomain.lambdaQuery()
                .eq(ManufacturerOrder::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                .list();
        
        boolean allClosed = relatedOrders.stream()
                .allMatch(order -> order.getId().equals(manufacturerOrder.getId()) || 
                        ManufactureOrderStatusEnum.CLOSED.equals(order.getStatus()) ||
                        ManufactureOrderStatusEnum.REJECT.equals(order.getStatus()));
                
            // 如果所有供应商订单都已关闭，则关闭采购单
            if (allClosed && purchaseOrder != null) {
                purchaseOrderServiceDomain.lambdaUpdate()
                    .set(PurchaseOrder::getOrderStatus, PurchaseOrderStatusEnum.CLOSE)
                    .eq(PurchaseOrder::getId, purchaseOrder.getId())
                    .update();
                    
            }else {
                // 如果不是所有供应商订单都已关闭，则查询供应商订单中其他订单的状态，并来作为采购单的状态
                updatePurchaseOrderStatusByManufacturerOrders(manufacturerOrder.getPurchaseCode(), relatedOrders);
            }
        
        return manufacterOrderServiceDomain.updateById(manufacturerOrder);
    }

    /**
     * 根据供应商订单状态更新采购单状态
     *
     * @param purchaseCode 采购单编号
     * @param relatedOrders 相关的供应商订单列表
     */
    public void updatePurchaseOrderStatusByManufacturerOrders(String purchaseCode, List<ManufacturerOrder> relatedOrders) {
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, purchaseCode)
                .one();
        
        if (purchaseOrder == null) {
            return;
        }
        
        // 过滤出有效的供应商订单（非关闭、非拒绝状态）
        List<ManufacturerOrder> activeOrders = relatedOrders.stream()
                .filter(order -> !ManufactureOrderStatusEnum.CLOSED.equals(order.getStatus()) &&
                                !ManufactureOrderStatusEnum.REJECT.equals(order.getStatus()))
                .collect(Collectors.toList());
        
        if (activeOrders.isEmpty()) {
            // 如果没有有效订单，关闭采购单
            purchaseOrderServiceDomain.lambdaUpdate()
                    .set(PurchaseOrder::getOrderStatus, PurchaseOrderStatusEnum.CLOSE)
                    .eq(PurchaseOrder::getId, purchaseOrder.getId())
                    .update();
            return;
        }
        
        // 根据有效订单的状态确定采购单状态
        PurchaseOrderStatusEnum newStatus = determinePurchaseOrderStatus(activeOrders);
        
        // 只有当状态发生变化时才更新
        if (!newStatus.equals(purchaseOrder.getOrderStatus())) {
            purchaseOrderServiceDomain.lambdaUpdate()
                    .set(PurchaseOrder::getOrderStatus, newStatus)
                    .eq(PurchaseOrder::getId, purchaseOrder.getId())
                    .update();
        }
    }
    
    /**
     * 根据供应商订单状态列表确定采购单应该设置的状态
     *
     * @param activeOrders 有效的供应商订单列表
     * @return 采购单状态
     */
    private PurchaseOrderStatusEnum determinePurchaseOrderStatus(List<ManufacturerOrder> activeOrders) {
        // 统计各种状态的数量
        long waitAuditCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.WAIT_AUDIT.equals(order.getStatus()))
                .count();
        long waitPayCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.WAIT_PAY.equals(order.getStatus()))
                .count();
        long waitDeliveryCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.WAIT_DELIVERY.equals(order.getStatus()))
                .count();
        long partDeliveryCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.PART_DELIVERY.equals(order.getStatus()))
                .count();
        long waitReceiveCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.WAIT_RECEIVE.equals(order.getStatus()))
                .count();
        long partReceiveCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.PART_RECEIVE.equals(order.getStatus()))
                .count();
        long successCount = activeOrders.stream()
                .filter(order -> ManufactureOrderStatusEnum.SUCCESS.equals(order.getStatus()))
                .count();
        
        // 按优先级确定采购单状态
        if (waitAuditCount > 0) {
            // 如果有待审核的订单，采购单状态为待确认
            return PurchaseOrderStatusEnum.SUPPY_CONFIRM;
        } else if (waitPayCount > 0) {
            // 如果有待付款的订单，采购单状态为待付款
            return PurchaseOrderStatusEnum.WAIT_PAY;
        } else if (waitDeliveryCount > 0 || partDeliveryCount > 0) {
            // 如果有待发货或部分发货的订单，采购单状态为发货中
            return PurchaseOrderStatusEnum.WAIT_DELIVERY;
        } else if (waitReceiveCount > 0 || partReceiveCount > 0) {
            // 如果有待收货或部分收货的订单，采购单状态为待收货
            return PurchaseOrderStatusEnum.WAIT_RECEIVE;
        } else if (successCount == activeOrders.size()) {
            // 如果所有有效订单都已完成，采购单状态为已完成
            return PurchaseOrderStatusEnum.SUCCESS;
        } else {
            // 默认保持当前状态或设为待确认
            return PurchaseOrderStatusEnum.WAIT_CONFIRM;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean historyRecord(String purchaseCode) {
        List<PurchaseOrder> purchaseOrderList = purchaseOrderServiceDomain.lambdaQuery()
                .eq(StringUtils.isNotBlank(purchaseCode), PurchaseOrder::getPurchaseCode, purchaseCode)
                .list();
        for (PurchaseOrder purchaseOrder : purchaseOrderList) {
            if (purchaseOrder.getStatus().equals("0") || purchaseOrder.getPlanNum() == 0) {
                purchaseOrder.setOrderStatus(PurchaseOrderStatusEnum.CLOSE);
                purchaseOrderServiceDomain.updateById(purchaseOrder);
                continue;
            }

            //处理采购单号
             purchaseCode = purchaseOrder.getPurchaseCode();
            if (purchaseCode.startsWith("CGID")) {
                String newPurchaseCode = purchaseCode.replaceFirst("CGID", "CGSQD");
                purchaseOrder.setPurchaseCode(newPurchaseCode);
                purchaseOrderGoodsServiceDomain.lambdaUpdate().set(PurchaseOrderGoods::getPurchaseOrderCode, newPurchaseCode)
                        .eq(PurchaseOrderGoods::getPurchaseOrderCode, purchaseCode).update();
            } else {
                continue;
            }

            //存在供应商订单处理单号
            List<ManufacturerOrder> manufacturerOrders = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getPurchaseCode, purchaseOrder.getPurchaseCode())
                    .list();
            if (CollectionUtils.isNotEmpty(manufacturerOrders)) {
                for (ManufacturerOrder manufacturerOrder : manufacturerOrders) {
                    String code = manufacturerOrder.getCode();
                    manufacturerOrder.setPurchaseCode(purchaseOrder.getPurchaseCode());
                    manufacturerOrder.setCode(purchaseCode);
                    manufacterOrderServiceDomain.updateById(manufacturerOrder);

                    manufacterOrderGoodsServiceDomain.lambdaUpdate()
                            .set(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode())
                            .eq(ManufacturerOrderGoods::getManufacturerOrderCode, code);

                    manufacturerDeliveryServiceDomain.lambdaUpdate()
                            .set(ManufacturerDelivery::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                            .set(ManufacturerDelivery::getManufacturerOrderCode, manufacturerOrder.getCode())
                            .eq(ManufacturerDelivery::getManufacturerOrderCode, code).update();

                    manufacturerDeliveryRecordServiceDomain.lambdaUpdate()
                            .set(ManufacturerDeliveryRecord::getManufacturerOrderCode, manufacturerOrder.getCode())
                            .set(ManufacturerDeliveryRecord::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                            .eq(ManufacturerDeliveryRecord::getManufacturerOrderCode, code);

                    purchasePaymentServiceDomain.lambdaUpdate()
                            .set(PurchasePayment::getManufacterOrderCodes, manufacturerOrder.getCode())
                            .set(PurchasePayment::getPurchaseCodes, manufacturerOrder.getPurchaseCode())
                            .eq(PurchasePayment::getManufacterOrderCodes, code);

                    manufacterReceiveServiceDomain.lambdaUpdate()
                            .set(ManufacturerReceive::getManufacturerOrderCode, manufacturerOrder.getCode())
                            .set(ManufacturerReceive::getPurchaseCode, manufacturerOrder.getPurchaseCode())
                            .eq(ManufacturerReceive::getManufacturerOrderCode, code);
                }
                continue;
            }
            log.info("采购单号：{} 生成供应商订单",purchaseCode);
            //入库单
            List<StorageInWarehouse> storageInWarehouses = storageInWarehouseServiceDomain.lambdaQuery()
                    .eq(StorageInWarehouse::getShopWaybill, purchaseCode)
                    .eq(StorageInWarehouse::getInType, InOutTypeEnum.PURCHASE.getCode())
                    .list();
            if (CollectionUtils.isEmpty(storageInWarehouses)) {
                purchaseOrder.setOrderStatus(PurchaseOrderStatusEnum.CLOSE);
                purchaseOrderServiceDomain.updateById(purchaseOrder);
                continue;
            }
            List<String> inWarehouseIds = storageInWarehouses.stream().map(StorageInWarehouse::getInWarehouseId).collect(Collectors.toList());
            List<StorageInWarehouseGoods> storageInWarehouseList = storageInWarehouseGoodsServiceDomain.lambdaQuery()
                    .in(StorageInWarehouseGoods::getInWarehouseId,inWarehouseIds)
                    .list();
            Map<String, List<StorageInWarehouseGoods>> storageInWarehouseMap = storageInWarehouseList.stream().collect(Collectors.groupingBy(StorageInWarehouseGoods::getCode));

            List<PurchaseOrderGoods> purchaseOrderGoodsList = purchaseOrderGoodsServiceDomain.lambdaQuery()
                    .eq(PurchaseOrderGoods::getPurchaseOrderCode, purchaseOrder.getPurchaseCode())
                    .ge(PurchaseOrderGoods::getNumber, 0)
                    .list();

            //创建供应商订单
            Map<Long, List<PurchaseOrderGoods>> purchaseOrderGoodsMap = purchaseOrderGoodsList.stream()
                    .collect(Collectors.groupingBy(PurchaseOrderGoods::getManufacturerId));
            for (Map.Entry<Long, List<PurchaseOrderGoods>> entry : purchaseOrderGoodsMap.entrySet()) {
                Manufacturer manufacturer = manufacturerServiceDomain.getById(entry.getKey());

                ManufacturerOrder manufacturerOrder = new ManufacturerOrder();
                manufacturerOrder.setCode(purchaseCode);
                manufacturerOrder.setReceiveCompany("四川至简智印科技有限公司");
                manufacturerOrder.setManufacturerId(entry.getKey());
                manufacturerOrder.setSettleMethod(manufacturer.getSettleMethod());
                manufacturerOrder.setWarehouseId(purchaseOrder.getWarehouseId());
                UserPrivacy privacy = this.userPrivacyDomainService.getById(purchaseOrder.getInitiatorId());
                manufacturerOrder.setInitiatorPhone(privacy.getMobileNumber().getValue());
                manufacturerOrder.setInitiatorId(purchaseOrder.getInitiatorId());
                manufacturerOrder.setStatus(ManufactureOrderStatusEnum.SUCCESS);
                BeanUtils.copyProperties(purchaseOrder, manufacturerOrder);
                manufacturerOrder.setId(null);
                manufacturerOrder.setSettleStatus(SettleStatusEnum.SETTLED);
                Long totalAmount = 0L;
                List<ManufacturerOrderGoods> manufacturerOrderGoodsList = Lists.newArrayList();
                for (PurchaseOrderGoods purchaseOrderGoods : entry.getValue()) {
                    ManufacturerOrderGoods manufacturerOrderGoods = new ManufacturerOrderGoods();
                    BeanUtils.copyProperties(purchaseOrderGoods, manufacturerOrderGoods);
                    manufacturerOrderGoods.setManufacturerOrderCode(purchaseCode);
                    manufacturerOrderGoods.setNumber(purchaseOrderGoods.getApproveNum());
                    manufacturerOrderGoods.setApproveNum(purchaseOrderGoods.getApproveNum());
                    manufacturerOrderGoods.setPurchaseOrderGoodsId(purchaseOrderGoods.getId());
                    Long amount = purchaseOrderGoods.getApproveNum() * purchaseOrderGoods.getPrice();
                    manufacturerOrderGoods.setAmount(amount);
                    totalAmount = totalAmount + amount;
                    manufacturerOrderGoods.setId(null);
                    if (purchaseOrderGoods.getArticleType().equals(0)) {
                        if (storageInWarehouseMap.containsKey(purchaseOrderGoods.getArticleCode())) {
                            List<StorageInWarehouseGoods> storageInWarehouseGoods = storageInWarehouseMap.get(purchaseOrderGoods.getArticleCode());
                            Integer receiveNum = storageInWarehouseGoods.stream().mapToInt(StorageInWarehouseGoods::getAuditInWarehouseNumber).sum();
                            manufacturerOrderGoods.setReceiveNum(receiveNum.longValue());
                            manufacturerOrderGoods.setDeliveryNum(manufacturerOrderGoods.getReceiveNum());
                            purchaseOrderGoods.setDeliveryNum(manufacturerOrderGoods.getDeliveryNum());
                            purchaseOrderGoods.setReceiveNum(manufacturerOrderGoods.getReceiveNum());
                            purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(storageInWarehouseList)) {
                            StorageInWarehouseGoods storageInWarehouseGoods = storageInWarehouseList.get(0);
                            manufacturerOrderGoods.setReceiveNum(storageInWarehouseGoods.getAuditInWarehouseNumber().longValue());
                            manufacturerOrderGoods.setDeliveryNum(manufacturerOrderGoods.getReceiveNum());
                            purchaseOrderGoods.setDeliveryNum(manufacturerOrderGoods.getDeliveryNum());
                            purchaseOrderGoods.setReceiveNum(manufacturerOrderGoods.getReceiveNum());
                            purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);
                        }
                    }
                    manufacturerOrderGoodsList.add(manufacturerOrderGoods);
                }
                manufacturerOrder.setAmount(totalAmount);
                manufacterOrderServiceDomain.save(manufacturerOrder);
                manufacterOrderGoodsServiceDomain.saveBatch(manufacturerOrderGoodsList);
                purchaseOrderServiceDomain.updateById(purchaseOrder);
            }
        }

        return Boolean.TRUE;
    }


    public DataGrid<ManufacterOrderStatisticsVo> articlePage(OrderStatisticsPageQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterOrderServiceDomain.articlePage(query)
        ).peek(p -> {
            p.setProductTreeDtoList(partProductTreeDomainService.getByPartId(p.getPartId()));
        });
    }


    public DataGrid<ManufacterOrderStatisticsVo> companyPage(OrderStatisticsPageQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterOrderServiceDomain.companyPage(query)
        );
    }


    public DataGrid<ManufacterOrderStatisticsVo> monthlyPage(OrderStatisticsPageQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterOrderServiceDomain.monthlyPage(query)
        );
    }


    public DataGrid<PurchaseMechineVo> mechinePage(ManufacterReturnQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterOrderServiceDomain.mechinePage(query)
        );
    }

    public ManufacterDeliveryVo salesSum(OrderStatisticsPageQuery query) {
        return    manufacterOrderServiceDomain.salesSum(query);
    }
    public ManufacterReturnDto mechineSum(OrderStatisticsPageQuery query) {
        return    manufacterOrderServiceDomain.mechineSum(query);
    }

}
