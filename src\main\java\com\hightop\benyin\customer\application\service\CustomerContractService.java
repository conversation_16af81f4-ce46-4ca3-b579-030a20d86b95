package com.hightop.benyin.customer.application.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.customer.api.dto.*;
import com.hightop.benyin.customer.api.dto.query.CustomerContractQuery;
import com.hightop.benyin.customer.application.vo.CustomerContractCurrVo;
import com.hightop.benyin.customer.application.vo.CustomerContractInstallmentVo;
import com.hightop.benyin.customer.application.vo.CustomerContractVo;
import com.hightop.benyin.customer.domain.service.*;
import com.hightop.benyin.customer.infrastructure.entity.*;
import com.hightop.benyin.customer.infrastructure.enums.*;
import com.hightop.benyin.customer.infrastructure.util.CustomerMpUtils;
import com.hightop.benyin.item.domain.service.SaleSkuServiceDomain;
import com.hightop.benyin.item.infrastructure.entity.SaleSku;
import com.hightop.benyin.machine.domain.event.MachineInoutEvent;
import com.hightop.benyin.machine.domain.service.MachineDomainService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachineInoutFlow;
import com.hightop.benyin.machine.infrastructure.enums.MachineInOutType;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.machine.infrastructure.enums.SettleMethod;
import com.hightop.benyin.order.domain.service.TradeOrderInstallmentDomainService;
import com.hightop.benyin.order.infrastructure.enmu.InstallmentStatusEnum;
import com.hightop.benyin.order.infrastructure.enmu.PayModeEnum;
import com.hightop.benyin.order.infrastructure.entity.TradeOrderInstallment;
import com.hightop.benyin.payment.domain.service.TransactionDomainService;
import com.hightop.benyin.payment.domain.vo.PayOrderVo;
import com.hightop.benyin.payment.infrastructure.enums.TradeOrderOrigin;
import com.hightop.benyin.payment.infrastructure.restful.WeChatDataPackage;
import com.hightop.benyin.product.domain.service.ProductAccessoryDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductAccessory;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.repair.price.application.service.RepairMonthlyPriceService;
import com.hightop.benyin.repair.price.domain.service.RepairMonthlyPriceServiceDomain;
import com.hightop.benyin.repair.price.infrastructure.entity.RepairMonthlyPrice;
import com.hightop.benyin.share.domain.service.RegionDomainService;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.storage.domain.service.StorageInventoryServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.entity.StorageInventory;
import com.hightop.benyin.work.order.application.service.InstallOrderService;
import com.hightop.benyin.work.order.domain.service.ExchangeInfoDomainService;
import com.hightop.benyin.work.order.domain.service.InstallOrderDomainService;
import com.hightop.benyin.work.order.infrastructure.entity.InstallOrder;
import com.hightop.benyin.work.order.infrastructure.enums.InstallOrderStatus;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.Days;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class CustomerContractService {

    CustomerContractDomainService customerContractDomainService;
    InstallOrderDomainService installOrderDomainService;
    MachineDomainService machineDomainService;
    UserBasicDomainService userBasicDomainService;
    CustomerStaffDomainService customerStaffDomainService;
    RegionDomainService regionDomainService;
    TransactionDomainService transactionDomainService;
    SequenceDomainService sequenceDomainService;
    RepairMonthlyPriceService repairMonthlyPriceService;
    CustomerDeviceGroupDomainService customerDeviceGroupDomainService;
    TradeOrderInstallmentDomainService tradeOrderInstallmentDomainService;
    RepairMonthlyPriceServiceDomain repairMonthlyPriceServiceDomain;
    CustomerContractBuyDomainService customerContractBuyDomainService;
    CustomerContractServeDomainService customerContractServeDomainService;
    CustomerContractGiveDomainService customerContractGiveDomainService;
    CustomerContractMergeDomainService customerContractMergeDomainService;
    CustomerDeviceAccessoryServiceDomain customerDeviceAccessoryServiceDomain;
    ProductTreeDomainService productTreeDomainService;
    ProductAccessoryDomainService productAccessoryDomainService;
    StorageInventoryServiceDomain storageInventoryServiceDomain;
    CustomerDomainService customerDomainService;
    InstallOrderService installOrderService;
    SaleSkuServiceDomain saleSkuServiceDomain;
    CustomerTicketDomainService customerTicketDomainService;
    ApplicationEventPublisher applicationEventPublisher;
    ;

    public DataGrid<CustomerContract> page(CustomerContractQuery pageQuery) {
        pageQuery.setRegionPath(regionDomainService.cutRegion(pageQuery.getRegionPath()));
        return PageHelper.startPage(pageQuery, p ->
                this.customerContractDomainService.selectJoinList(
                        CustomerContract.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(CustomerContract.class)
                                .selectAs(CustomerContractServe::getEndTime, CustomerContract::getExpiredDate)
                                .selectAs(Customer::getName, CustomerContract::getCustomerName)
                                .selectAs(Customer::getSubbranch, CustomerContract::getSubbranch)
                                .selectAs(Customer::getSeqId, CustomerContract::getCustomerSeqId)
                                .selectAs(Region::getParentCode, CustomerContract::getParentRegionCode)
                                .selectAs(Region::getName, CustomerContract::getArea)
                                // 添加机器编号字段
                                .leftJoin(Customer.class, Customer::getId, CustomerContract::getCustomerId)
                                .leftJoin(UserBasic.class, UserBasic::getId, CustomerContract::getCreatedBy)
                                .leftJoin(Region.class, Region::getCode, Customer::getRegionCode)
                                .leftJoin(CustomerContractServe.class, CustomerContractServe::getContractCode, CustomerContract::getCode)
                                .leftJoin(CustomerContractBuy.class, CustomerContractBuy::getContractCode, CustomerContract::getCode)
                                .eq(pageQuery.getCustomerId() != null, CustomerContract::getCustomerId, pageQuery.getCustomerId())
                                .eq(StringUtils.isNotBlank(pageQuery.getContractType()), CustomerContract::getContractType, pageQuery.getContractType())
                                .eq(StringUtils.isNotBlank(pageQuery.getStatus()), CustomerContract::getStatus, pageQuery.getStatus())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getStatusList()), CustomerContract::getStatus, pageQuery.getStatusList())
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerName()), Customer::getName, pageQuery.getCustomerName())
                                .like(StringUtils.isNotBlank(pageQuery.getSignName()), CustomerContract::getSignName, pageQuery.getSignName())
                                .like(StringUtils.isNotBlank(pageQuery.getAgentName()), CustomerContract::getAgentName, pageQuery.getAgentName())
                                .like(StringUtils.isNotBlank(pageQuery.getCode()), CustomerContract::getCode, pageQuery.getCode())
                                .likeRight(StringUtils.isNotBlank(pageQuery.getRegionPath()), Customer::getRegionCode, pageQuery.getRegionPath())
                                .like(StringUtils.isNotBlank(pageQuery.getContractName()), CustomerContract::getContractName, pageQuery.getContractName())
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerSeqId()), Customer::getSeqId, pageQuery.getCustomerSeqId())
                                .like(StringUtils.isNotBlank(pageQuery.getCreatedBy()), UserBasic::getName, pageQuery.getCreatedBy())
                                .like(StringUtils.isNotBlank(pageQuery.getMachineNum()), CustomerContractBuy::getMachineNum, pageQuery.getMachineNum())
                                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), CustomerContract::getSignTime, pageQuery.getEndDate())
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), CustomerContract::getSignTime, pageQuery.getStartDate())
                                .apply(Objects.nonNull(pageQuery.getExpiredDay()), " DATEDIFF(t4.end_time, CURRENT_DATE) <= {0} and t4.end_time > CURRENT_DATE and t.status not in ('UNEFFECT','CANCEL') ", pageQuery.getExpiredDay())
                                .apply(pageQuery.getDeliveryStatus() != null, "  (t.code in (select contract_code from b_customer_contract_buy where delivery_status ={0} ) or t.code in (select contract_code from b_customer_contract_serve where delivery_status ={1} )) ", pageQuery.getDeliveryStatus(), pageQuery.getDeliveryStatus())
                                .apply(StringUtils.isNotBlank(pageQuery.getInstallStartDate()) && StringUtils.isNotBlank(pageQuery.getInstallEndDate()),
                                        " (t.code in (select contract_code from b_customer_contract_buy where install_date between {0} and {1} ) or t.code in (select contract_code from b_customer_contract_serve where install_date between {2} and {3} )) ", pageQuery.getInstallStartDate(), pageQuery.getInstallEndDate(), pageQuery.getInstallStartDate(), pageQuery.getInstallEndDate())
                                .apply(StringUtils.isNotBlank(pageQuery.getContractStartDate()) && StringUtils.isNotBlank(pageQuery.getContractEndDate()),
                                        " (t.code in  (select contract_code from b_customer_contract_serve where start_time between {0} and {1} )) ", pageQuery.getContractStartDate() + " 00:00:00", pageQuery.getContractEndDate() + " 23:59:59")
                                // 添加机器编号搜索条件
                                .and(StringUtils.isNotBlank(pageQuery.getMachineNum()), i -> i
                                        .exists("SELECT 1 FROM b_customer_contract_buy WHERE contract_code = t.code AND machine_num LIKE CONCAT('%',{0},'%')", pageQuery.getMachineNum())
                                        .or()
                                        .exists("SELECT 1 FROM b_customer_contract_serve WHERE contract_code = t.code AND machine_num LIKE CONCAT('%',{0},'%')", pageQuery.getMachineNum())
                                )
                                .groupBy(CustomerContract::getId)
                                .orderByDesc(CustomerContract::getCreatedAt)
                )
        ).peek(p -> {
            if(Objects.nonNull(p.getExpiredDate())){
                long day = ChronoUnit.DAYS.between(LocalDate.now(), p.getExpiredDate());
                p.setExpiredDay(day);
            }else{
                p.setExpiredDay(0L);
            }
            if (p.getParentRegionCode() != null) {
                Region region = regionDomainService.getRegionAndParentName(p.getParentRegionCode());
                p.setCity(region.getName());
                p.setProvince(region.getParentName());
            }
            p.setDeliveryStatus(-1);
            if (CustomerDeviceGroup.NEED_INSTALL_TREATY_TYPE.contains(p.getContractType().getValue())) {
                if (p.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)) {
                    Long count = this.customerContractBuyDomainService.lambdaQuery()
                            .eq(CustomerContractBuy::getContractCode, p.getCode())
                            .eq(CustomerContractBuy::getDeliveryStatus, false)
                            .count();
                    p.setDeliveryStatus(count > 0L ? 0 : 1);
                } else {
                    Long count = this.customerContractServeDomainService.lambdaQuery()
                            .eq(CustomerContractServe::getContractCode, p.getCode())
                            .eq(CustomerContractServe::getDeliveryStatus, false)
                            .count();
                    p.setDeliveryStatus(count > 0L ? 0 : 1);
                }
            }
            // 获取所有相关的机器编号
            List<String> machineNums = new ArrayList<>();
            List<CustomerContractBuy> buys = customerContractBuyDomainService.lambdaQuery()
                    .eq(CustomerContractBuy::getContractCode, p.getCode())
                    .isNotNull(CustomerContractBuy::getMachineNum)
                    .list();
            machineNums.addAll(buys.stream().map(CustomerContractBuy::getMachineNum).filter(Objects::nonNull).collect(Collectors.toList()));
            
            List<CustomerContractServe> serves = customerContractServeDomainService.lambdaQuery()
                    .eq(CustomerContractServe::getContractCode, p.getCode())
                    .isNotNull(CustomerContractServe::getMachineNum)
                    .list();
            machineNums.addAll(serves.stream().map(CustomerContractServe::getMachineNum).filter(Objects::nonNull).collect(Collectors.toList()));
            
            p.setMachineNums(machineNums);
            
            //获取结算状态
            p.setSettleStatus(this.getContractSettleStatus(p));
        });
    }

    public DataGrid<CustomerContract> deliveryPage(CustomerContractQuery pageQuery) {
        pageQuery.setRegionPath(regionDomainService.cutRegion(pageQuery.getRegionPath()));
        return PageHelper.startPage(pageQuery, p ->
                this.customerContractDomainService.selectJoinList(
                        CustomerContract.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(CustomerContract.class)
                                .selectAs(CustomerContract::getArrersAmount, CustomerContract::getArrersAmount)
                                .selectAs(CustomerContract::getArrersPayStatus, CustomerContract::getArrersPayStatus)
                                .selectAs(CustomerContract::getPrePayStatus, CustomerContract::getPrePayStatus)
                                .selectAs(Customer::getName, CustomerContract::getCustomerName)
                                .selectAs(Customer::getSubbranch, CustomerContract::getSubbranch)
                                .selectAs(Customer::getSeqId, CustomerContract::getCustomerSeqId)
                                .selectAs(Region::getParentCode, CustomerContract::getParentRegionCode)
                                .selectAs(Region::getName, CustomerContract::getArea)
                                .leftJoin(Customer.class, Customer::getId, CustomerContract::getCustomerId)
                                .leftJoin(UserBasic.class, UserBasic::getId, CustomerContract::getCreatedBy)
                                .leftJoin(Region.class, Region::getCode, Customer::getRegionCode)
                                .notIn(CustomerContract::getContractType,Lists.newArrayList(CustomerDeviceGroup.TREATY_TYPE_W, CustomerDeviceGroup.TREATY_TYPE_HALF))
                                .ne(CustomerContract::getStatus,ContractStatusEnums.CANCEL)
                                .eq(pageQuery.getCustomerId() != null, CustomerContract::getCustomerId, pageQuery.getCustomerId())
                                .eq(pageQuery.getIsReturn() != null, CustomerContract::getIsReturn, pageQuery.getIsReturn())
                                .eq(StringUtils.isNotBlank(pageQuery.getContractType()), CustomerContract::getContractType, pageQuery.getContractType())
                                .eq(StringUtils.isNotBlank(pageQuery.getStatus()), CustomerContract::getStatus, pageQuery.getStatus())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getStatusList()), CustomerContract::getStatus, pageQuery.getStatusList())
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerName()), Customer::getName, pageQuery.getCustomerName())
                                .like(StringUtils.isNotBlank(pageQuery.getSignName()), CustomerContract::getSignName, pageQuery.getSignName())
                                .like(StringUtils.isNotBlank(pageQuery.getAgentName()), CustomerContract::getAgentName, pageQuery.getAgentName())
                                .like(StringUtils.isNotBlank(pageQuery.getCode()), CustomerContract::getCode, pageQuery.getCode())
                                .likeRight(StringUtils.isNotBlank(pageQuery.getRegionPath()), Customer::getRegionCode, pageQuery.getRegionPath())
                                .like(StringUtils.isNotBlank(pageQuery.getContractName()), CustomerContract::getContractName, pageQuery.getContractName())
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerSeqId()), Customer::getSeqId, pageQuery.getCustomerSeqId())
                                .like(StringUtils.isNotBlank(pageQuery.getCreatedBy()), UserBasic::getName, pageQuery.getCreatedBy())
                                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), CustomerContract::getSignTime, pageQuery.getEndDate())
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), CustomerContract::getSignTime, pageQuery.getStartDate())
                                .apply(pageQuery.getDeliveryStatus() != null, "  (t.code in (select contract_code from b_customer_contract_buy where delivery_status ={0} ) or t.code in (select contract_code from b_customer_contract_serve where delivery_status ={1} )) ", pageQuery.getDeliveryStatus(), pageQuery.getDeliveryStatus())
                                .apply(StringUtils.isNotBlank(pageQuery.getInstallStartDate()) && StringUtils.isNotBlank(pageQuery.getInstallEndDate()),
                                        " (t.code in (select contract_code from b_customer_contract_buy where install_date between {0} and {1} ) or t.code in (select contract_code from b_customer_contract_serve where install_date between {2} and {3} )) ", pageQuery.getInstallStartDate(), pageQuery.getInstallEndDate(), pageQuery.getInstallStartDate(), pageQuery.getInstallEndDate())
                                .apply(StringUtils.isNotBlank(pageQuery.getContractStartDate()) && StringUtils.isNotBlank(pageQuery.getContractEndDate()),
                                        " (t.code in  (select contract_code from b_customer_contract_serve where start_time between {2} and {3} )) ", pageQuery.getContractStartDate() + " 00:00:00", pageQuery.getContractEndDate() + " 23:59:59")
                                .orderByDesc(CustomerContract::getCreatedAt)
                )
        ).peek(p -> {
            if (p.getParentRegionCode() != null) {
                Region region = regionDomainService.getRegionAndParentName(p.getParentRegionCode());
                p.setCity(region.getName());
                p.setProvince(region.getParentName());
            }
            p.setDeliveryStatus(-1);
            if (CustomerDeviceGroup.NEED_INSTALL_TREATY_TYPE.contains(p.getContractType().getValue())) {
                if (p.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)) {
                    Long count = this.customerContractBuyDomainService.lambdaQuery()
                            .eq(CustomerContractBuy::getContractCode, p.getCode())
                            .eq(CustomerContractBuy::getDeliveryStatus, false)
                            .count();
                    p.setDeliveryStatus(count > 0L ? 0 : 1);
                } else {
                    Long count = this.customerContractServeDomainService.lambdaQuery()
                            .eq(CustomerContractServe::getContractCode, p.getCode())
                            .eq(CustomerContractServe::getDeliveryStatus, false)
                            .count();
                    p.setDeliveryStatus(count > 0L ? 0 : 1);
                }
            }
            //获取结算状态
            p.setSettleStatus(this.getContractSettleStatus(p));
        });
    }

    public CustomerContractItemDto getByItemCode(String itemCode) {
        CustomerContractItemDto customerContractDto = this.customerContractServeDomainService.selectJoinOne(
                CustomerContractItemDto.class, MPJWrappers.lambdaJoin()
                        .selectAll(CustomerContractServe.class)
                        .selectAs(CustomerContractServe::getDeviceOn, CustomerContractItemDto::getDeviceOn)
                        .selectAs(Machine::getProductName, CustomerContractItemDto::getProductInfo)
                        .selectAs(Machine::getDeviceOn, CustomerContractItemDto::getDeviceOn)
                        .selectAs(Machine::getTagName, CustomerContractItemDto::getTagName)
                        .selectAs(ProductTree::getFullIdPath, CustomerContractItemDto::getFullIdPath)
                        .selectAs(CustomerContract::getContractType, CustomerContractItemDto::getContractType)
                        .selectAs(CustomerContract::getCustomerId, CustomerContractItemDto::getCustomerId)
                        .leftJoin(Machine.class, Machine::getMachineNum, CustomerContractServe::getMachineNum)
                        .leftJoin(ProductTree.class, ProductTree::getId, CustomerContractServe::getProductId)
                        .leftJoin(CustomerContract.class, CustomerContract::getCode, CustomerContractServe::getContractCode)
                        .eq(CustomerContractServe::getCode, itemCode)
        );
        List<String> t = Arrays.stream(customerContractDto.getFullIdPath().split(StringConstants.SLASH))
                // 去掉头尾
                .skip(1).limit(3).collect(Collectors.toList());
        Optional.ofNullable(this.productTreeDomainService.getById(t.get(0)))
                .ifPresent(b -> customerContractDto.setProductInfo(b.getName() + "/" + customerContractDto.getProductInfo()));
        if (customerContractDto.getDepositAmount() != null) {
            customerContractDto.setServeDepositAmount(customerContractDto.getDepositAmount());
        }
        if (customerContractDto.getSettleMethod() != null && customerContractDto.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
            List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.lambdaQuery()
                    .eq(TradeOrderInstallment::getItemCode, itemCode).list();
            customerContractDto.setServerInstallments(tradeOrderInstallments);
        }
        if (customerContractDto.getPriceType().equals(PriceTypeEnums.LADDER)) {
            List<RepairMonthlyPrice> repairMonthlyPrices = repairMonthlyPriceServiceDomain.lambdaQuery()
                    .eq(RepairMonthlyPrice::getItemCode, itemCode).list();
            customerContractDto.setRepairMonthlyPrices(repairMonthlyPrices);
        }
        return customerContractDto;
    }

    public CustomerContractDto getById(Long id) {
        CustomerContract customerContract = this.customerContractDomainService.selectJoinOne(
                CustomerContract.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(CustomerContract.class)
                        .selectAs(Customer::getName, CustomerContract::getCustomerName)
                        .selectAs(Customer::getSubbranch, CustomerContract::getSubbranch)
                        .selectAs(Customer::getSeqId, CustomerContract::getCustomerSeqId)
                        .leftJoin(Customer.class, Customer::getId, CustomerContract::getCustomerId)
                        .eq(CustomerContract::getId, id)
        );
        CustomerContractDto customerContractDto = new CustomerContractDto();
        BeanUtils.copyProperties(customerContract, customerContractDto);
        customerContractDto.setCustomerName(customerContract.getCustomerName());
        customerContractDto.setCustomerSeqId(customerContract.getCustomerSeqId());
        List<CustomerContractItemDto> customerContractItems = Lists.newArrayList();
        //购机
        if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)
                || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_W)) {
            List<CustomerContractBuy> customerContractBuys = customerContractBuyDomainService.selectJoinList(CustomerContractBuy.class, MPJWrappers
                    .lambdaJoin()
                    .selectAll(CustomerContractBuy.class)
                    .selectAs(Machine::getProductName, CustomerContractBuy::getProductInfo)
                    .selectAs(ProductTree::getName, CustomerContractBuy::getProductName)
                    .selectAs(Machine::getDeviceOn, CustomerContractBuy::getDeviceOn)
                    .selectAs(Machine::getTagName, CustomerContractBuy::getTagName)
                    .selectAs(ProductTree::getFullIdPath, CustomerContractBuy::getFullIdPath)
                    .leftJoin(Machine.class, Machine::getMachineNum, CustomerContractBuy::getMachineNum)
                    .leftJoin(ProductTree.class, ProductTree::getId, CustomerContractBuy::getProductId)
                    .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
            );
            if (CollectionUtils.isNotEmpty(customerContractBuys)) {
                List<String> itemCodes = Lists.newArrayList();
                for (CustomerContractBuy customerContractBuy : customerContractBuys) {
                    if (StringUtils.isBlank(customerContractBuy.getProductInfo())) {
                        customerContractBuy.setProductInfo(customerContractBuy.getProductName());
                    }
                    itemCodes.add(customerContractBuy.getCode());
                    CustomerContractItemDto customerContractItemDto = new CustomerContractItemDto();
                    BeanUtils.copyProperties(customerContractBuy, customerContractItemDto);
                    customerContractItemDto.setFullIdPath(customerContractBuy.getFullIdPath());
                    customerContractItemDto.setBuyCode(customerContractBuy.getCode());
                    customerContractItemDto.setWarrantyPartTypes(customerContractBuy.getWarrantyPartTypes());
                    customerContractItemDto.setArrersAmount(customerContractBuy.getArrersAmount());
                    customerContractItemDto.setForceStopBuy(customerContractBuy.getForceStop());
                    customerContractItemDto.setBuyRemark(customerContractBuy.getRemark());
                    customerContractItemDto.setHasGive(customerContractBuy.getHasGive());
                    customerContractItemDto.setPercentage(customerContractBuy.getPercentage());
                    customerContractItemDto.setDeviceOn(customerContractBuy.getDeviceOn());
                    if (customerContractBuy.getDeviceGroupId() != null) {
                        CustomerDeviceGroup customerDeviceGroup = this.customerDeviceGroupDomainService.selectJoinOne(CustomerDeviceGroup.class, MPJWrappers
                                .lambdaJoin().selectAll(CustomerDeviceGroup.class)
                                .selectAs(ProductTree::getFullIdPath, CustomerDeviceGroup::getFullIdPath)
                                .selectAs(ProductTree::getName, CustomerDeviceGroup::getProductInfo)
                                .leftJoin(ProductTree.class, ProductTree::getId, CustomerDeviceGroup::getProductId)
                                .eq(CustomerDeviceGroup::getId, customerContractBuy.getDeviceGroupId())
                        );
                        List<String> t = Arrays.stream(customerDeviceGroup.getFullIdPath().split(StringConstants.SLASH))
                                // 去掉头尾
                                .skip(1).limit(3).collect(Collectors.toList());
                        Optional.ofNullable(this.productTreeDomainService.getById(t.get(0)))
                                .ifPresent(b -> customerContractItemDto.setProductInfo(b.getName() + "/" + customerDeviceGroup.getProductInfo()));
                        customerContractItemDto.setDeviceGroup(customerDeviceGroup.getDeviceGroup());
                        customerContractItemDto.setDeviceSeqId(customerDeviceGroup.getDeviceSeqId());
                        customerContractItemDto.setDeviceGroupId(customerContractBuy.getDeviceGroupId());

                    } else if (customerContractBuy.getProductId() != null) {
                        if (customerContractBuy.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                            List<String> t = Arrays.stream(customerContractBuy.getFullIdPath().split(StringConstants.SLASH))
                                    // 去掉头尾
                                    .skip(1).limit(3).collect(Collectors.toList());
                            Optional.ofNullable(this.productTreeDomainService.getById(t.get(0)))
                                    .ifPresent(b -> customerContractItemDto.setProductInfo(b.getName() + "/" + customerContractItemDto.getProductInfo()));
                        } else {
                            ProductAccessory productAccessory = productAccessoryDomainService.getById(customerContractBuy.getProductId());
                            customerContractItemDto.setProductInfo(productAccessory.getModeType());
                        }
                    }
                    if (customerContractBuy.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
                        List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.lambdaQuery()
                                .eq(TradeOrderInstallment::getItemCode, customerContractBuy.getCode()).list();
                        customerContractItemDto.setTradeOrderInstallments(tradeOrderInstallments);
                    }

                    CustomerContractServe customerContractServe = customerContractServeDomainService.lambdaQuery()
                            .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                            .eq(CustomerContractServe::getItemCode, customerContractBuy.getCode())
                            .one();
                    if (customerContractServe != null) {
                        itemCodes.add(customerContractServe.getCode());
                        BeanUtils.copyProperties(customerContractServe, customerContractItemDto, "productInfo", "settleMethod", "settleStatus", "depositAmount");
                        customerContractItemDto.setServeCode(customerContractServe.getCode());
                        customerContractItemDto.setForceStopServer(customerContractServe.getForceStop());
                        customerContractItemDto.setServeArrersAmount(customerContractServe.getArrersAmount());
                        customerContractItemDto.setServeSettleStatus(customerContractServe.getSettleStatus());
                        customerContractItemDto.setServeSettleMethod(customerContractServe.getSettleMethod());
                        customerContractItemDto.setHasGive(customerContractServe.getHasGive());
                        customerContractItemDto.setServeRemark(customerContractServe.getRemark());
                        customerContractItemDto.setStartTime(customerContractServe.getStartTime());
                        customerContractItemDto.setEndTime(customerContractServe.getEndTime());
                        if (customerContractServe.getDepositAmount() != null) {
                            customerContractItemDto.setServeDepositAmount(customerContractItemDto.getDepositAmount());
                        }
                        if (customerContractServe.getSettleMethod() != null && customerContractServe.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
                            List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.lambdaQuery()
                                    .eq(TradeOrderInstallment::getItemCode, customerContractServe.getCode()).list();
                            customerContractItemDto.setServerInstallments(tradeOrderInstallments);
                        }
                        if (customerContractServe.getPriceType().equals(PriceTypeEnums.LADDER)) {
                            List<RepairMonthlyPrice> repairMonthlyPrices = repairMonthlyPriceServiceDomain.lambdaQuery()
                                    .eq(RepairMonthlyPrice::getItemCode, customerContractServe.getCode()).list();
                            customerContractItemDto.setRepairMonthlyPrices(repairMonthlyPrices);
                        }
                    }

                    List<CustomerContractGive> customerContractGives = customerContractGiveDomainService.selectJoinList(CustomerContractGive.class, MPJWrappers.lambdaJoin()
                            .selectAll(CustomerContractGive.class)
                            .selectAs(StorageArticle::getName, CustomerContractGive::getArticleName)
                            .leftJoin(StorageArticle.class, StorageArticle::getCode, CustomerContractGive::getArticleCode)
                            .eq(CustomerContractGive::getContractCode, customerContract.getCode())
                            .in(CustomerContractGive::getItemCode, itemCodes)
                    );
                    customerContractGives.forEach(customerContractGive -> {
                        if (customerContractGive.getGiveType().equals(GiveType.PART)) {
                            SaleSku saleSku = saleSkuServiceDomain.getById(customerContractGive.getSkuId());
                            if (saleSku != null) {
                                customerContractGive.setSaleAttrVals(saleSku.getSaleAttrVals());
                            }
                        }
                    });
                    customerContractItemDto.setCustomerContractGives(customerContractGives);
                    customerContractItems.add(customerContractItemDto);
                }
            }
        } else {
            List<CustomerContractServe> customerContractServes = customerContractServeDomainService.selectJoinList(CustomerContractServe.class, MPJWrappers
                    .lambdaJoin()
                    .selectAll(CustomerContractServe.class)
                    .selectAs(CustomerContractServe::getDeviceOn, CustomerContractServe::getDeviceOn)
                    .selectAs(Machine::getProductName, CustomerContractServe::getProductInfo)
                    .selectAs(ProductTree::getName, CustomerContractServe::getProductName)
                    .selectAs(Machine::getDeviceOn, CustomerContractServe::getDeviceOn)
                    .selectAs(Machine::getTagName, CustomerContractServe::getTagName)
                    .selectAs(ProductTree::getFullIdPath, CustomerContractServe::getFullIdPath)
                    .leftJoin(Machine.class, Machine::getMachineNum, CustomerContractServe::getMachineNum)
                    .leftJoin(ProductTree.class, ProductTree::getId, CustomerContractServe::getProductId)
                    .eq(CustomerContractServe::getContractCode, customerContract.getCode())
            );

            for (CustomerContractServe customerContractServe : customerContractServes) {
                if (StringUtils.isBlank(customerContractServe.getProductInfo())) {
                    customerContractServe.setProductInfo(customerContractServe.getProductName());
                }
                CustomerContractItemDto customerContractItemDto = new CustomerContractItemDto();
                BeanUtils.copyProperties(customerContractServe, customerContractItemDto);
                customerContractItemDto.setServeCode(customerContractServe.getCode());
                customerContractItemDto.setPackageBwNumber(customerContractServe.getPackageBwNumber());
                customerContractItemDto.setPackageColorNumber(customerContractServe.getPackageColorNumber());
                customerContractItemDto.setPackageAmount(customerContractServe.getPackageAmount());
                customerContractItemDto.setPackageNumber(customerContractServe.getPackageNumber());
                customerContractItemDto.setPackageBwPrice(customerContractServe.getPackageBwPrice());
                customerContractItemDto.setPackageColorPrice(customerContractServe.getPackageColorPrice());
                customerContractItemDto.setPackageType(customerContractServe.getPackageType());
                customerContractItemDto.setPackageUse(customerContractServe.getPackageUse());
                customerContractItemDto.setPackageExpireDate(customerContractServe.getPackageExpireDate());
                customerContractItemDto.setServeArrersAmount(customerContractServe.getArrersAmount());
                customerContractItemDto.setServeSettleStatus(customerContractServe.getSettleStatus());
                customerContractItemDto.setServeSettleMethod(customerContractServe.getSettleMethod());
                customerContractItemDto.setFullIdPath(customerContractServe.getFullIdPath());
                customerContractItemDto.setPercentage(customerContractServe.getPercentage());
                customerContractItemDto.setDeviceOn(customerContractServe.getDeviceOn());
                customerContractItemDto.setStartTime(customerContractServe.getStartTime());
                customerContractItemDto.setEndTime(customerContractServe.getEndTime());
                customerContractItemDto.setServeRemark(customerContractServe.getRemark());
                customerContractItemDto.setForceStopServer(customerContractServe.getForceStop());
                customerContractItemDto.setPrepayment(customerContractServe.getPrepayment());

                if (customerContractServe.getDepositAmount() != null) {
                    customerContractItemDto.setServeDepositAmount(customerContractItemDto.getDepositAmount());

                }
                //为租赁 去掉与服务合同重复的字段
                if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)
                        || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_FULL)) {
                    customerContractItemDto.setSettleMethod(null);
                    customerContractItemDto.setSettleStatus(null);
                }
                if (customerContractServe.getSettleMethod() != null && customerContractServe.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
                    List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.lambdaQuery()
                            .eq(TradeOrderInstallment::getItemCode, customerContractServe.getCode()).list();
                    customerContractItemDto.setServerInstallments(tradeOrderInstallments);
                }
                if (customerContractServe.getDeviceGroupId() != null) {
                    CustomerDeviceGroup customerDeviceGroup = this.customerDeviceGroupDomainService.selectJoinOne(CustomerDeviceGroup.class, MPJWrappers
                            .lambdaJoin().selectAll(CustomerDeviceGroup.class)
                            .selectAs(ProductTree::getFullIdPath, CustomerDeviceGroup::getFullIdPath)
                            .selectAs(ProductTree::getName, CustomerDeviceGroup::getProductInfo)
                            .leftJoin(ProductTree.class, ProductTree::getId, CustomerDeviceGroup::getProductId)
                            .eq(CustomerDeviceGroup::getId, customerContractServe.getDeviceGroupId())
                    );
                    List<String> t = Arrays.stream(customerDeviceGroup.getFullIdPath().split(StringConstants.SLASH))
                            // 去掉头尾
                            .skip(1).limit(3).collect(Collectors.toList());
                    Optional.ofNullable(this.productTreeDomainService.getById(t.get(0)))
                            .ifPresent(b -> customerContractItemDto.setProductInfo(b.getName() + "/" + customerDeviceGroup.getProductInfo()));
                    customerContractItemDto.setDeviceGroup(customerDeviceGroup.getDeviceGroup());
                    customerContractItemDto.setDeviceSeqId(customerDeviceGroup.getDeviceSeqId());
                    customerContractItemDto.setDeviceGroupId(customerContractServe.getDeviceGroupId());

                } else if (customerContractServe.getProductId() != null) {
                    if (customerContractServe.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                        List<String> t = Arrays.stream(customerContractServe.getFullIdPath().split(StringConstants.SLASH))
                                // 去掉头尾
                                .skip(1).limit(3).collect(Collectors.toList());
                        Optional.ofNullable(this.productTreeDomainService.getById(t.get(0)))
                                .ifPresent(b -> customerContractItemDto.setProductInfo(b.getName() + "/" + customerContractItemDto.getProductInfo()));
                    } else {
                        ProductAccessory productAccessory = productAccessoryDomainService.getById(customerContractServe.getProductId());
                        customerContractItemDto.setProductInfo(productAccessory.getModeType());
                    }
                }
                if (!customerContractServe.getSerType().equals(SerTypeEnums.OTHER)) {

                    if (customerContractServe.getPriceType().equals(PriceTypeEnums.LADDER)) {
                        List<RepairMonthlyPrice> repairMonthlyPrices = repairMonthlyPriceServiceDomain.lambdaQuery()
                                .eq(RepairMonthlyPrice::getItemCode, customerContractServe.getCode()).list();
                        customerContractItemDto.setRepairMonthlyPrices(repairMonthlyPrices);
                    }
                }
                List<CustomerContractGive> customerContractGives = customerContractGiveDomainService.selectJoinList(CustomerContractGive.class, MPJWrappers.lambdaJoin()
                        .selectAll(CustomerContractGive.class)
                        .selectAs(StorageArticle::getName, CustomerContractGive::getArticleName)
                        .selectAs(SaleSku::getSaleAttrVals, CustomerContractGive::getSaleAttrVals)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, CustomerContractGive::getArticleCode)
                        .leftJoin(SaleSku.class, SaleSku::getId, CustomerContractGive::getSkuId)
                        .eq(CustomerContractGive::getContractCode, customerContract.getCode())
                        .eq(CustomerContractGive::getItemCode, customerContractServe.getCode())
                );
                customerContractItemDto.setCustomerContractGives(customerContractGives);
                customerContractItems.add(customerContractItemDto);
            }
        }
        customerContractDto.setCustomerContractItems(customerContractItems);
        return customerContractDto;
    }


    public boolean confirm(Long id) {
        CustomerContract customerContract = customerContractDomainService.getById(id);
//        customerContract.setSignId(ApplicationSessions.id());
//        CustomerStaff staff = customerStaffDomainService.getCustomerStaff(customerContract.getCustomerId(), ApplicationSessions.id().toString());
//        customerContract.setSignName(staff != null ? staff.getName() : "");
        if (customerContract.getPrepayment() != null && customerContract.getPrepayment() > 0L) {
            customerContract.setStatus(ContractStatusEnums.WAIT_PAY);
        } else {
            customerContract.setStatus(ContractStatusEnums.EFFECTED);
            this.effected(customerContract);
        }
        return this.customerContractDomainService.updateById(customerContract);
    }

    public boolean effected(CustomerContract customerContract) {
        //更新订单状态
        customerContractBuyDomainService.lambdaUpdate()
                .set(CustomerContractBuy::getStatus, ContractStatusEnums.EFFECTED)
                .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
                .update();

        //服务合同需要判断时间确认是否生效
        List<CustomerContractServe> customerContractServeList = customerContractServeDomainService.lambdaQuery()
                .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                .in(CustomerContractServe::getSerType, SerTypeEnums.getServeType())
                .list();

        if (CollectionUtils.isNotEmpty(customerContractServeList)) {
            //判断服务合同是否生效
            customerContractServeList.forEach(customerContractServe -> {
                if (customerContractServe.getStartTime().isBefore(LocalDate.now())) {
                    customerContractServe.setStatus(ContractStatusEnums.EFFECTED);
                    customerContractServeDomainService.updateById(customerContractServe);
                }
            });
            //是否有已生效的服务合同
            List<CustomerContractServe> effectedServeList = customerContractServeList.stream()
                    .filter(customerContractServe ->
                            SerTypeEnums.getServeType().contains(customerContractServe.getSerType()) &&
                                    customerContractServe.getStatus().equals(ContractStatusEnums.EFFECTED)
                    ).collect(Collectors.toList());
            for (CustomerContractServe customerContractServe : effectedServeList) {
                customerDeviceGroupDomainService.lambdaUpdate()
                        .set(CustomerDeviceGroup::getSerType, customerContractServe.getSerType())
                        .eq(CustomerDeviceGroup::getId, customerContractServe.getDeviceGroupId())
                        .update();
            }
            //更新客户签约状态
            if (CollectionUtils.isNotEmpty(effectedServeList)) {
                //是否有生效的全保服务合同
                List<CustomerContractServe> effectedAllServeList = customerContractServeList.stream()
                        .filter(customerContractServe ->
                                SerTypeEnums.getServeAllType().contains(customerContractServe.getSerType()) &&
                                        customerContractServe.getStatus().equals(ContractStatusEnums.EFFECTED)
                        ).collect(Collectors.toList());
                customerDomainService.lambdaUpdate()
                        .set(Customer::getIsContracted, true)
                        .set(CollectionUtils.isNotEmpty(effectedAllServeList), Customer::getIsAll, true)
                        .eq(Customer::getId, customerContract.getCustomerId())
                        .update();
            }
        }

        return true;
    }

    /**
     * 退回合同
     *
     * @param id
     * @return
     */
    public boolean returnContract(Long id) {
        CustomerContract customerContract = customerContractDomainService.getById(id);
        if (!customerContract.getIsSupplement()) {
            throw new MaginaException("非补录合同不能退回！");
        }
        customerContract.setStatus(ContractStatusEnums.WAIT_CONFIRM);
        return this.customerContractDomainService.updateById(customerContract);
    }

    /**
     * 作废合同
     *
     * @param id
     * @return
     */
    public boolean deleteContract(Long id) {
        CustomerContract customerContract = customerContractDomainService.getById(id);
        customerContract.setStatus(ContractStatusEnums.CANCEL);

        List<CustomerContractBuy> customerContractBuys = customerContractBuyDomainService.lambdaQuery()
                .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
                .list();
        List<CustomerContractServe> customerContractServes = customerContractServeDomainService.lambdaQuery()
                .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                .list();
        List<String> machineNums = customerContractBuys.stream().filter(v -> v.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)).map(CustomerContractBuy::getMachineNum).collect(Collectors.toList());
        List<String> accessorys = customerContractBuys.stream().filter(v -> !v.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)).map(CustomerContractBuy::getMachineNum).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(customerContractServes)) {
            machineNums.addAll(customerContractServes.stream().filter(v -> v.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)).map(CustomerContractServe::getMachineNum).collect(Collectors.toList()));
            accessorys.addAll(customerContractServes.stream().filter(v -> !v.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)).map(CustomerContractServe::getMachineNum).collect(Collectors.toList()));
        }

        customerDeviceGroupDomainService.lambdaUpdate()
                .set(CustomerDeviceGroup::getSerType, SerTypeEnums.SCATTERED)
                .set(CustomerDeviceGroup::getMachineNum, null)
                .in(CustomerDeviceGroup::getMachineNum, machineNums).update();

        if (CollectionUtils.isNotEmpty(accessorys)) {
            customerDeviceAccessoryServiceDomain.remove(Wrappers.<CustomerDeviceAccessory>lambdaQuery()
                    .in(CustomerDeviceAccessory::getAccessoryCode, accessorys));

            machineDomainService.lambdaUpdate()
                    .set(Machine::getStatus, MachineStatus.INVENTORY)
                    .in(Machine::getMachineNum, accessorys)
                    .update();
        }

        machineDomainService.lambdaUpdate()
                .set(Machine::getStatus, MachineStatus.INVENTORY)
                .in(Machine::getMachineNum, machineNums)
                .update();

        //安装工单更新
        installOrderDomainService.lambdaUpdate()
                .set(InstallOrder::getStatus, InstallOrderStatus.CLOSE)
                .eq(InstallOrder::getContractCode, customerContract.getCode())
                .in(InstallOrder::getStatus, Lists.newArrayList(InstallOrderStatus.DISTRIBUTED, InstallOrderStatus.WAIT_DISTRIBUTE, InstallOrderStatus.WAIT_APPROVE))
                .update();

        return this.customerContractDomainService.updateById(customerContract);
    }

    /**
     * 退机
     *
     * @param contractReturnDto
     * @return
     */
    public Boolean returnBack(ContractReturnDto contractReturnDto) {
        CustomerContract customerContract = customerContractDomainService.getById(contractReturnDto.getId());
        if (customerContract.getStatus().equals(ContractStatusEnums.RETURN) ||
                customerContract.getStatus().equals(ContractStatusEnums.EXCHANGE) || customerContract.getStatus().equals(ContractStatusEnums.CANCEL)
                || customerContract.getStatus().equals(ContractStatusEnums.WAIT_CONFIRM)
                || customerContract.getStatus().equals(ContractStatusEnums.WAIT_PAY)
                || customerContract.getStatus().equals(ContractStatusEnums.WAIT_AUDIT)
                || customerContract.getStatus().equals(ContractStatusEnums.REJECT)
        ) {
            throw new MaginaException("当前合约状态不能退机！");
        }

        customerContractServeDomainService.lambdaUpdate()
                .set(CustomerContractServe::getIsRecycle, false)
                .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                .update();
        customerContractBuyDomainService.lambdaUpdate()
                .set(CustomerContractBuy::getIsRecycle, false)
                .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
                .update();
        customerContract.setStatus(ContractStatusEnums.RETURN);
        customerContract.setReturnReason(contractReturnDto.getReturnReason());
        customerContract.setReturnTime(LocalDateTime.now());
        customerContract.setIsReturn(true);
        return customerContractDomainService.updateById(customerContract);
    }

    public List<Machine> getReturnMachines(Long id) {
        CustomerContract customerContract = customerContractDomainService.getById(id);
        List<CustomerContractBuy> customerContractBuys = customerContractBuyDomainService.lambdaQuery()
                .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
                .list();
        List<String> machineNums = customerContractBuys.stream().map(CustomerContractBuy::getMachineNum).collect(Collectors.toList());
        List<CustomerContractServe> customerContractServes = customerContractServeDomainService.lambdaQuery()
                .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                .list();
        machineNums.addAll(customerContractServes.stream().map(CustomerContractServe::getMachineNum).collect(Collectors.toList()));
        return machineDomainService.lambdaQuery().in(Machine::getMachineNum, machineNums).list();
    }


    /**
     * 确认收货
     *
     * @param contractReceiveDto
     */
    public Boolean receiveMachine(ContractReceiveDto contractReceiveDto) {
        if (contractReceiveDto.getId() == null && StringUtils.isBlank(contractReceiveDto.getContractCode())) {
            throw new MaginaException("合同编号不能为空！");
        }

        CustomerContract customerContract = customerContractDomainService.lambdaQuery()
                .eq(StringUtils.isNotBlank(contractReceiveDto.getContractCode()), CustomerContract::getCode, contractReceiveDto.getContractCode())
                .eq(contractReceiveDto.getId() != null, CustomerContract::getId, contractReceiveDto.getId()).one();

        List<Machine> machines = contractReceiveDto.getMachines();
        List<String> machineNums = machines.stream().map(Machine::getMachineNum).collect(Collectors.toList());
        customerContractServeDomainService.lambdaUpdate()
                .set(CustomerContractServe::getIsRecycle, true)
                .set(CustomerContractServe::getRecycleTime, LocalDateTime.now())
                .set(CustomerContractServe::getStatus, ContractStatusEnums.UNEFFECT)
                .eq(CustomerContractServe::getContractCode, customerContract.getCode())
                .in(CustomerContractServe::getMachineNum, machineNums)
                .update();
        customerContractBuyDomainService.lambdaUpdate()
                .set(CustomerContractBuy::getIsRecycle, true)
                .set(CustomerContractBuy::getRecycleTime, LocalDateTime.now())
                .set(CustomerContractBuy::getStatus, ContractStatusEnums.UNEFFECT)
                .eq(CustomerContractBuy::getContractCode, customerContract.getCode())
                .in(CustomerContractBuy::getMachineNum, machineNums)
                .update();
        customerContract.setStatus(ContractStatusEnums.UNEFFECT);
        customerContractDomainService.updateById(customerContract);

        List<MachineInoutFlow> machineInoutEvents = Lists.newArrayList();

        machines.forEach(machine -> {
            machine.setStatus(MachineStatus.INVENTORY);

            MachineInoutFlow machineInoutEvent = MachineInoutFlow.builder()
                    .price(machine.getPurchasePrice())
                    .machineNum(machine.getMachineNum())
                    .sourceCode(customerContract.getCode())
                    .createdBy(new UserEntry().setId(ApplicationSessions.id()))
                    .time(LocalDateTime.now())
                    .inOutType(1)
                    .sourceType(customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)?MachineInOutType.BUY_RETURN:MachineInOutType.RENT_RETURN)
                    .build();
            machineInoutEvents.add(machineInoutEvent);

            customerDeviceGroupDomainService.lambdaUpdate()
                    .set(CustomerDeviceGroup::getSerType, SerTypeEnums.SCATTERED)
                    .set(CustomerDeviceGroup::getMachineNum, null)
                    .set(CustomerDeviceGroup::getStatus, false)
                    .eq(CustomerDeviceGroup::getMachineNum, machine.getMachineNum())
                    .update();
        });
         machineDomainService.updateBatchById(machines);

        ExecutorUtils.doAfterCommit(() -> {
            if (CollectionUtils.isNotEmpty(machineInoutEvents)) {
                applicationEventPublisher.publishEvent(MachineInoutEvent.builder().machineInoutFlowList(machineInoutEvents).build());
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 续约合同
     *
     * @param id
     * @return
     */
    public CustomerContractDto renewal(Long id) {
        CustomerContractDto customerContract = this.getById(id);
        customerContract.setId(null);
        customerContract.setSignTime(LocalDateTime.now());
        String contractCode = sequenceDomainService.nextDateSequence("CT", 4);
        customerContract.setCode(contractCode);
        customerContract.setStatus(ContractStatusEnums.WAIT_EFFECT);
        customerContract.setIsRenewal(true);
        customerContract.setIsSupplement(false);
        customerContract.setAttachments(null);
        customerContract.getCustomerContractItems().forEach(item -> {
            if (SerTypeEnums.getNoServeType().contains(item.getSerType())) {
                throw new MaginaException("非服务类合同不能续约！");
            }
            item.setStartTime(item.getEndTime().plusDays(1));
            item.setEndTime(null);
            if (SerTypeEnums.BUY_FULL.equals(item.getSerType())) {
                item.setSerType(SerTypeEnums.ALL);
            }
            if (SerTypeEnums.BUY_HALF.equals(item.getSerType())) {
                item.setSerType(SerTypeEnums.HALF);
            }
        });
        if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)) {
            customerContract.setContractType(new DictItemEntry().setValue(CustomerDeviceGroup.TREATY_TYPE_HALF));
        }

        return customerContract;
    }

    /**
     * 新增编辑合同
     *
     * @param customerContractDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(CustomerContractDto customerContractDto) {
        CustomerContract customerContract = null;

        if (StringUtils.isNotBlank(customerContractDto.getCode())) {
            Long count = this.customerContractDomainService.lambdaQuery()
                    .eq(CustomerContract::getCode, customerContractDto.getCode())
                    .ne(customerContractDto.getId() != null, CustomerContract::getId, customerContractDto.getId())
                    .count();
            if (count > 0) {
                throw new MaginaException("合同编码已存在！");
            }
        }
        customerContractDto.getCustomerContractItems().forEach(item -> {
            if (item.getSerType() == null) {
                throw new MaginaException("所选机型未完善信息填写，请点击编辑按钮完善合同信息！");
            }
        });

        if (customerContractDto.getId() != null) {
            customerContract = this.customerContractDomainService.getById(customerContractDto.getId());
            if (customerContract.getStatus().equals(ContractStatusEnums.EFFECTED)) {
                throw new MaginaException("合约已生效，不能修改");
            }
            //清理明细数据
            repairMonthlyPriceServiceDomain.remove(Wrappers.<RepairMonthlyPrice>lambdaQuery()
                    .eq(RepairMonthlyPrice::getContractCode, customerContract.getCode()));

            customerContractBuyDomainService.remove(Wrappers.<CustomerContractBuy>lambdaQuery()
                    .eq(CustomerContractBuy::getContractCode, customerContract.getCode()));

            customerContractGiveDomainService.remove(Wrappers.<CustomerContractGive>lambdaQuery()
                    .eq(CustomerContractGive::getContractCode, customerContract.getCode()));

            customerContractMergeDomainService.remove(Wrappers.<CustomerContractMerge>lambdaQuery()
                    .eq(CustomerContractMerge::getContractCode, customerContract.getCode()));

            customerContractServeDomainService.remove(Wrappers.<CustomerContractServe>lambdaQuery()
                    .eq(CustomerContractServe::getContractCode, customerContract.getCode()));

            tradeOrderInstallmentDomainService.remove(Wrappers.<TradeOrderInstallment>lambdaQuery()
                    .eq(TradeOrderInstallment::getContractCode, customerContract.getCode()));


        } else {
            customerContract = new CustomerContract();
            if (StringUtils.isBlank(customerContractDto.getCode())) {
                customerContract.setCode(sequenceDomainService.nextDateSequence("CT", 4));
                customerContractDto.setCode(customerContract.getCode());
            }else{
                customerContract.setCode(customerContractDto.getCode());
            }
        }
        Long customerId = customerContractDto.getCustomerId();
        customerContract.setCustomerId(customerContractDto.getCustomerId());
        customerContract.setContractName(customerContractDto.getContractName());
        customerContract.setCustomerId(customerContractDto.getCustomerId());
        customerContract.setContractType(customerContractDto.getContractType());
        customerContract.setSignTime(customerContractDto.getSignTime());
        customerContract.setMergeType(customerContractDto.getMergeType());

        customerContract.setAttachments(customerContractDto.getAttachments());
        customerContract.setRemark(customerContractDto.getRemark());
        customerContract.setStatus(customerContractDto.getStatus());
        customerContract.setContractType(customerContractDto.getContractType());
        customerContract.setConsigneePhone(customerContractDto.getConsigneePhone());
        customerContract.setConsignee(customerContractDto.getConsignee());
        customerContract.setIsSupplement(customerContractDto.getIsSupplement());
        customerContract.setIsInstall(customerContractDto.getIsInstall());
        customerContract.setAddressId(customerContractDto.getAddressId());
        customerContract.setDepositAmount(customerContractDto.getDepositAmount() == null ? 0L : customerContractDto.getDepositAmount());

        customerContract.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        if (customerContractDto.getSignTime() == null) {
            customerContract.setSignTime(LocalDateTime.now());
        }
        if (customerContractDto.getStatus() == null) {
            customerContract.setStatus(ContractStatusEnums.WAIT_CONFIRM);
            customerContract.setSettleStatus(ContractSettleStatus.WAIT_CONFIRM);
        }
        if (customerContractDto.getSignId() != null) {
            UserBasic userBasic = userBasicDomainService.getById(customerContractDto.getSignId());
            if (userBasic != null) {
                customerContract.setSignName(userBasic.getName());
            }
            customerContract.setSignId(customerContractDto.getSignId());
        }
        if (customerContractDto.getAgentId() != null) {
            UserBasic userBasic = userBasicDomainService.getById(customerContractDto.getAgentId());
            if (userBasic != null) {
                customerContract.setAgentName(userBasic.getName());
            }
            customerContract.setAgentId(customerContractDto.getAgentId());
        } else {
            customerContract.setAgentId(ApplicationSessions.id());
            UserBasic userBasic = userBasicDomainService.getById(customerContract.getAgentId());
            customerContract.setAgentName(userBasic.getName());
        }

        Long firstAmount = 0L;
        Long depositAmount = 0L;
        Long prepayment = 0L;//待付款金额
        Long totalAmount = 0L;//总金额
        Long arrersAmount = 0L;//尾款金额

        List<CustomerContractGive> customerContractGives = Lists.newArrayList();
        for (CustomerContractItemDto customerContractItemDto : customerContractDto.getCustomerContractItems()) {
            if (customerContractDto.getIsSupplement() && customerContractItemDto.getDeviceGroupId() == null && StringUtils.isBlank(customerContractItemDto.getMachineNum())) {
                throw new MaginaException("设备组或机器编号不能为空");
            }

            if (!customerContractDto.getIsSupplement() && customerContractItemDto.getProductId() == null) {
                throw new MaginaException("设备组或机器编号不能为空");
            }

            //购机和维保
            CustomerContractBuy customerContractBuy = null;
            CustomerContractServe customerContractServe = null;
            if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)
                    || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_W)) {
                customerContractBuy = this.buildCustomerContractBuy(customerContract.getCode(), customerContractItemDto);
                customerContractBuy.setRemark(customerContractItemDto.getBuyRemark());
                customerContractBuy.setHasGive(customerContractItemDto.getHasGive());
                customerContractBuy.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                customerContractBuy.setWarrantyPartTypes(customerContractItemDto.getWarrantyPartTypes());
                customerContractBuy.setContractCode(customerContract.getCode());
                if (customerContractBuy.getSettleMethod() == null) {
                    customerContractBuy.setSettleMethod(SettleMethod.FULL);
                }
                if (customerContractDto.getIsSupplement()) {
                    customerContractBuy.setDeliveryStatus(true);
                    customerContractBuy.setStatus(ContractStatusEnums.EFFECTED);
                } else {
                    customerContractBuy.setDeliveryStatus(false);
                    customerContractBuy.setStatus(ContractStatusEnums.WAIT_EFFECT);
                }
                if (customerContractBuy.getDeviceGroupId() != null) {
                    customerContractBuy.setHostType(new DictItemEntry().setValue(Machine.HOST_TYPE_MACHINE));
                }
                //维保合同与其他类型合同
                if (customerContractItemDto.getSerType().equals(SerTypeEnums.OTHER) || customerContractDto.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_W)) {
                    customerContractBuy.setRemark(customerContractItemDto.getBuyRemark());
                    customerContractBuy.setWarrantyPartTypes(customerContractItemDto.getWarrantyPartTypes());
                }

                //定金+分期
                if (customerContractBuy.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
                    if (CollectionUtils.isEmpty(customerContractItemDto.getTradeOrderInstallments())) {
                        throw new MaginaException("结算方式为分期，还款计划不能为空");
                    }
                    Long totalInstallmentAmount = customerContractItemDto.getTradeOrderInstallments().stream().mapToLong(TradeOrderInstallment::getAmount).sum();
                    if (totalInstallmentAmount.compareTo(customerContractBuy.getArrersAmount()) != 0) {
                        throw new MaginaException("计划还款总金额有误，请检查！");
                    }
                    for (TradeOrderInstallment tradeOrderInstallment : customerContractItemDto.getTradeOrderInstallments()) {
                        tradeOrderInstallment.setCode(sequenceDomainService.nextDateSequence("FQ", 4));
                        tradeOrderInstallment.setContractCode(customerContractBuy.getContractCode());
                        tradeOrderInstallment.setItemCode(customerContractBuy.getCode());
                        tradeOrderInstallment.setCustomerId(customerContractDto.getCustomerId());
                        tradeOrderInstallment.setStatus(InstallmentStatusEnum.WAIT_PAY);
                        tradeOrderInstallment.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                        tradeOrderInstallment.setId(null);
                    }
                    firstAmount = firstAmount + customerContractBuy.getDepositAmount();
                    tradeOrderInstallmentDomainService.saveBatch(customerContractItemDto.getTradeOrderInstallments());
                    prepayment = prepayment + customerContractBuy.getDepositAmount();
                    customerContractBuy.setArrersAmount(customerContractBuy.getFullAmount() - customerContractBuy.getDepositAmount());
                    //折扣金额
                    if (customerContractBuy.getDiscountAmount() != null) {
                        customerContractBuy.setArrersAmount(customerContractBuy.getArrersAmount() - customerContractBuy.getDiscountAmount());
                    }
                }
                //定金 +尾款
                if (customerContractBuy.getSettleMethod().equals(SettleMethod.ADVANCE)) {
                    customerContractBuy.setArrersAmount(customerContractBuy.getFullAmount() - customerContractBuy.getDepositAmount());
                    //折扣金额
                    if (customerContractBuy.getDiscountAmount() != null) {
                        customerContractBuy.setArrersAmount(customerContractBuy.getArrersAmount() - customerContractBuy.getDiscountAmount());
                    }
                    //arrersAmount = arrersAmount + customerContractItemDto.getArrersAmount();
                    arrersAmount = arrersAmount + customerContractBuy.getArrersAmount();
                    prepayment = prepayment + customerContractBuy.getDepositAmount();

                }
                //全款
                if (customerContractBuy.getSettleMethod().equals(SettleMethod.FULL)) {
                    prepayment = prepayment + customerContractBuy.getFullAmount();
                    //折扣金额
                    if (customerContractBuy.getDiscountAmount() != null) {
                        prepayment = prepayment - customerContractBuy.getDiscountAmount();
                    }
                    customerContractBuy.setArrersAmount(0L);
                    customerContractBuy.setDepositAmount(0L);
                }

                totalAmount = totalAmount + customerContractBuy.getFullAmount();
                if (customerContractDto.getIsSupplement() && customerContractItemDto.getAddToDeviceGroup()
                        && customerContractBuy.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                    this.addToDeviceGroup(customerContract, customerContractItemDto);
                    customerContractBuy.setDeviceGroupId(customerContractItemDto.getDeviceGroupId());
                }
                customerContractBuy.setDeliveryStatus(true);
                if (!customerContract.getIsSupplement()) {
                    customerContractBuy.setDeliveryStatus(false);
                }
                //服务合同清除对象金额
                customerContractItemDto.setDepositAmount(0L);
                customerContractItemDto.setFullAmount(0L);
                customerContractItemDto.setArrersAmount(0L);
                customerContractBuyDomainService.save(customerContractBuy);
                //补录维保合同 生效服务类型变更
                if (customerContract.getIsSupplement() && customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_W)) {
                    if (customerContractBuy.getDeviceGroupId() != null) {
                        customerDeviceGroupDomainService.lambdaUpdate()
                                .set(CustomerDeviceGroup::getSerType, customerContractBuy.getSerType())
                                .eq(CustomerDeviceGroup::getId, customerContractBuy.getDeviceGroupId())
                                .update();
                    }
                }
            }

            //服务合同
            if (!SerTypeEnums.getNoServeType().contains(customerContractItemDto.getSerType()) || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)) {
                if (customerContractDto.getIsSupplement() && customerContractItemDto.getAddToDeviceGroup() &&
                        customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)
                        && customerContractItemDto.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                    this.addToDeviceGroup(customerContract, customerContractItemDto);
                }
                customerContractServe = this.buildCustomerContractServe(customerContract.getCode(), customerContractDto.getIsSupplement(), customerContractItemDto);
                customerContractServe.setDeviceGroupId(customerContractItemDto.getDeviceGroupId());
                customerContractServe.setHostType(customerContractItemDto.getHostType());
                customerContractServe.setSerType(customerContractItemDto.getSerType());
                customerContractServe.setSettleStatus(customerContractItemDto.getServeSettleStatus());
                customerContractServe.setSettleMethod(customerContractItemDto.getServeSettleMethod());
                customerContractServe.setArrersAmount(customerContractItemDto.getServeArrersAmount());
                customerContractServe.setHasGive(customerContractItemDto.getHasGive());
                customerContractServe.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                customerContractServe.setContractCode(customerContract.getCode());
                if (customerContractServe.getSettleMethod() == null) {
                    customerContractServe.setSettleMethod(SettleMethod.FULL);
                }

                if (customerContractItemDto.getServeDepositAmount() != null && customerContractItemDto.getServeDepositAmount() > 0L) {
                    customerContractServe.setDepositAmount(customerContractItemDto.getServeDepositAmount());
                }

                // 确保 packageAmount 不为 null，避免空指针异常
                if (customerContractServe.getPackageAmount() == null) {
                    customerContractServe.setPackageAmount(0L);
                }

                if (customerContractItemDto.getSerType().equals(SerTypeEnums.OTHER)) {
                    customerContractServe.setRemark(customerContractItemDto.getServeRemark());
                    customerContractServe.setSettleMethod(SettleMethod.FULL);
                    prepayment = prepayment + customerContractServe.getPackageAmount();
                    totalAmount = totalAmount + customerContractServe.getPackageAmount();
                    customerContractServe.setPrepayment(0L);
                    customerContractServe.setDepositAmount(0L);
                    customerContractServe.setPackagePreAmount(0L);
                } else {
                    if (customerContractServe.getPriceType() != null && customerContractServe.getPriceType().equals(PriceTypeEnums.LADDER)) {
                        if (CollectionUtils.isEmpty(customerContractItemDto.getRepairMonthlyPrices())) {
                            throw new MaginaException("价格类型为阶梯，价格明细不能为空");
                        }
                        for (RepairMonthlyPrice repairMonthlyPrice : customerContractItemDto.getRepairMonthlyPrices()) {
                            repairMonthlyPrice.setContractCode(customerContractServe.getContractCode());
                            repairMonthlyPrice.setItemCode(customerContractServe.getCode());
                            repairMonthlyPrice.setCustomerId(customerId);
                            repairMonthlyPrice.setDeviceGroupId(customerContractServe.getDeviceGroupId());
                            repairMonthlyPrice.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                        }
                        repairMonthlyPriceService.saveBatch(customerContractServe.getDeviceGroupId(), customerContractServe.getContractCode(), customerContractServe.getCode(), customerContractItemDto.getRepairMonthlyPrices());
                    } else {
                        if (customerContractItemDto.getBlackWhitePrice() == null
                                && customerContractItemDto.getColorPrice() == null &&
                                customerContractItemDto.getFiveColourPrice() == null) {
                            throw new MaginaException("固定单价，价格不能都为空！");
                        }
                        if (customerContractItemDto.getBlackWhitePrice().compareTo(BigDecimal.ZERO) == 0
                                && customerContractItemDto.getColorPrice().compareTo(BigDecimal.ZERO) == 0 &&
                                customerContractItemDto.getFiveColourPrice().compareTo(BigDecimal.ZERO) == 0) {
                            if (!customerContractItemDto.getSerType().equals(SerTypeEnums.PACKAGE_ALL)
                                    && !customerContractItemDto.getSerType().equals(SerTypeEnums.PACKAGE_HALF)) {
                                throw new MaginaException("固定单价，价格不能都为0！");
                            }
                        }
                    }

                    //租赁或融资租赁有押金默认全款
                    if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_FULL)
                            || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)) {
                        //租赁服务 添加 预付款
                        if (SerTypeEnums.getRentServeType().contains(customerContractServe.getSerType())) {
                            customerContractServe.setSettleMethod(SettleMethod.FULL);
                            totalAmount = totalAmount + customerContractServe.getDepositAmount();
                            prepayment = prepayment + customerContractServe.getDepositAmount();
                            totalAmount = totalAmount + customerContractServe.getPrepayment();
                            prepayment = prepayment + customerContractServe.getPrepayment();
                        }
                    }
                    //服务里面只有 租赁与包量涉及费用
                    if (customerContractServe.getSettleMethod() != null && customerContractServe.getSettleMethod().equals(SettleMethod.INSTALLMENT)) {
                        if (CollectionUtils.isEmpty(customerContractItemDto.getServerInstallments())) {
                            throw new MaginaException("结算方式为分期，还款计划不能为空");
                        }
                        Long totalInstallmentAmount = customerContractItemDto.getServerInstallments().stream().mapToLong(TradeOrderInstallment::getAmount).sum();
                        if (totalInstallmentAmount.compareTo(customerContractServe.getArrersAmount()) != 0) {
                            throw new MaginaException("计划还款总金额有误，请检查！");
                        }
                        for (TradeOrderInstallment tradeOrderInstallment : customerContractItemDto.getServerInstallments()) {
                            tradeOrderInstallment.setCode(sequenceDomainService.nextDateSequence("FQ", 4));
                            tradeOrderInstallment.setContractCode(customerContractServe.getContractCode());
                            tradeOrderInstallment.setItemCode(customerContractServe.getCode());
                            tradeOrderInstallment.setCustomerId(customerContractDto.getCustomerId());
                            tradeOrderInstallment.setStatus(InstallmentStatusEnum.WAIT_PAY);
                            tradeOrderInstallment.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                            tradeOrderInstallment.setId(null);
                        }
                        tradeOrderInstallmentDomainService.saveBatch(customerContractItemDto.getServerInstallments());
                        //包量与其他类型合同字段不一样
                        if (SerTypeEnums.getPackageServeType().contains(customerContractServe.getSerType())) {
                            prepayment = prepayment + customerContractServe.getPackagePreAmount();
                            customerContractServe.setArrersAmount(customerContractServe.getPackageAmount() - customerContractServe.getPackagePreAmount());
                        } else {
                            prepayment = prepayment + customerContractServe.getDepositAmount();
                            customerContractServe.setArrersAmount(customerContractServe.getPackageAmount() - customerContractServe.getDepositAmount());
                        }
                    }

                    //定金 +尾款
                    if (customerContractServe.getSettleMethod().equals(SettleMethod.ADVANCE)) {
                        if (SerTypeEnums.getPackageServeType().contains(customerContractServe.getSerType())) {
                            customerContractServe.setArrersAmount(customerContractServe.getPackageAmount() - customerContractServe.getPackagePreAmount());
                            arrersAmount = arrersAmount + customerContractServe.getArrersAmount();
                            prepayment = prepayment + customerContractServe.getPackagePreAmount();
                        } else {
                            customerContractServe.setArrersAmount(customerContractServe.getPackageAmount() - customerContractServe.getDepositAmount());
                            arrersAmount = arrersAmount + customerContractServe.getArrersAmount();
                            prepayment = prepayment + customerContractServe.getDepositAmount();
                        }
                    }
                    //全款
                    if (customerContractServe.getSettleMethod().equals(SettleMethod.FULL)) {
                        prepayment = prepayment + customerContractServe.getPackageAmount();
                        customerContractServe.setPackagePreAmount(0L);
                        //租赁这两个值要保留
                        if (!SerTypeEnums.getRentServeType().contains(customerContractServe.getSerType())) {
                            customerContractServe.setPrepayment(0L);
                            customerContractServe.setDepositAmount(0L);
                        }
                    }
                    totalAmount = totalAmount + customerContractServe.getPackageAmount();
                }


                if (customerContractDto.getIsSupplement() && customerContractItemDto.getAddToDeviceGroup()
                        && (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)
                        || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_FULL))) {
                    Machine machine = machineDomainService.getMachineByCode(customerContractServe.getMachineNum());

                    CustomerDeviceGroup dataCustomerDeviceGroup = this.customerDeviceGroupDomainService.selectJoinOne(CustomerDeviceGroup.class, MPJWrappers
                            .lambdaJoin().selectAll(CustomerDeviceGroup.class)
                            .selectAs(Customer::getName, CustomerDeviceGroup::getCustomerName)
                            .leftJoin(Customer.class, Customer::getId, CustomerDeviceGroup::getCustomerId)
                            .eq(CustomerDeviceGroup::getMachineNum, customerContractServe.getMachineNum())
                    );

                    if (dataCustomerDeviceGroup == null) {
                        //添加到客户设备组
                        CustomerDeviceGroup customerDeviceGroup = this.buildCustomerDeviceGroup(machine, customerContract.getCustomerId(), customerContract.getContractType(), customerContractItemDto);
                        customerDeviceGroupDomainService.save(customerDeviceGroup);
                        customerContractServe.setDeviceGroupId(customerDeviceGroup.getId());
                        customerContractItemDto.setDeviceGroupId(customerDeviceGroup.getId());
                    } else {
                        if (!dataCustomerDeviceGroup.getCustomerId().equals(customerContract.getCustomerId())) {
                            throw new MaginaException("设备[" + machine.getProductName() + "]已存在客户[" + dataCustomerDeviceGroup.getCustomerName() + "]合同中，请检查！");
                        }
                        dataCustomerDeviceGroup.setSerType(customerContractServe.getSerType());
                        customerDeviceGroupDomainService.updateById(dataCustomerDeviceGroup);
                        customerContractServe.setDeviceGroupId(dataCustomerDeviceGroup.getId());
                        customerContractItemDto.setDeviceGroupId(dataCustomerDeviceGroup.getId());
                    }
                }
                if(customerContractServe.getDeviceGroupId()!=null){
                    if(customerContractServe.getHostType()==null){
                        customerContractServe.setHostType(new DictItemEntry().setValue(Machine.HOST_TYPE_MACHINE));
                    }
                }
                customerContractServe.setStatus(ContractStatusEnums.WAIT_EFFECT);
                if (customerContractDto.getIsSupplement()) {
                    customerContractServe.setStatus(ContractStatusEnums.EFFECTED);
                    if (customerContractServe.getDeviceGroupId() != null) {
                        //更新设备组合约服务类型
                        this.customerDeviceGroupDomainService.lambdaUpdate()
                                .set(CustomerDeviceGroup::getTreatyType, customerContract.getContractType())
                                .set(CustomerDeviceGroup::getSerType, customerContractServe.getSerType())
                                .eq(CustomerDeviceGroup::getId, customerContractServe.getDeviceGroupId())
                                .update();
                    }
                    customerContractServe.setDeliveryStatus(true);
                }

                if ((customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE)
                        || customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_FULL))
                        && !customerContract.getIsSupplement()) {
                    customerContractServe.setDeliveryStatus(false);
                }
                if (SerTypeEnums.getDeliveryServeType().contains(customerContractItemDto.getSerType())
                        && !customerContract.getIsSupplement()) {
                    customerContractServe.setDeliveryStatus(false);
                }
                customerContractServe.setItemCode(customerContractBuy != null ? customerContractBuy.getCode() : null);
                if (!customerContractServe.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                    customerContractServe.setDeviceGroupId(null);
                }

                customerContractServeDomainService.save(customerContractServe);
            }

            //赠品数据
            if (customerContractItemDto.getHasGive() != null && customerContractItemDto.getHasGive()) {
                if (CollectionUtils.isEmpty(customerContractItemDto.getCustomerContractGives())) {
                    throw new MaginaException("有赠送请选择赠品信息！");
                }
                long errorCount = customerContractItemDto.getCustomerContractGives().stream().filter(give -> give.getGiveType() == null).count();
                if (errorCount > 0L) {
                    throw new MaginaException("请保证添加的赠品信息填写完整！");

                }
                List<CustomerContractGive> notPartCustomerContractGives = customerContractItemDto.getCustomerContractGives().stream().filter(give -> !give.getGiveType().equals(GiveType.PART)).collect(Collectors.toList());
                Map<GiveType, List<CustomerContractGive>> customerContractGiveMap = notPartCustomerContractGives.stream()
                        .collect(Collectors.groupingBy(CustomerContractGive::getGiveType));
                for (Map.Entry<GiveType, List<CustomerContractGive>> entry : customerContractGiveMap.entrySet()) {
                    if (entry.getValue().size() > 1) {
                        throw new MaginaException("赠品类型[" + entry.getKey().getName() + "]不能重复！");
                    }
                }
                List<CustomerContractGive> partCustomerContractGives = customerContractItemDto.getCustomerContractGives().stream().filter(give -> give.getGiveType().equals(GiveType.PART)).collect(Collectors.toList());
                Map<String, List<CustomerContractGive>> articleCodeMap = partCustomerContractGives.stream()
                        .collect(Collectors.groupingBy(CustomerContractGive::getArticleCode));
                for (Map.Entry<String, List<CustomerContractGive>> entry : articleCodeMap.entrySet()) {
                    if (entry.getValue().size() > 1) {
                        throw new MaginaException("赠品[" + entry.getKey() + "]不能重复！");
                    }
                }

                for (CustomerContractGive customerContractGive : customerContractItemDto.getCustomerContractGives()) {
                    customerContractGive.setId(null);
                    customerContractGive.setContractCode(customerContract.getCode());
                    customerContractGive.setStatus("1");
                    customerContractGive.setItemCode(customerContractBuy != null ? customerContractBuy.getCode() : customerContractServe.getCode());
                    customerContractGive.setCustomerId(customerContract.getCustomerId());
                    customerContractGive.setMachineNum(customerContractItemDto.getMachineNum());
                    customerContractGive.setDeviceGroupId(customerContractItemDto.getDeviceGroupId());
                    customerContractGive.setItemType(customerContractBuy != null ? GiveSourceTypeEnums.BUY : GiveSourceTypeEnums.SERVE);
                    customerContractGive.setCode(sequenceDomainService.nextDateSequence("GV", 4));
                    customerContractGive.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                    customerContractGives.add(customerContractGive);
                }
            }
        }
        customerContract.setFirstAmount(firstAmount);
        customerContract.setTotalAmount(totalAmount);
        customerContract.setDepositAmount(depositAmount);
        customerContract.setPrepayment(prepayment);
        customerContract.setArrersAmount(arrersAmount);
        if (customerContract.getTotalAmount() == null || customerContract.getTotalAmount() == 0L) {
            customerContract.setTotalAmount(customerContract.getFirstAmount() + customerContract.getDepositAmount() + customerContract.getArrersAmount());
        }
        if (customerContractDto.getIsSupplement()) {
            customerContract.setStatus(ContractStatusEnums.EFFECTED);
            if (customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_HALF)) {
                customerContractDto.getCustomerContractItems().forEach(item -> {
                    item.setHostType(new DictItemEntry().setValue(Machine.HOST_TYPE_MACHINE));
                });
            }
            if (!customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_W)) {
                List<String> machineNums = customerContractDto.getCustomerContractItems().stream().filter(item -> item.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE) && StringUtils.isNotBlank(item.getMachineNum()))
                        .map(item -> item.getMachineNum()).collect(Collectors.toList());

                //更新机器状态
                if (CollectionUtils.isNotEmpty(machineNums)) {
                    machineDomainService.lambdaUpdate()
                            .set(!customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE), Machine::getStatus, MachineStatus.OVER_SALE)
                            .set(customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_LEASE), Machine::getStatus, MachineStatus.RENT)
                            .in(Machine::getMachineNum, machineNums).update();
                }
            }
            customerContract.setPrePayStatus(true);
            customerContract.setArrersPayStatus(true);
        } else {
            customerContract.setPrePayStatus(true);
            customerContract.setArrersPayStatus(true);
            if (customerContract.getPrepayment() > 0) {
                customerContract.setPrePayStatus(false);
            }
            long count = customerContractDto.getCustomerContractItems().stream().filter(item -> item.getSettleMethod() != null && item.getSettleMethod().equals(SettleMethod.ADVANCE)).count();
            if (count > 0) {
                customerContract.setArrersPayStatus(false);
            }
        }
        if (CollectionUtils.isNotEmpty(customerContractGives)) {
            List<CustomerContractGive> partGives = customerContractGives.stream().filter(give -> give.getGiveType().equals(GiveType.PART)).collect(Collectors.toList());
            List<String> strings = partGives.stream().map(CustomerContractGive::getArticleCode).collect(Collectors.toList());
            Long giveAmount = 0L;
            if (CollectionUtils.isNotEmpty(strings)) {
                List<StorageInventory> storageInventorys = storageInventoryServiceDomain.lambdaQuery().in(StorageInventory::getCode, strings).list();
                if (CollectionUtils.isNotEmpty(storageInventorys)) {
                    Map<String, StorageInventory> storageInventoryMap = storageInventorys.stream().collect(Collectors.toMap(StorageInventory::getCode, Function.identity()));
                    for (CustomerContractGive customerContractGive : partGives) {
                        if (storageInventoryMap.containsKey(customerContractGive.getArticleCode())) {
                            StorageInventory storageInventory = storageInventoryMap.get(customerContractGive.getArticleCode());
                            if (storageInventory.getAveragePrice() != null) {
                                giveAmount = giveAmount + (storageInventory.getAveragePrice() * customerContractGive.getQuantity());
                            }
                        }
                    }
                }
            }
            customerContract.setGiveAmount(giveAmount);
        }
        if (CollectionUtils.isNotEmpty(customerContractGives)) {
            customerContractGiveDomainService.saveBatch(customerContractGives);
        }
        customerContractDomainService.saveOrUpdate(customerContract);
        return Boolean.TRUE;
    }

    public ContractSettleStatus getContractSettleStatus(CustomerContract customerContract) {
        if (customerContract.getSettleStatus() != null) {
            if(customerContract.getSettleStatus().equals(ContractSettleStatus.COMPLETED)
            || customerContract.getSettleStatus().equals(ContractSettleStatus.WAIT_AUDIT)
                    || customerContract.getSettleStatus().equals(ContractSettleStatus.REJECT)){
                return customerContract.getSettleStatus();
            }
        }


        if (customerContract.getStatus().equals(ContractStatusEnums.WAIT_CONFIRM)) {
            return ContractSettleStatus.WAIT_CONFIRM;
        }
        if (customerContract.getStatus().equals(ContractStatusEnums.CANCEL)
                || customerContract.getStatus().equals(ContractStatusEnums.UNEFFECT)) {
            return null;
        }
        if (customerContract.getStatus().equals(ContractStatusEnums.WAIT_CONFIRM)
                || customerContract.getStatus().equals(ContractStatusEnums.WAIT_PAY)
                || customerContract.getStatus().equals(ContractStatusEnums.WAIT_AUDIT)
                || customerContract.getStatus().equals(ContractStatusEnums.REJECT)) {
            return ContractSettleStatus.WAIT_CONFIRM;
        }
        Long installmentCount = tradeOrderInstallmentDomainService.lambdaQuery()
                .eq(TradeOrderInstallment::getContractCode, customerContract.getCode())
                .in(TradeOrderInstallment::getStatus, Lists.newArrayList(InstallmentStatusEnum.WAIT_PAY, InstallmentStatusEnum.OVERDUE,
                        InstallmentStatusEnum.REJECT, InstallmentStatusEnum.PAY_FAILED))
                .count();
        //补录合同则看是否有分期
        if (customerContract.getIsSupplement()) {
            if (installmentCount > 0) {
                return ContractSettleStatus.INSTALLMENT;
            } else {
                return ContractSettleStatus.COMPLETED;
            }
        } else {
            if (!customerContract.getPrePayStatus()) {
                return ContractSettleStatus.PRE_WAIT_PAY;
            }
            if (!customerContract.getArrersPayStatus()) {
                return ContractSettleStatus.ARRERS_WAIT_PAY;
            }
            if (installmentCount > 0) {
                return ContractSettleStatus.INSTALLMENT;
            } else {
                return ContractSettleStatus.COMPLETED;
            }
        }
    }

    public CustomerContractBuy buildCustomerContractBuy(String contractCode, CustomerContractItemDto customerContractItemDto) {

        CustomerContractBuy customerContractBuy = new CustomerContractBuy();
        BeanUtils.copyProperties(customerContractItemDto, customerContractBuy);
        customerContractBuy.setId(null);
        customerContractBuy.setProductId(customerContractItemDto.getProductId());
        customerContractBuy.setContractCode(contractCode);
        customerContractBuy.setForceStop(customerContractItemDto.getForceStopBuy());
        customerContractBuy.setCode(sequenceDomainService.nextSequence(contractCode, 3));
        customerContractBuy.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        return customerContractBuy;
    }

    private void addToDeviceGroup(CustomerContract customerContract, CustomerContractItemDto customerContractItemDto) {
        Machine machine = machineDomainService.getMachineByCode(customerContractItemDto.getMachineNum());
        CustomerDeviceGroup dataCustomerDeviceGroup = null;
        if (customerContractItemDto.getDeviceGroupId() != null) {
            dataCustomerDeviceGroup = customerDeviceGroupDomainService.getById(customerContractItemDto.getDeviceGroupId());
        } else {
            dataCustomerDeviceGroup = customerDeviceGroupDomainService.lambdaQuery()
                    .eq(CustomerDeviceGroup::getMachineNum, customerContractItemDto.getMachineNum()).one();
        }
        if (dataCustomerDeviceGroup == null) {
            //添加到客户设备组
            CustomerDeviceGroup customerDeviceGroup = this.buildCustomerDeviceGroup(machine, customerContract.getCustomerId(), customerContract.getContractType(), customerContractItemDto);
            customerDeviceGroupDomainService.save(customerDeviceGroup);
            customerContractItemDto.setDeviceGroupId(customerDeviceGroup.getId());
            dataCustomerDeviceGroup = customerDeviceGroup;
        } else {
            if (!dataCustomerDeviceGroup.getCustomerId().equals(customerContract.getCustomerId())) {
                throw new MaginaException("设备[" + machine.getProductName() + "]已存在其他合同，并且非当前客户，请检查！");
            }
            dataCustomerDeviceGroup.setMachineNum(customerContractItemDto.getMachineNum());
            dataCustomerDeviceGroup.setSerType(customerContractItemDto.getSerType());
            if (SerTypeEnums.NO_WARRANTY.equals(customerContractItemDto.getSerType())) {
                dataCustomerDeviceGroup.setSerType(SerTypeEnums.SCATTERED);
            }
            customerDeviceGroupDomainService.updateById(dataCustomerDeviceGroup);
            customerContractItemDto.setDeviceGroupId(dataCustomerDeviceGroup.getId());
        }

        customerDeviceAccessoryServiceDomain.remove(Wrappers.<CustomerDeviceAccessory>lambdaQuery()
                .eq(CustomerDeviceAccessory::getDeviceGroupId, dataCustomerDeviceGroup.getId()));

        List<Machine> machines = machineDomainService.lambdaQuery()
                .eq(Machine::getBindMachine, machine.getMachineNum()).list();
        if (CollectionUtils.isNotEmpty(machines)) {
            Long customerId = customerContract.getCustomerId();
            Long productId = machine.getProductId();
            List<CustomerDeviceAccessory> customerDeviceAccessories = machines.stream()
                    .map(m -> {
                        CustomerDeviceAccessory customerDeviceAccessory = new CustomerDeviceAccessory();
                        customerDeviceAccessory.setAccessoryId(m.getProductId());
                        customerDeviceAccessory.setAccessoryCode(m.getMachineNum());
                        customerDeviceAccessory.setDeviceGroupId(customerContractItemDto.getDeviceGroupId());
                        customerDeviceAccessory.setCustomerId(customerId);
                        customerDeviceAccessory.setProductId(productId);
                        return customerDeviceAccessory;
                    }).collect(Collectors.toList());
            customerDeviceAccessoryServiceDomain.saveBatch(customerDeviceAccessories);
        }
    }

    public CustomerContractServe buildCustomerContractServe(String contractCode, Boolean iSupplement, CustomerContractItemDto customerContractItemDto) {
        if (iSupplement && customerContractItemDto.getDeviceGroupId() == null && StringUtils.isBlank(customerContractItemDto.getMachineNum())) {
            throw new MaginaException("设备组或机器编号不能为空");
        }
        CustomerContractServe customerContractServe = new CustomerContractServe();
        BeanUtils.copyProperties(customerContractItemDto, customerContractServe);
        customerContractServe.setId(null);
        customerContractServe.setProductId(customerContractItemDto.getProductId());
        customerContractServe.setContractCode(contractCode);
        customerContractServe.setForceStop(customerContractItemDto.getForceStopServer());
        customerContractServe.setCode(sequenceDomainService.nextSequence(contractCode, 3));
        customerContractServe.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        customerContractServe.setRemark(customerContractItemDto.getServeRemark());
        return customerContractServe;
    }

    private CustomerDeviceGroup buildCustomerDeviceGroup(Machine machine, Long customerId, DictItemEntry contractType, CustomerContractItemDto customerContractItemDto) {
        CustomerDeviceGroup customerDeviceGroup = new CustomerDeviceGroup();
        String deviceGroup = customerDeviceGroupDomainService.selectJoinOne(
                String.class, MPJWrappers.lambdaJoin()
                        .selectMax(CustomerDeviceGroup::getDeviceGroup)
                        .eq(CustomerDeviceGroup::getCustomerId, customerId)
        );
        if (StringUtils.isBlank(deviceGroup)) {
            customerDeviceGroup.setDeviceGroup(new DictItemEntry().setValue("701"));
        } else {
            Integer deviceGroupInt = Integer.parseInt(deviceGroup);
            deviceGroupInt += 1;
            customerDeviceGroup.setDeviceGroup(new DictItemEntry().setValue(deviceGroupInt.toString()));
        }
        customerDeviceGroup.setDeviceSeqId(sequenceDomainService.nextDateSequence(CustomerDeviceGroup.SEQ_PREFIX, CustomerDeviceGroup.SEQ_LEN));
        customerDeviceGroup.setCustomerId(customerId);
        customerDeviceGroup.setProductId(machine.getProductId());
        customerDeviceGroup.setMachineNum(machine.getMachineNum());
        customerDeviceGroup.setDeviceStatus(new DictItemEntry().setValue("901"));
        customerDeviceGroup.setFixStatus(new DictItemEntry().setValue("1501"));
        customerDeviceGroup.setSerType(customerContractItemDto.getSerType());
        if (SerTypeEnums.NO_WARRANTY.equals(customerContractItemDto.getSerType())) {
            customerDeviceGroup.setSerType(SerTypeEnums.SCATTERED);
        }

        customerDeviceGroup.setTreatyType(contractType);
        if (contractType.getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)) {
            if (customerContractItemDto.getSerType().equals(SerTypeEnums.BUY_HALF)) {
                customerDeviceGroup.setTreatyType(new DictItemEntry().setValue(CustomerDeviceGroup.TREATY_TYPE_HALF));
            } else if (customerContractItemDto.getSerType().equals(SerTypeEnums.BUY_FULL)) {
                customerDeviceGroup.setTreatyType(new DictItemEntry().setValue(CustomerDeviceGroup.TREATY_TYPE_FULL));
            } else {
                customerDeviceGroup.setTreatyType(new DictItemEntry().setValue(CustomerDeviceGroup.TREATY_TYPE_SCATTERED));
            }
        } else {
            customerDeviceGroup.setTreatyType(contractType);
        }
        customerDeviceGroup.setStatus(true);
        if (machine != null) {
            customerDeviceGroup.setDeviceOn(machine.getDeviceOn());
            customerDeviceGroup.setDeviceSequence(machine.getDeviceSequence());
            customerDeviceGroup.setPlaceOrigin(machine.getPlaceOrigin());
            customerDeviceGroup.setSupplyVoltage(machine.getElectric());
            if (CollectionUtils.isNotEmpty(machine.getPicsUrl())) {
                customerDeviceGroup.setDeviceGroupImg(machine.getPicsUrl().get(0));
            }
        }
        return customerDeviceGroup;
    }

    public boolean uploadContractAttachment(ContractAttachmentDto contractAttachmentDto) {
        CustomerContract customerContract = this.customerContractDomainService.getById(contractAttachmentDto.getId());
        customerContract.setAttachments(contractAttachmentDto.getAttachments());
        return customerContractDomainService.updateById(customerContract);
    }

    /**
     * 购机发货
     *
     * @param contractDeliveryDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deliveryMachine(ContractDeliveryDto contractDeliveryDto) {
        List<ContractDeliveryItemDto> contractDeliveryItemDtos = contractDeliveryDto.getContractDeliveryItemDtos();
        contractDeliveryItemDtos.forEach(item -> {
            CustomerContractBuy customerContractBuy = this.customerContractBuyDomainService.getById(item.getId());
            if (customerContractBuy == null) {
                CustomerContractServe customerContractServe = this.customerContractServeDomainService.getById(item.getId());
                if (customerContractServe == null) {
                    throw new MaginaException("合同项不存在");
                }
                item.setItemType(GiveSourceTypeEnums.SERVE);
                item.setHostType(customerContractServe.getHostType());
                Machine machine = machineDomainService.getMachineByCode(item.getMachineNum());
                item.setBindMachine(machine.getBindMachine());
                item.setProductName(machine.getProductName());
            } else {
                item.setItemType(GiveSourceTypeEnums.BUY);
                item.setHostType(customerContractBuy.getHostType());
                Machine machine = machineDomainService.getMachineByCode(item.getMachineNum());
                item.setBindMachine(machine.getBindMachine());
                item.setProductName(machine.getProductName());
            }
        });

        List<ContractDeliveryItemDto> contractDeliveryItemMachines = contractDeliveryItemDtos.stream()
                .filter(item -> item.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)).collect(Collectors.toList());
        //表示多台机器
        if (contractDeliveryItemMachines.size() > 1) {
            List<ContractDeliveryItemDto> notBindMachines = contractDeliveryItemDtos.stream()
                    .filter(item -> !item.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE) && StringUtils.isBlank(item.getBindMachine())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notBindMachines)) {
                throw new MaginaException("本次发货多台机器，" + String.join(",", notBindMachines.stream().map(ContractDeliveryItemDto::getProductName).collect(Collectors.toList())) + "未绑定机器！");
            }
        } else {
            if (CollectionUtils.isNotEmpty(contractDeliveryItemMachines)) {
                contractDeliveryItemDtos.forEach(item -> {
                    if (!item.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                        machineDomainService.lambdaUpdate()
                                .set(Machine::getBindMachine, contractDeliveryItemMachines.get(0).getMachineNum())
                                .eq(Machine::getMachineNum, item.getMachineNum())
                                .update();
                    }
                });
            }
        }

        List<CustomerContractBuy> customerContractMachineBuys = Lists.newArrayList();
        List<CustomerContractBuy> customerContractAccessoryBuys = Lists.newArrayList();
        List<CustomerContractServe> customerContractServeMachiness = Lists.newArrayList();
        List<CustomerContractServe> customerContractServeAccessoryBuys = Lists.newArrayList();
        List<MachineInoutFlow> machineInoutEvents = Lists.newArrayList();

        CustomerContract customerContract = this.customerContractDomainService.getById(contractDeliveryDto.getId());
        contractDeliveryItemDtos.forEach(item -> {
            Machine machine = machineDomainService.getMachineByCode(item.getMachineNum());
            CustomerContractBuy customerContractBuy = this.customerContractBuyDomainService.getById(item.getId());
            if (customerContractBuy != null) {
                item.setHostType(customerContractBuy.getHostType());
                item.setItemType(GiveSourceTypeEnums.BUY);
                if (customerContractBuy.getDeliveryStatus()) {
                    String productName = null;
                    if (customerContractBuy.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                        productName = productTreeDomainService.getById(customerContractBuy.getProductId()).getName();
                    } else {
                        productName = productAccessoryDomainService.getById(customerContractBuy.getProductId()).getModeType();
                    }
                    throw new MaginaException("机器[" + productName + "]已发货，请勿重复操作！");
                }
                customerContractBuy.setMachineNum(item.getMachineNum());
                customerContractBuy.setDeliveryStatus(true);
                customerContractBuy.setDeliveryTime(LocalDateTime.now());
                if (customerContractBuy.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                    customerContractMachineBuys.add(customerContractBuy);
                } else {
                    customerContractBuy.setMachineNum(machine.getMachineNum());
                    customerContractBuy.setBindMachine(machine.getBindMachine());
                    customerContractBuy.setProductInfo(machine.getProductName());
                    customerContractAccessoryBuys.add(customerContractBuy);
                }

                customerContractGiveDomainService.lambdaUpdate()
                        .set(CustomerContractGive::getMachineNum, item.getMachineNum())
                        .eq(CustomerContractGive::getItemCode, customerContractBuy.getCode());
            }

            CustomerContractServe customerContractServe = this.customerContractServeDomainService.getById(item.getId());
            if(customerContractServe!=null){
                customerContractServe.setMachineNum(item.getMachineNum());
                customerContractServe.setDeliveryStatus(true);
                customerContractServe.setDeliveryTime(LocalDateTime.now());
                if (customerContractServe.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE)) {
                    customerContractServeMachiness.add(customerContractServe);
                } else {
                    customerContractServe.setMachineNum(machine.getMachineNum());
                    customerContractServe.setBindMachine(machine.getBindMachine());
                    customerContractServe.setProductInfo(machine.getProductName());
                    customerContractServeAccessoryBuys.add(customerContractServe);
                }
                if (StringUtils.isNotBlank(customerContractServe.getItemCode())) {
                    customerContractBuyDomainService.lambdaUpdate()
                            .set(CustomerContractBuy::getDeliveryStatus, true)
                            .set(CustomerContractBuy::getDeliveryTime, LocalDateTime.now())
                            .set(CustomerContractBuy::getMachineNum, item.getMachineNum())
                            .eq(CustomerContractBuy::getCode, customerContractServe.getItemCode())
                            .update();
                }
                customerContractGiveDomainService.lambdaUpdate()
                        .set(CustomerContractGive::getMachineNum, item.getMachineNum())
                        .eq(CustomerContractGive::getItemCode, customerContractServe.getCode());
            }

            machineDomainService.lambdaUpdate()
                    .set(Machine::getStatus, MachineStatus.OVER_SALE)
                    .eq(Machine::getMachineNum, item.getMachineNum())
                    .update();
            MachineInoutFlow machineInoutEvent = MachineInoutFlow.builder()
                    .price(machine.getPurchasePrice())
                    .machineNum(machine.getMachineNum())
                    .sourceCode(customerContract.getCode())
                    .createdBy(new UserEntry().setId(ApplicationSessions.id()))
                    .time(LocalDateTime.now())
                    .inOutType(2)
                    .sourceType(customerContract.getContractType().getValue().equals(CustomerDeviceGroup.TREATY_TYPE_BUY)?MachineInOutType.SHOPPING_MALL:MachineInOutType.RENT_MALL)
                    .build();
            machineInoutEvents.add(machineInoutEvent);
        });

        if (CollectionUtils.isNotEmpty(customerContractMachineBuys)) {
            customerContractBuyDomainService.updateBatchById(customerContractMachineBuys);
        }
        if (CollectionUtils.isNotEmpty(customerContractAccessoryBuys)) {
            customerContractBuyDomainService.updateBatchById(customerContractAccessoryBuys);
        }
        if (CollectionUtils.isNotEmpty(customerContractServeMachiness)) {
            customerContractServeDomainService.updateBatchById(customerContractServeMachiness);
        }
        if (CollectionUtils.isNotEmpty(customerContractServeAccessoryBuys)) {
            customerContractServeDomainService.updateBatchById(customerContractServeAccessoryBuys);
        }

        //等于购机 要生成安装工单
        if (CustomerDeviceGroup.NEED_INSTALL_TREATY_TYPE
                .contains(customerContract.getContractType().getValue())) {
            if (customerContract.getIsInstall()) {
                installOrderService.createInstallOrder(customerContract, contractDeliveryDto.getContractDeliveryItemDtos());
            } else {
                // 不需要安装，直接为每个机器创建设备组
                List<ContractDeliveryItemDto> machineItems = contractDeliveryItemDtos.stream()
                        .filter(item -> item.getHostType().getValue().equals(Machine.HOST_TYPE_MACHINE))
                        .collect(Collectors.toList());

                for (ContractDeliveryItemDto item : machineItems) {
                    // 获取机器信息
                    Machine machine = machineDomainService.getMachineByCode(item.getMachineNum());

                    // 获取合约项目信息
                    String contractItemCode = null;
                    CustomerContractItemDto contractItemDto = new CustomerContractItemDto();

                    if (item.getItemType().equals(GiveSourceTypeEnums.BUY)) {
                        CustomerContractBuy customerContractBuy = customerContractBuyDomainService.getById(item.getId());
                        contractItemCode = customerContractBuy.getCode();
                        BeanUtils.copyProperties(customerContractBuy, contractItemDto);
                    } else if (item.getItemType().equals(GiveSourceTypeEnums.SERVE)) {
                        CustomerContractServe customerContractServe = customerContractServeDomainService.getById(item.getId());
                        contractItemCode = customerContractServe.getCode();
                        BeanUtils.copyProperties(customerContractServe, contractItemDto);
                    }

                    // 查找或创建设备组
                    CustomerDeviceGroup customerDeviceGroup = customerDeviceGroupDomainService.lambdaQuery()
                            .eq(CustomerDeviceGroup::getCustomerId, customerContract.getCustomerId())
                            .eq(CustomerDeviceGroup::getMachineNum, item.getMachineNum())
                            .one();

                    if (customerDeviceGroup == null) {
                        customerDeviceGroup = this.buildCustomerDeviceGroup(machine, customerContract.getCustomerId(),
                                customerContract.getContractType(), contractItemDto);
                        customerDeviceGroupDomainService.save(customerDeviceGroup);
                    }

                    if (StringUtils.isNotBlank(customerDeviceGroup.getMachineNum())) {
                        // 处理选配件
                        List<Machine> machines = machineDomainService.lambdaQuery()
                                .eq(Machine::getBindMachine, customerDeviceGroup.getMachineNum()).list();
                        if (CollectionUtils.isNotEmpty(machines)) {
                            CustomerDeviceGroup finalCustomerDeviceGroup = customerDeviceGroup;
                            List<CustomerDeviceAccessory> customerDeviceAccessories = machines.stream()
                                    .map(m -> {
                                        CustomerDeviceAccessory customerDeviceAccessory = new CustomerDeviceAccessory();
                                        customerDeviceAccessory.setAccessoryId(m.getProductId());
                                        customerDeviceAccessory.setAccessoryCode(m.getMachineNum());
                                        customerDeviceAccessory.setDeviceGroupId(finalCustomerDeviceGroup.getId());
                                        customerDeviceAccessory.setCustomerId(finalCustomerDeviceGroup.getCustomerId());
                                        customerDeviceAccessory.setProductId(finalCustomerDeviceGroup.getProductId());
                                        return customerDeviceAccessory;
                                    }).collect(Collectors.toList());
                            customerDeviceAccessoryServiceDomain.saveBatch(customerDeviceAccessories);
                        }
                    }

                    // 更新合同设备组id
                    customerContractBuyDomainService.lambdaUpdate()
                            .set(CustomerContractBuy::getDeviceGroupId, customerDeviceGroup.getId())
                            //.set(CustomerContractBuy::getStatus, ContractStatusEnums.EFFECTED)
                            .eq(CustomerContractBuy::getCode, contractItemCode)
                            .update();

                    customerContractServeDomainService.lambdaUpdate()
                            .set(CustomerContractServe::getDeviceGroupId, customerDeviceGroup.getId())
                            //.set(CustomerContractServe::getStatus, ContractStatusEnums.EFFECTED)
                            .eq(CustomerContractServe::getCode, contractItemCode)
                            .update();

                    customerContractGiveDomainService.lambdaUpdate()
                            .set(CustomerContractGive::getDeviceGroupId, customerDeviceGroup.getId())
                            .eq(CustomerContractGive::getItemCode, contractItemCode)
                            .update();

                    // 更新价格设备组id
                    this.repairMonthlyPriceServiceDomain.lambdaUpdate()
                            .set(RepairMonthlyPrice::getDeviceGroupId, customerDeviceGroup.getId())
                            .eq(RepairMonthlyPrice::getItemCode, contractItemCode)
                            .update();
                }

                /*// 更新合同状态
                customerContractDomainService.lambdaUpdate()
                        .set(CustomerContract::getStatus, ContractStatusEnums.EFFECTED)
                        .eq(CustomerContract::getCode, customerContract.getCode())
                        .update();*/
            }
        }

        ExecutorUtils.doAfterCommit(() -> {
            if (CollectionUtils.isNotEmpty(machineInoutEvents)) {
                applicationEventPublisher.publishEvent(MachineInoutEvent.builder().machineInoutFlowList(machineInoutEvents).build());
            }
        });
        return Boolean.TRUE;
    }

    public boolean removeById(Long id) {
        return this.customerContractDomainService.removeById(id);
    }

    /**
     * 支付合同预付款
     *
     * @param contractPayDto
     * @return
     */
    public WeChatDataPackage payOrder(ContractPayDto contractPayDto) {
        CustomerContract customerContract = this.customerContractDomainService.getById(contractPayDto.getId());
        customerContract.setPayMode(PayModeEnum.WECHART);
        Long ticketAmount = 0L;
        Long payAmount = customerContract.getPrepayment();
        if (contractPayDto.getTicketId() != null) {
            CustomerTicket customerTicket = customerTicketDomainService.getById(contractPayDto.getTicketId());
            if (customerTicket != null) {
                if (!customerTicket.getStatus().equals(TicketStatusEnums.ACQUIRED)) {
                    throw new MaginaException("该优惠券状态异常，请联系客服人员！");
                }
                if (!customerTicket.getTicketType().equals(TicketTypeEnums.MACHINE)) {
                    throw new MaginaException("当前选择的优惠券非购机券，无法使用！");
                }
            }
            ticketAmount = customerTicket.getDenomination().multiply(DownloadResponseUtil.HUNDRED).longValue();
            payAmount = customerContract.getPrepayment() - ticketAmount;
            if (customerContract.getPrepayment() <= 0L) {
                payAmount = 1L;
            }
        }
        customerContractDomainService.updateById(customerContract);
        if (!Objects.equals(ContractStatusEnums.WAIT_PAY, customerContract.getStatus())) {
            throw new MaginaException("当前合约状态不可支付");
        }
        // 完成支付
        PayOrderVo payOrderVo = this.transactionDomainService.order(ApplicationSessions.openId(), customerContract.getCode(), TradeOrderOrigin.CONTRACT_PRE,
                CustomerMpUtils.requiredCustomerId(),
                payAmount, customerContract.getCode(), contractPayDto.getTicketId(), ticketAmount);
        return payOrderVo.getWeChatDataPackage();
    }

    /**
     * 支付合同尾款
     *
     * @param contractPayDto
     * @return
     */
    public WeChatDataPackage payOrderArr(ContractPayDto contractPayDto) {
        CustomerContract customerContract = this.customerContractDomainService.getById(contractPayDto.getId());
        customerContract.setPayMode(PayModeEnum.WECHART);
        Long ticketAmount = 0L;
        Long payAmount = customerContract.getArrersAmount();
        customerContractDomainService.updateById(customerContract);
        if (customerContract.getArrersPayStatus()) {
            throw new MaginaException("当前合约状态不可支付");
        }
        // 完成支付
        PayOrderVo payOrderVo = this.transactionDomainService.order(ApplicationSessions.openId(), customerContract.getCode(), TradeOrderOrigin.CONTRACT_ARR,
                CustomerMpUtils.requiredCustomerId(),
                payAmount, customerContract.getCode(), contractPayDto.getTicketId(), ticketAmount);
        return payOrderVo.getWeChatDataPackage();
    }

    /**
     * 获取分期明细列表
     *
     * @return
     */
    public CustomerContractInstallmentVo getInstallmentDetailList(String itemCode) {
        List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.selectJoinList(
                TradeOrderInstallment.class, MPJWrappers.lambdaJoin()
                        .selectAll(TradeOrderInstallment.class)
                        .selectAs(CustomerContract::getContractName, TradeOrderInstallment::getContractName)
                        .selectAs(Customer::getName, TradeOrderInstallment::getCustomerName)
                        .selectAs(Customer::getSeqId, TradeOrderInstallment::getCustomerSeqId)
                        .leftJoin(CustomerContract.class, CustomerContract::getCode, TradeOrderInstallment::getContractCode)
                        .leftJoin(Customer.class, Customer::getId, CustomerContract::getCustomerId)
                        .eq(TradeOrderInstallment::getItemCode, itemCode)
                        .ne(TradeOrderInstallment::getStatus, InstallmentStatusEnum.CANCEL)
                        .orderByAsc(TradeOrderInstallment::getInstallmentIndex)
        );
        CustomerContractInstallmentVo customerContractInstallmentVo = new CustomerContractInstallmentVo();

        CustomerContractServe customerContractServe = customerContractServeDomainService.lambdaQuery().eq(CustomerContractServe::getCode, itemCode).one();
        if (customerContractServe == null) {
            CustomerContractBuy customerContractBuy = customerContractBuyDomainService.lambdaQuery().eq(CustomerContractBuy::getCode, itemCode).one();
            if (customerContractBuy != null) {
                customerContractInstallmentVo.setSerType(customerContractBuy.getSerType());
                if(StringUtils.isNotBlank(customerContractBuy.getMachineNum())){
                    Machine machine = machineDomainService.getMachineByCode(customerContractBuy.getMachineNum());
                    customerContractInstallmentVo.setProoductName(machine.getProductName());
                }else{
                    ProductTree product = productTreeDomainService.getById(customerContractBuy.getProductId());
                    customerContractInstallmentVo.setProoductName(product.getName());
                }
            }
        } else {
            customerContractInstallmentVo.setSerType(customerContractServe.getSerType());
            if(StringUtils.isNotBlank(customerContractServe.getMachineNum())) {
                Machine machine = machineDomainService.getMachineByCode(customerContractServe.getMachineNum());
                customerContractInstallmentVo.setProoductName(machine.getProductName());
            }else{
                ProductTree product = productTreeDomainService.getById(customerContractServe.getProductId());
                customerContractInstallmentVo.setProoductName(product.getName());
            }
        }
        customerContractInstallmentVo.setContractName(tradeOrderInstallments.get(0).getContractName());
        customerContractInstallmentVo.setContractItemCode(tradeOrderInstallments.get(0).getItemCode());
        Long amount = tradeOrderInstallments.stream().mapToLong(TradeOrderInstallment::getAmount).sum();
        Long alreadyPaid = tradeOrderInstallments.stream().filter(item -> item.getStatus().equals(InstallmentStatusEnum.PAID)).count();
        customerContractInstallmentVo.setInstallmentNum(tradeOrderInstallments.size());
        customerContractInstallmentVo.setAlreadyPaid(alreadyPaid);
        customerContractInstallmentVo.setAmount(amount);
        customerContractInstallmentVo.setStatus(0);
        if (alreadyPaid == tradeOrderInstallments.size()) {
            customerContractInstallmentVo.setStatus(1);
        }

        customerContractInstallmentVo.setTradeOrderInstallments(tradeOrderInstallments);
        return customerContractInstallmentVo;
    }


    /**
     * 获取本期分期列表
     *
     * @return
     */
    public List<CustomerContractCurrVo> getCurrentInstallmentList() {
        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //查询查询分期数据
        List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.selectJoinList(
                TradeOrderInstallment.class, MPJWrappers.lambdaJoin()
                        .selectAll(TradeOrderInstallment.class)
                        .selectAs(CustomerContract::getContractName, TradeOrderInstallment::getContractName)
                        .selectAs(Machine::getProductName, TradeOrderInstallment::getProoductName)
                        .selectAs(CustomerContractBuy::getSerType, TradeOrderInstallment::getSerType)
                        .selectAs(Customer::getName, TradeOrderInstallment::getCustomerName)
                        .selectAs(Customer::getSeqId, TradeOrderInstallment::getCustomerSeqId)
                        .leftJoin(CustomerContract.class, CustomerContract::getCode, TradeOrderInstallment::getContractCode)
                        .leftJoin(CustomerContractBuy.class, CustomerContractBuy::getCode, TradeOrderInstallment::getItemCode)
                        .leftJoin(Machine.class, Machine::getMachineNum, CustomerContractBuy::getMachineNum)
                        .leftJoin(Customer.class, Customer::getId, CustomerContract::getCustomerId)
                        .eq(TradeOrderInstallment::getCustomerId, CustomerMpUtils.requiredCustomerId())
                        .ge(TradeOrderInstallment::getPlanPayDate, firstDayOfMonth)
                        .le(TradeOrderInstallment::getPlanPayDate, lastDayOfMonth)
                        .ne(TradeOrderInstallment::getStatus, InstallmentStatusEnum.CANCEL)
                        .notIn(CustomerContract::getStatus, Lists.newArrayList(ContractStatusEnums.WAIT_CONFIRM, ContractStatusEnums.WAIT_AUDIT, ContractStatusEnums.WAIT_PAY))
                        .orderByAsc(TradeOrderInstallment::getInstallmentIndex)
        );
        List<CustomerContractCurrVo> customerContractCurrVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tradeOrderInstallments)) {
            for (TradeOrderInstallment tradeOrderInstallment : tradeOrderInstallments) {
                CustomerContractCurrVo customerContractCurrVo = new CustomerContractCurrVo();
                customerContractCurrVo.setSerType(tradeOrderInstallment.getSerType());
                customerContractCurrVo.setContractName(tradeOrderInstallment.getContractName());
                customerContractCurrVo.setContractCode(tradeOrderInstallment.getContractCode());
                customerContractCurrVo.setProoductName(tradeOrderInstallment.getProoductName());
                if (tradeOrderInstallment.getSerType() == null) {
                    CustomerContractServe customerContractServe = customerContractServeDomainService.lambdaQuery().eq(CustomerContractServe::getCode, tradeOrderInstallment.getItemCode()).one();
                    if (customerContractServe != null) {
                        customerContractCurrVo.setSerType(tradeOrderInstallment.getSerType());
                        Machine machine = machineDomainService.getMachineByCode(customerContractServe.getMachineNum());
                        customerContractCurrVo.setProoductName(machine.getProductName());
                    }
                }
                customerContractCurrVo.setCode(tradeOrderInstallment.getCode());
                customerContractCurrVo.setId(tradeOrderInstallment.getId());
                customerContractCurrVo.setCustomerName(tradeOrderInstallment.getCustomerName());
                customerContractCurrVo.setCustomerSeqId(tradeOrderInstallment.getCustomerSeqId());
                customerContractCurrVo.setInstallmentIndex(tradeOrderInstallment.getInstallmentIndex());
                customerContractCurrVo.setInstallmentNum(tradeOrderInstallment.getInstallmentNum());
                customerContractCurrVo.setPlanPayDate(tradeOrderInstallment.getPlanPayDate());
                customerContractCurrVo.setAmount(tradeOrderInstallment.getAmount());
                customerContractCurrVo.setContractItemCode(tradeOrderInstallment.getItemCode());
                customerContractCurrVo.setCurrentStatus(tradeOrderInstallment.getStatus());
                if (InstallmentStatusEnum.WAIT_PAY.equals(tradeOrderInstallment.getStatus())) {
                    if (LocalDate.now().isAfter(tradeOrderInstallment.getPlanPayDate())) {
                        customerContractCurrVo.setCurrentStatus(InstallmentStatusEnum.OVERDUE);
                    }
                }
                customerContractCurrVos.add(customerContractCurrVo);
            }
        }
        return customerContractCurrVos;
    }

    /**
     * 获取本期分期列表
     *
     * @return
     */
    public List<CustomerContractVo> getInstallmentList() {
        //查询查询分期数据
        List<TradeOrderInstallment> tradeOrderInstallments = tradeOrderInstallmentDomainService.selectJoinList(
                TradeOrderInstallment.class, MPJWrappers.lambdaJoin()
                        .selectAll(TradeOrderInstallment.class)
                        .selectAs(CustomerContract::getContractName, TradeOrderInstallment::getContractName)
                        .leftJoin(CustomerContract.class, CustomerContract::getCode, TradeOrderInstallment::getContractCode)
                        .eq(TradeOrderInstallment::getCustomerId, CustomerMpUtils.requiredCustomerId())
                        .ne(TradeOrderInstallment::getStatus, InstallmentStatusEnum.CANCEL)
                        .notIn(CustomerContract::getStatus, Lists.newArrayList(ContractStatusEnums.WAIT_CONFIRM, ContractStatusEnums.WAIT_AUDIT, ContractStatusEnums.WAIT_PAY))
                        .orderByAsc(TradeOrderInstallment::getInstallmentIndex)
        );
        List<CustomerContractVo> customerContractCurrVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tradeOrderInstallments)) {
            Map<String, List<TradeOrderInstallment>> map = tradeOrderInstallments.stream().collect(Collectors.groupingBy(TradeOrderInstallment::getItemCode));

            for (Map.Entry<String, List<TradeOrderInstallment>> entry : map.entrySet()) {
                CustomerContractVo customerContractVo = new CustomerContractVo();

                CustomerContractServe customerContractServe = customerContractServeDomainService.lambdaQuery().eq(CustomerContractServe::getCode, entry.getKey()).one();
                if (customerContractServe == null) {
                    CustomerContractBuy customerContractBuy = customerContractBuyDomainService.lambdaQuery().eq(CustomerContractBuy::getCode, entry.getKey()).one();
                    if (customerContractBuy != null) {
                        customerContractVo.setSerType(customerContractBuy.getSerType());
                        if(StringUtils.isNotBlank(customerContractBuy.getMachineNum())){
                            Machine machine = machineDomainService.getMachineByCode(customerContractBuy.getMachineNum());
                            customerContractVo.setProoductName(machine.getProductName());
                        }else{
                            ProductTree productTree = productTreeDomainService.getById(customerContractBuy.getProductId());
                            customerContractVo.setProoductName(productTree.getName());
                        }

                    }
                } else {
                    customerContractVo.setSerType(customerContractServe.getSerType());
                    if(StringUtils.isNotBlank(customerContractServe.getMachineNum())) {
                        Machine machine = machineDomainService.getMachineByCode(customerContractServe.getMachineNum());
                        customerContractVo.setProoductName(machine.getProductName());
                    }else{
                        ProductTree productTree = productTreeDomainService.getById(customerContractServe.getProductId());
                        customerContractVo.setProoductName(productTree.getName());
                     }
                }

                List<TradeOrderInstallment> tradeOrderInstallmentList = entry.getValue();
                Long amount = tradeOrderInstallmentList.stream().mapToLong(TradeOrderInstallment::getAmount).sum();
                Long alreadyPaid = tradeOrderInstallmentList.stream().filter(item -> item.getStatus().equals(InstallmentStatusEnum.PAID)).count();
                Long paidAmount = tradeOrderInstallmentList.stream().filter(item -> item.getStatus().equals(InstallmentStatusEnum.PAID)).mapToLong(TradeOrderInstallment::getAmount).sum();
                customerContractVo.setStatus(0);
                if (alreadyPaid == tradeOrderInstallments.size()) {
                    customerContractVo.setStatus(1);
                }
                customerContractVo.setAmount(amount);
                customerContractVo.setAlreadyPaid(alreadyPaid);
                customerContractVo.setPaidAmount(paidAmount);
                customerContractVo.setUnPaidAmount(amount - alreadyPaid);
                customerContractVo.setContractName(tradeOrderInstallmentList.get(0).getContractName());
                customerContractVo.setContractItemCode(tradeOrderInstallmentList.get(0).getItemCode());
                customerContractVo.setContractCode(tradeOrderInstallmentList.get(0).getContractCode());
                customerContractVo.setInstallmentNum(tradeOrderInstallmentList.size());
                customerContractCurrVos.add(customerContractVo);
            }
        }
        return customerContractCurrVos;
    }

    /**
     * 支付分期
     *
     * @param id
     * @return
     */
    public WeChatDataPackage payInstallment(Long id) {
        TradeOrderInstallment tradeOrderInstallment = this.tradeOrderInstallmentDomainService.getById(id);
        tradeOrderInstallment.setPayMode(PayModeEnum.WECHART);
        tradeOrderInstallmentDomainService.updateById(tradeOrderInstallment);
        if (!tradeOrderInstallment.getStatus().equals(InstallmentStatusEnum.WAIT_PAY)
                && !tradeOrderInstallment.getStatus().equals(InstallmentStatusEnum.REJECT)
                && !tradeOrderInstallment.getStatus().equals(InstallmentStatusEnum.PAY_FAILED)
        ) {
            throw new MaginaException("本期还款状态不可支付");
        }
        // 完成支付
        PayOrderVo payOrderVo = this.transactionDomainService.order(ApplicationSessions.openId(), tradeOrderInstallment.getCode(), TradeOrderOrigin.CONTRACT_PRE,
                CustomerMpUtils.requiredCustomerId(),
                tradeOrderInstallment.getAmount(), tradeOrderInstallment.getCode(), null, 0L);
        return payOrderVo.getWeChatDataPackage();
    }


}
