package com.hightop.benyin.purchase.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.items.store.application.vo.ApplyReturnVo;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyReturn;
import com.hightop.benyin.product.domain.service.PartProductTreeDomainService;
import com.hightop.benyin.purchase.api.dto.ManufacterReturnDto;
import com.hightop.benyin.purchase.api.dto.ManufacterReturnGoodsVo;
import com.hightop.benyin.purchase.api.dto.PurchaseMechineVo;
import com.hightop.benyin.purchase.api.dto.query.ManufacterOrderQuery;
import com.hightop.benyin.purchase.api.dto.query.ManufacterReturnQuery;
import com.hightop.benyin.purchase.api.param.ManufacteRefundParam;
import com.hightop.benyin.purchase.api.param.ManufacteRturnParam;
import com.hightop.benyin.purchase.domain.service.*;
import com.hightop.benyin.purchase.infrastructure.entity.*;
import com.hightop.benyin.purchase.infrastructure.enums.*;
import com.hightop.benyin.share.domain.service.RegionDomainService;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.benyin.storage.application.service.StorageOutWarehouseService;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.domain.service.WarehouseServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.entity.Warehouse;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商退货单服务
 *
 * <AUTHOR>
 * @date 2023-12-06 14:48:56
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class ManufacterReturnService {

    ManufacterReturnServiceDomain manufacterReturnServiceDomain;

    WarehouseServiceDomain warehouseServiceDomain;

    ManufacturerServiceDomain manufacturerServiceDomain;

    ManufacterReturnGoodsServiceDomain manufacterReturnGoodsServiceDomain;

    ManufacterOrderServiceDomain manufacterOrderServiceDomain;

    ManufacterOrderGoodsServiceDomain manufacterOrderGoodsServiceDomain;

    PurchasePayVoucherServiceDomain purchasePayVoucherServiceDomain;

    ManufacturerDeliveryGoodsServiceDomain manufacturerDeliveryGoodsServiceDomain;
    ManufacturerDeliveryServiceDomain manufacturerDeliveryServiceDomain;
    RegionDomainService regionDomainService;
    ManufacturerDeliveryRecordServiceDomain manufacturerDeliveryRecordServiceDomain;

    PurchaseOrderGoodsServiceDomain purchaseOrderGoodsServiceDomain;

    SequenceDomainService sequenceDomainService;

    PurchaseOrderServiceDomain purchaseOrderServiceDomain;

    PartProductTreeDomainService partProductTreeDomainService;

    PurchasePaymentServiceDomain purchasePaymentServiceDomain;

    StorageOutWarehouseService storageOutWarehouseService;

    ManufacturerBackRecordServiceDomain manufacturerBackRecordServiceDomain;

    ManufacterReturnRecordServiceDomain manufacterReturnRecordServiceDomain;

    public DataGrid<ManufacturerReturn> page(ManufacterOrderQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterReturnServiceDomain.selectJoinList(ManufacturerReturn.class, MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerReturn.class)
                        .selectAs(Manufacturer::getName, ManufacturerReturn::getManufacturerName)
                        .selectAs(UserPrivacy::getMobileNumber, ManufacturerReturn::getInitiatorPhone)
                        .selectAs(UserBasic::getName, ManufacturerReturn::getReceiveByName)
                        .leftJoin(UserPrivacy.class, UserPrivacy::getId, ManufacturerReturn::getCreatedBy)
                        .leftJoin(UserBasic.class, UserBasic::getId, ManufacturerReturn::getCreatedBy)
                        .leftJoin(Manufacturer.class, Manufacturer::getId, ManufacturerReturn::getManufacturerId)
                        .leftJoin(ManufacturerBackRecord.class, ManufacturerBackRecord::getManufacturerReturnCode, ManufacturerReturn::getCode)
                        .like(StringUtils.isNotBlank(query.getCode()), ManufacturerReturn::getCode, query.getCode())
                        .like(StringUtils.isNotBlank(query.getManufacturerName()), Manufacturer::getName, query.getManufacturerName())
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), ManufacturerReturn::getPurchaseCode, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getManufacturerOrderCode()), ManufacturerReturn::getManufacturerOrderCode, query.getManufacturerOrderCode())
                        .like(StringUtils.isNotBlank(query.getReceiveCompany()), ManufacturerReturn::getReceiveCompany, query.getReceiveCompany())
                        .like(StringUtils.isNotBlank(query.getInitiatorPhone()), UserPrivacy::getMobileNumber, query.getInitiatorPhone())
                        .like(StringUtils.isNotBlank(query.getTrackingNumber()), ManufacturerBackRecord::getTrackingNumber, query.getTrackingNumber())
                        .eq(query.getRefundStatus() != null && query.getRefundStatus().size() == 1, ManufacturerReturn::getRefundStatus, CollectionUtils.isNotEmpty(query.getRefundStatus()) ? query.getRefundStatus().get(0) : null)
                        .in(CollectionUtils.isNotEmpty(query.getRefundStatus()) && query.getRefundStatus().size() > 1, ManufacturerReturn::getRefundStatus, query.getRefundStatus())
                        .eq(query.getCreatedByID() != null, ManufacturerReturn::getCreatedBy, query.getCreatedByID())
                        .like(StringUtils.isNotBlank(query.getCreatedBy()), UserBasic::getName, query.getCreatedBy())
                        .ge(StringUtils.isNotBlank(query.getStartDate()), ManufacturerReturn::getCreatedAt, query.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(query.getEndDate()), ManufacturerReturn::getCreatedAt, query.getEndDate() + " 23:59:59")
                        .orderByDesc(ManufacturerReturn::getCreatedAt)
                )
        );
    }

    public ManufacterReturnDto getSummary(ManufacterReturnQuery query) {
        return manufacterReturnGoodsServiceDomain.selectJoinOne(ManufacterReturnDto.class,
                MPJWrappers.lambdaJoin()
                        .selectSum(ManufacturerReturnGoods::getNumber, ManufacterReturnDto::getReturnNum)
                        .selectSum(ManufacturerReturnGoods::getAmount, ManufacterReturnDto::getReturnAmount)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ManufacturerReturnGoods::getArticleCode)
                        .leftJoin(ManufacturerOrderGoods.class, ManufacturerOrderGoods::getId, ManufacturerReturnGoods::getPurchaseOrderGoodsId)
                        .leftJoin(ManufacturerOrder.class, ManufacturerOrder::getCode, ManufacturerOrderGoods::getManufacturerOrderCode)
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), ManufacturerOrder::getPurchaseCode, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getManufacturerOrderCode()), ManufacturerOrder::getCode, query.getManufacturerOrderCode())
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), ManufacturerOrder::getPurchaseCode, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getArticleCode()), ManufacturerReturnGoods::getArticleCode, query.getArticleCode())
                        .like(StringUtils.isNotBlank(query.getPurchaseCode()), StorageArticle::getName, query.getPurchaseCode())
                        .like(StringUtils.isNotBlank(query.getOemNumber()), StorageArticle::getNumberOem, query.getOemNumber())
                        .like(StringUtils.isNotBlank(query.getOemNumber()), StorageArticle::getNumberOem, query.getOemNumber())
                        .ge(query.getReturnTimeStart() != null, ManufacturerReturn::getCreatedAt, query.getReturnTimeStart() + " 00:00:00")
                        .le(query.getReturnTimeEnd() != null, ManufacturerReturn::getCreatedAt, query.getReturnTimeEnd() + " 23:59:59")
                        .orderByDesc(ManufacturerReturnGoods::getCreatedAt)
        );
    }

    public DataGrid<ManufacterReturnGoodsVo> articlePage(ManufacterReturnQuery query) {
        return PageHelper.startPage(query, p ->manufacterReturnGoodsServiceDomain.articlePage(query)
        ).peek(p -> {
            p.setProductTreeDtoList(partProductTreeDomainService.getByPartId(p.getPartId()));
        });
    }
    public DataGrid<ManufacturerReturnGoods> detailPage(ManufacterReturnQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterReturnGoodsServiceDomain.selectJoinList(ManufacturerReturnGoods.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(ManufacturerReturnGoods.class)
                                .selectAs(StorageArticle::getNumberOem, ManufacturerReturnGoods::getOemNumber)
                                .selectAs(StorageArticle::getName, ManufacturerReturnGoods::getArticleName)
                                .selectAs(StorageArticle::getUnit, ManufacturerReturnGoods::getUnit)
                                .selectAs(StorageArticle::getPartId, ManufacturerReturnGoods::getPartId)
                                .selectAs(ManufacturerOrder::getCode, ManufacturerReturnGoods::getManufacturerOrderCode)
                                .selectAs(ManufacturerOrder::getInitiatorName, ManufacturerReturnGoods::getInitiatorName)
                                .selectAs(Manufacturer::getName, ManufacturerReturnGoods::getManufacturerName)
                                .selectAs(ManufacturerOrder::getPurchaseCode, ManufacturerReturnGoods::getPurchaseOrderCode)
                                .selectAs(ManufacturerOrderGoods::getNumber, ManufacturerReturnGoods::getBuyNumber)
                                .leftJoin(StorageArticle.class, StorageArticle::getCode, ManufacturerReturnGoods::getArticleCode)
                                .leftJoin(ManufacturerOrderGoods.class, ManufacturerOrderGoods::getId, ManufacturerReturnGoods::getPurchaseOrderGoodsId)
                                .leftJoin(ManufacturerOrder.class, ManufacturerOrder::getCode, ManufacturerOrderGoods::getManufacturerOrderCode)
                                .leftJoin(Manufacturer.class, Manufacturer::getId, ManufacturerOrder::getManufacturerId)
                                .like(StringUtils.isNotBlank(query.getPurchaseCode()), ManufacturerOrder::getPurchaseCode, query.getPurchaseCode())
                                .like(StringUtils.isNotBlank(query.getManufacturerName()), Manufacturer::getName, query.getManufacturerName())
                                .like(StringUtils.isNotBlank(query.getManufacturerOrderCode()), ManufacturerOrder::getCode, query.getManufacturerOrderCode())
                                .like(StringUtils.isNotBlank(query.getArticleCode()), ManufacturerReturnGoods::getArticleCode, query.getArticleCode())
                                .like(StringUtils.isNotBlank(query.getArticleName()), StorageArticle::getName, query.getArticleName())
                                .like(StringUtils.isNotBlank(query.getOemNumber()), StorageArticle::getNumberOem, query.getOemNumber())
                                .ge(query.getReturnTimeStart() != null, ManufacturerReturn::getCreatedAt, query.getReturnTimeStart() + " 00:00:00")
                                .le(query.getReturnTimeEnd() != null, ManufacturerReturn::getCreatedAt, query.getReturnTimeEnd() + " 23:59:59")
                                .orderByDesc(ManufacturerReturnGoods::getCreatedAt)
                )
        ).peek(p -> {
            p.setProductTreeDtoList(partProductTreeDomainService.getByPartId(p.getPartId()));
        });
    }

    /**
     * 创建供应商退货订单
     *
     * @param manufacterReturnDto
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean manufacterReturn(ManufacterReturnDto manufacterReturnDto) {
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, manufacterReturnDto.getPurchaseCode()).one();
        if (Objects.isNull(purchaseOrder)) {
            throw new MaginaException("采购单不存在");
        }
        //退货明细
        List<PurchaseOrderGoods> purchaseOrderGoodsList = manufacterReturnDto.getReturnParams().stream()
                .filter(v -> v.getCurrNum() != null && v.getCurrNum() > 0L).collect(Collectors.toList());

        Map<Long, List<PurchaseOrderGoods>> purchaseOrderGoodsMap = purchaseOrderGoodsList.stream().collect(Collectors.groupingBy(PurchaseOrderGoods::getManufacturerId));
        for (Map.Entry<Long, List<PurchaseOrderGoods>> entry : purchaseOrderGoodsMap.entrySet()) {
            Manufacturer manufacturer = manufacturerServiceDomain.getById(entry.getKey());

            //一个采购单同一供应商只会生成一个供应商订单
            ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getManufacturerId, entry.getKey())
                    .eq(ManufacturerOrder::getPurchaseCode, purchaseOrder.getPurchaseCode()).one();

            //未生成供应商订单 容错处理
            if (manufacturerOrder == null) {
                throw new MaginaException("采购订单中供应商[" + manufacturer.getName() + "]未生成供应商订单");
            }
            manufacturerOrder.setRefundStatus(ManufactureReturnStatusEnum.WAIT_CONFIRM);
            manufacterOrderServiceDomain.updateById(manufacturerOrder);

            //退货单主表
            ManufacturerReturn manufacturerReturn = new ManufacturerReturn();
            String code = sequenceDomainService.nextDateSequence("CGTH", 6);
            manufacturerReturn.setCode(code);
            manufacturerReturn.setWarehouseId(purchaseOrder.getWarehouseId());
            manufacturerReturn.setManufacturerId(entry.getKey());
            manufacturerReturn.setCompanyCode(manufacturerOrder.getCompanyCode());
            manufacturerReturn.setReceiveCompany(manufacturerOrder.getReceiveCompany());
            manufacturerReturn.setBank(manufacterReturnDto.getBank());
            manufacturerReturn.setBankClient(manufacterReturnDto.getBankClient());
            manufacturerReturn.setBankAccount(manufacterReturnDto.getBankAccount());
            manufacturerReturn.setAccount(manufacterReturnDto.getAccount());
            manufacturerReturn.setRefundType(manufacterReturnDto.getRefundType());
            manufacturerReturn.setAccount(manufacterReturnDto.getAccount());
            manufacturerReturn.setManufacturerOrderCode(manufacturerOrder.getCode());
            manufacturerReturn.setPurchaseCode(manufacturerOrder.getPurchaseCode());
            manufacturerReturn.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));

            //是否付款
            PurchasePayment purchasePayment = purchasePaymentServiceDomain.lambdaQuery()
                    .like(PurchasePayment::getManufacterOrderCodes, manufacturerOrder.getCode())
                    .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.CLOSED)
                    .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.REJECT)
                    .one();
            if (purchasePayment != null) {
                List<PurchasePayVoucher> purchasePayVouchers = purchasePayVoucherServiceDomain.lambdaQuery().eq(PurchasePayVoucher::getPaymentCode, purchasePayment.getCode()).list();
                if (CollectionUtils.isNotEmpty(purchasePayVouchers)) {
                    manufacturerReturn.setIsPay(true);
                } else {
                    manufacturerReturn.setIsPay(false);
                }
            }

            ManufacturerDelivery manufacturerDelivery = manufacturerDeliveryServiceDomain.lambdaQuery()
                    .eq(ManufacturerDelivery::getManufacturerOrderCode, manufacturerOrder.getCode()).one();
            if (manufacturerDelivery != null) {
                manufacturerReturn.setProvince(manufacturerDelivery.getProvince());
                manufacturerReturn.setCity(manufacturerDelivery.getProvince());
                manufacturerReturn.setCounty(manufacturerDelivery.getProvince());
                manufacturerReturn.setOrderAddress(manufacturerDelivery.getAddress());
            } else {
                Warehouse warehouse = warehouseServiceDomain.getById(manufacturerOrder.getWarehouseId());
                manufacturerReturn.setProvince(warehouse.getProvince());
                manufacturerReturn.setCity(warehouse.getCity());
                manufacturerReturn.setCounty(warehouse.getCounty());
                manufacturerReturn.setOrderAddress(warehouse.getAddr());
            }

            //退货明细处理
            Long totalReturnNum = 0L;
            Long totalReturnAmount = 0L;

            List<ManufacturerReturnGoods> manufacturerReturnGoodsList = Lists.newArrayList();
            String reason = "";
            for (PurchaseOrderGoods returnOrderGoods : entry.getValue()) {
                if (StringUtils.isNotBlank(returnOrderGoods.getReason())) {
                    reason = reason + returnOrderGoods.getReason() + ",";
                }
                //采购单明细
                PurchaseOrderGoods purchaseOrderGoods = purchaseOrderGoodsServiceDomain.getById(returnOrderGoods.getId());
                Long currentReturnNum = returnOrderGoods.getCurrNum();
                Long totalNum = purchaseOrderGoods.getRefundNum() + currentReturnNum;
                Long totalAmount = totalNum * purchaseOrderGoods.getPrice();
                purchaseOrderGoods.setRefundNum(totalNum);
                purchaseOrderGoods.setRefundAmount(totalAmount);
                purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);
                totalReturnNum += currentReturnNum;
                totalReturnAmount += currentReturnNum * purchaseOrderGoods.getPrice();
                if (totalNum.compareTo(purchaseOrderGoods.getNumber()) > 0) {
                    throw new MaginaException("供应商[" + manufacturer.getName() + "],商品编号[" + purchaseOrderGoods.getArticleCode() + "]退货数量超过采购数量");
                }

                //供应商订单
                ManufacturerOrderGoods manufacturerOrderGoods = manufacterOrderGoodsServiceDomain.lambdaQuery()
                        .eq(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode())
                        .eq(ManufacturerOrderGoods::getPurchaseOrderGoodsId, returnOrderGoods.getId())
                        .one();
                //构建供应商退货单明细
                ManufacturerReturnGoods manufacturerReturnGoods = new ManufacturerReturnGoods();
                manufacturerReturnGoods.setManufacturerReturnCode(code);
                manufacturerReturnGoods.setManufacturerId(entry.getKey());
                manufacturerReturnGoods.setAmount(totalAmount);
                manufacturerReturnGoods.setNumber(currentReturnNum);
                manufacturerReturnGoods.setArticleCode(purchaseOrderGoods.getArticleCode());
                manufacturerReturnGoods.setArticleType(purchaseOrderGoods.getArticleType());
                manufacturerReturnGoods.setArticleId(purchaseOrderGoods.getArticleId());
                manufacturerReturnGoods.setPrice(purchaseOrderGoods.getPrice());
                manufacturerReturnGoods.setPurchaseOrderGoodsId(purchaseOrderGoods.getId());
                manufacturerReturnGoods.setManufacturerOrderGoodsId(manufacturerOrderGoods.getId());
                manufacturerReturnGoodsList.add(manufacturerReturnGoods);
            }
            manufacturerReturn.setAmount(totalReturnAmount);
            manufacturerReturn.setNumber(totalReturnNum);
            manufacturerReturn.setConsigneeAddress(manufacturer.getAddress());
            manufacturerReturn.setConsigneePhone(StringUtils.isNotBlank(manufacturer.getAfterSalePhone()) ? manufacturer.getAfterSalePhone() : manufacturer.getLegalPersonTel());
            manufacturerReturn.setConsignee(StringUtils.isNotBlank(manufacturer.getAfterSaleName()) ? manufacturer.getAfterSaleName() : manufacturer.getLegalPerson());
            manufacturerReturn.setReason(StringUtils.isNotBlank(reason) ? reason.substring(0, reason.length() - 1) : "");
            manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_CONFIRM);
            manufacturerReturn.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            manufacterReturnGoodsServiceDomain.saveBatch(manufacturerReturnGoodsList);
            manufacterReturnServiceDomain.save(manufacturerReturn);

            manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_ADD.getTitle(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_ADD.getContent());
        }

        return Boolean.TRUE;
    }

    public Boolean receive(Long id) {
        ManufacturerReturn manufacturerReturn = manufacterReturnServiceDomain.getById(id);
        if (manufacturerReturn.getRefundType().equals(RefundTypeEnum.CASH)) {
            manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_REFUND);
        } else {
            manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.SUCCESS);
            Manufacturer manufacturer = manufacturerServiceDomain.getById(manufacturerReturn.getManufacturerId());
            manufacturer.setFundBalance(manufacturer.getFundBalance() + manufacturerReturn.getAmount());
            manufacturerServiceDomain.updateById(manufacturer);
        }
        manufacturerReturn.setReceiveBy(ApplicationSessions.id());
        manufacturerReturn.setReceiveAt(LocalDateTime.now());
        manufacterReturnServiceDomain.updateById(manufacturerReturn);
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, manufacturerReturn.getPurchaseCode()).one();
        purchaseOrder.setRefundStatus(manufacturerReturn.getRefundStatus());
        purchaseOrderServiceDomain.updateById(purchaseOrder);
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                .eq(ManufacturerOrder::getCode, manufacturerReturn.getManufacturerOrderCode())
                .one();
        manufacturerOrder.setRefundStatus(manufacturerReturn.getRefundStatus());
        manufacterOrderServiceDomain.updateById(manufacturerOrder);
        manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_1.getTitle(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_1.getContent());
        return Boolean.TRUE;
    }

    public Boolean refund(ManufacteRefundParam manufacteRefundParam) {
        ManufacturerReturn manufacturerReturn = manufacterReturnServiceDomain.getById(manufacteRefundParam.getId());
        manufacturerReturn.setPicUrls(manufacteRefundParam.getPicUrls());
        manufacturerReturn.setSerialNumber(manufacteRefundParam.getSerialNumber());
        manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.SUCCESS);
        manufacterReturnServiceDomain.updateById(manufacturerReturn);

        //采购单处理
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, manufacturerReturn.getPurchaseCode()).one();
        purchaseOrder.setRefundStatus(manufacturerReturn.getRefundStatus());
        List<PurchaseOrderGoods> purchaseOrderGoods = purchaseOrderGoodsServiceDomain.lambdaQuery()
                .eq(PurchaseOrderGoods::getPurchaseOrderCode, purchaseOrder.getPurchaseCode()).list();
        List<PurchaseOrderGoods> refundList = purchaseOrderGoods.stream().filter(v -> v.getRefundAmount().compareTo(v.getSumPrice()) == 0).collect(Collectors.toList());
        purchaseOrder.setRefundStatus(refundList.size() == purchaseOrderGoods.size() ?
                ManufactureReturnStatusEnum.SUCCESS : ManufactureReturnStatusEnum.PART_REFUND);
        purchaseOrderServiceDomain.updateById(purchaseOrder);

        //供应商订单处理
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                .eq(ManufacturerOrder::getCode, manufacturerReturn.getManufacturerOrderCode())
                .one();
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = manufacterOrderGoodsServiceDomain.lambdaQuery()
                .eq(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode()).list();
        List<ManufacturerOrderGoods> orderRefundList = manufacturerOrderGoodsList.stream().filter(v -> v.getRefundAmount().compareTo(v.getAmount()) == 0).collect(Collectors.toList());
        manufacturerOrder.setRefundStatus(orderRefundList.size() == manufacturerOrderGoodsList.size() ?
                ManufactureReturnStatusEnum.SUCCESS : ManufactureReturnStatusEnum.PART_REFUND);
        manufacterOrderServiceDomain.updateById(manufacturerOrder);

        //更新流程
        manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_3.getTitle(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_3.getContent());
        return Boolean.TRUE;
    }


    /**
     * 退货确认
     *
     * @param manufacturerReturn
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean doReturn(ManufacturerReturn manufacturerReturn) {

        if (manufacturerReturn.getRefundStatus() != null && manufacturerReturn.getRefundStatus().equals(ManufactureReturnStatusEnum.CLOSED)) {
            //采购订单退货数量恢复
            manufacturerReturn.getManufacturerReturnGoods().forEach(v -> {
                PurchaseOrderGoods purchaseOrderGoods = purchaseOrderGoodsServiceDomain.getById(v.getPurchaseOrderGoodsId());
                purchaseOrderGoods.setRefundNum(purchaseOrderGoods.getRefundNum() - v.getNumber());
                purchaseOrderGoods.setRefundAmount(purchaseOrderGoods.getRefundAmount() - v.getAmount());
                purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);
            });

            //退货流程记录 --拒绝
            manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                    ManufacturerReturnRecordEnum.REFUSE.getTitle(),
                    ManufacturerReturnRecordEnum.REFUSE.getContent());

            //个供应商订单状态恢复
            ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getCode, manufacturerReturn.getManufacturerOrderCode()).one();
            manufacturerOrder.setRefundStatus(ManufactureReturnStatusEnum.NO);
            manufacterOrderServiceDomain.updateById(manufacturerOrder);
            return manufacterReturnServiceDomain.updateById(manufacturerReturn);
        }

        if(StringUtils.isBlank(manufacturerReturn.getConsigneeAddress()) ){
            throw new MaginaException("请填写收货地址");
        }

        List<ManufacturerReturnGoods> manufacturerReturnGoodsList = manufacturerReturn.getManufacturerReturnGoods();

        //一个采购单同一供应商只会生成一个供应商订单
        ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                .eq(ManufacturerOrder::getCode, manufacturerReturn.getManufacturerOrderCode()).one();

        //采购单
        ManufacturerDelivery manufacturerDelivery = manufacturerDeliveryServiceDomain.lambdaQuery()
                .eq(ManufacturerDelivery::getManufacturerOrderCode, manufacturerOrder.getCode()).one();

        //付款单
        PurchasePayment purchasePayment = purchasePaymentServiceDomain.lambdaQuery()
                .like(PurchasePayment::getManufacterOrderCodes, manufacturerOrder.getCode())
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.REJECT)
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.CLOSED)
                .one();

        //需要寄回的数量
        Long totalReturnBackAmount = 0L;
        Long deliveryNum = 0L;//已发数量
        Long refoundAmount = 0L;
        for (ManufacturerReturnGoods manufacturerReturnGoods : manufacturerReturnGoodsList) {
            PurchaseOrderGoods purchaseOrderGoods = purchaseOrderGoodsServiceDomain.getById(manufacturerReturnGoods.getPurchaseOrderGoodsId());
            Long totalNum = manufacturerReturnGoods.getNumber() ;
            Long totalAmount = totalNum * purchaseOrderGoods.getPrice();
            if (totalNum.compareTo(purchaseOrderGoods.getNumber()) > 0) {
                throw new MaginaException("商品编号[" + purchaseOrderGoods.getArticleCode() + "]退货数量超过采购数量" + purchaseOrderGoods.getNumber());
            }
            purchaseOrderGoods.setRefundNum(totalNum);
            purchaseOrderGoods.setRefundAmount(totalAmount);
            purchaseOrderGoodsServiceDomain.updateById(purchaseOrderGoods);

            //供应商订单
            ManufacturerOrderGoods manufacturerOrderGoods = manufacterOrderGoodsServiceDomain.getById(manufacturerReturnGoods.getManufacturerOrderGoodsId());
            manufacturerOrderGoods.setRefundAmount(totalAmount);
            refoundAmount+=totalAmount;
            manufacturerOrderGoods.setRefundNum(totalNum);
            manufacterOrderGoodsServiceDomain.updateById(manufacturerOrderGoods);

            //发货单
            ManufacturerDeliveryGoods manufacturerDeliveryGoods = manufacturerDeliveryGoodsServiceDomain.lambdaQuery()
                    .eq(ManufacturerDeliveryGoods::getManufacturerGoodsId, manufacturerOrderGoods.getId())
                    .eq(ManufacturerDeliveryGoods::getPurchaseOrderGoodsId, purchaseOrderGoods.getId())
                    .one();

            if (manufacturerDeliveryGoods != null) {
                deliveryNum += manufacturerDeliveryGoods.getDeliveryNum();
                // 退货数量小于待发货数量 直接取消发货 退款即可
                Long waitDeliveryNum = manufacturerDeliveryGoods.getNumber() - manufacturerDeliveryGoods.getDeliveryNum() - manufacturerDeliveryGoods.getCancelNum();
                if (totalNum.compareTo(waitDeliveryNum) <= 0) {
                    manufacturerDeliveryGoods.setCancelNum(manufacturerDeliveryGoods.getCancelNum() + totalNum);
                } else {
                    //退货数量大于待发货数量 需要判断是否收货入库
                    Long overReceiveNum = totalNum - waitDeliveryNum;//超出待发货部分即已收货
                    List<ManufacturerDeliveryRecord> manufacturerDeliveryRecords = manufacturerDeliveryRecordServiceDomain.lambdaQuery()
                            .eq(ManufacturerDeliveryRecord::getDeliveryGoodsId, manufacturerDeliveryGoods.getId()).list();
                    //已收货数量
                    Long receiveNum = manufacturerDeliveryRecords.stream().mapToLong(ManufacturerDeliveryRecord::getReceiveNum).sum();
                    manufacturerReturnGoods.setReturnBackNum(overReceiveNum);
                    manufacturerReturnGoods.setRefundNum(waitDeliveryNum);
                    totalReturnBackAmount += overReceiveNum;
                    manufacterReturnGoodsServiceDomain.updateById(manufacturerReturnGoods);
                }
                manufacturerDeliveryGoodsServiceDomain.updateById(manufacturerDeliveryGoods);
            }
        }

        if (Objects.nonNull(purchasePayment)) {
            purchasePayment.setRefundAmount(purchasePayment.getRefundAmount() + manufacturerReturn.getAmount());
            purchasePaymentServiceDomain.updateById(purchasePayment);
        }

        //确定是否全部退货并且未发货
        List<ManufacturerOrderGoods> manufacturerOrderGoodsList = manufacterOrderGoodsServiceDomain.lambdaQuery().eq(ManufacturerOrderGoods::getManufacturerOrderCode, manufacturerOrder.getCode()).list();
        List<ManufacturerOrderGoods> allReturnList = manufacturerOrderGoodsList.stream().filter(v -> v.getNumber().compareTo(v.getRefundNum()) == 0).collect(Collectors.toList());
        //全部退货完成并且未发货
        Boolean onlyRefund = false;//是否需要退款 判断是否付款
        if (allReturnList.size() == manufacturerOrderGoodsList.size() && deliveryNum == 0L) {
            if (Objects.nonNull(manufacturerDelivery)) {
                manufacturerDelivery.setStatus(ManufactureOrderStatusEnum.CLOSED);
            }
            manufacturerOrder.setStatus(ManufactureOrderStatusEnum.CLOSED);
            if(!manufacturerReturn.getIsPay()){
                manufacturerOrder.setAmount(manufacturerOrder.getAmount() - refoundAmount);
            }
            if (Objects.nonNull(purchasePayment)) {
                List<PurchasePayVoucher> purchasePayVouchers = purchasePayVoucherServiceDomain.lambdaQuery().eq(PurchasePayVoucher::getPaymentCode, purchasePayment.getCode()).list();
                if (CollectionUtils.isEmpty(purchasePayVouchers)) {
                    purchasePayment.setStatus(PurchasePayStatusEnum.CLOSED);
                    purchasePayment.setRemark(purchasePayment.getRemark() + " 订单" + manufacturerOrder.getCode() + "已关闭");
                    purchasePaymentServiceDomain.updateById(purchasePayment);
                } else {
                    onlyRefund = true;
                    manufacturerReturn.setIsPay(true);
                }
            }
            if (onlyRefund) {
                manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_RETURN);
                //退货流程记录 --退款中
                manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                        ManufacturerReturnRecordEnum.REFUND_ORDER_2.getTitle(),
                        ManufacturerReturnRecordEnum.REFUND_ORDER_2.getContent());
            } else {
                manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.SUCCESS);
                //退货流程记录 --尚未生效
                manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                        ManufacturerReturnRecordEnum.REFUND_ORDER_4.getTitle(),
                        ManufacturerReturnRecordEnum.REFUND_ORDER_4.getContent());
            }
        }

        //创建出库单 用overReceiveNum
        if (totalReturnBackAmount > 0L) {
            manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_RETURN);
            //退货流程记录 --退货中
            manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_1.getTitle(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_1.getContent());
            storageOutWarehouseService.createByPurchaseReturn(manufacturerReturn, manufacturerReturnGoodsList);
        } else {
            manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_REFUND);
            //退货流程记录 --退款中
            manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_1.getTitle(),
                    ManufacturerReturnRecordEnum.REFUND_ORDER_1.getContent());
        }
        manufacturerOrder.setRefundStatus(manufacturerReturn.getRefundStatus());
        manufacterOrderServiceDomain.updateById(manufacturerOrder);
        manufacterReturnServiceDomain.updateById(manufacturerReturn);
        //采购订单
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.lambdaQuery()
                .eq(PurchaseOrder::getPurchaseCode, manufacturerReturn.getPurchaseCode()).one();
        purchaseOrder.setRefundStatus(manufacturerReturn.getRefundStatus());
        return purchaseOrderServiceDomain.updateById(purchaseOrder);
    }

    public Boolean returnBack(ManufacteRturnParam manufacteRturnParam) {
        ManufacturerReturn manufacturerReturn = manufacterReturnServiceDomain.getById(manufacteRturnParam.getId());
        if (Objects.isNull(manufacturerReturn)) {
            throw new MaginaException("退货单不存在");
        }
        manufacteRturnParam.getDeliveryDetails().forEach(v -> {
            v.setManufacturerReturnCode(manufacturerReturn.getCode());
            v.setPurchaseCode(manufacturerReturn.getPurchaseCode());
            v.setManufacturerOrderCode(manufacturerReturn.getManufacturerOrderCode());
            v.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        });

        manufacteRturnParam.getManufacturerReturnGoods().forEach(v -> {
            if(StringUtils.isBlank(v.getTrackingNumber())){
                v.setTrackingNumber(manufacteRturnParam.getDeliveryDetails().get(0).getTrackingNumber());
            }
        });

        manufacterReturnGoodsServiceDomain.updateBatchById(manufacteRturnParam.getManufacturerReturnGoods());

        Long shippingFee = manufacteRturnParam.getDeliveryDetails().stream().mapToLong(v -> v.getShippingFee()).sum();
        manufacturerReturn.setShippingFee(shippingFee);
        manufacturerReturn.setRefundStatus(ManufactureReturnStatusEnum.WAIT_RECEIVE);
        manufacterReturnServiceDomain.updateById(manufacturerReturn);
        //退货流程记录 --退款中
        manufacterReturnRecordServiceDomain.saveRecord(manufacturerReturn.getCode(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_2.getTitle(),
                ManufacturerReturnRecordEnum.RETURN_ORDER_2.getContent());
        return manufacturerBackRecordServiceDomain.saveOrUpdateBatch(manufacteRturnParam.getDeliveryDetails());
    }

    /**
     * 明细查看
     *
     * @param id
     * @return
     */
    public ManufacturerReturn getById(Long id) {
        ManufacturerReturn manufacturerReturn = manufacterReturnServiceDomain.selectJoinOne(ManufacturerReturn.class, MPJWrappers.lambdaJoin()
                .selectAll(ManufacturerReturn.class)
                .selectAs(Manufacturer::getName, ManufacturerOrder::getManufacturerName)
                .selectAs(Manufacturer::getCode, ManufacturerOrder::getManufacturerCode)
                .selectAs(ManufacturerOrder::getInitiatorName, ManufacturerReturn::getInitiatorName)
                .selectAs(ManufacturerOrder::getInitiatorPhone, ManufacturerReturn::getInitiatorPhone)
                .selectAs(ManufacturerOrder::getCreatedAt, ManufacturerReturn::getInitiatorTime)
                .selectAs(ManufacturerOrder::getAmount, ManufacturerReturn::getOrderAmount)
                .selectAs(UserBasic::getName, ManufacturerReturn::getReceiveByName)
                .leftJoin(Manufacturer.class, Manufacturer::getId, ManufacturerReturn::getManufacturerId)
                .leftJoin(ManufacturerOrder.class, ManufacturerOrder::getCode, ManufacturerReturn::getManufacturerOrderCode)
                .leftJoin(UserBasic.class, UserBasic::getId, ManufacturerReturn::getReceiveBy)
                .eq(ManufacturerReturn::getId, id));

        if (manufacturerReturn.getCounty() != null) {
            Region region = regionDomainService.lambdaQuery().eq(Region::getCode, manufacturerReturn.getCounty()).one();
            manufacturerReturn.setCountyName(region.getName());
        }
        if (manufacturerReturn.getCity() != null) {
            Region region = regionDomainService.lambdaQuery().eq(Region::getCode, manufacturerReturn.getCity()).one();
            manufacturerReturn.setCityName(region.getName());
        }
        if (manufacturerReturn.getProvince() != null) {
            Region region = regionDomainService.lambdaQuery().eq(Region::getCode, manufacturerReturn.getProvince()).one();
            manufacturerReturn.setProvinceName(region.getName());
        }
        PurchasePayment purchasePayment = purchasePaymentServiceDomain.lambdaQuery()
                .like(PurchasePayment::getManufacterOrderCodes, manufacturerReturn.getManufacturerOrderCode())
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.REJECT)
                .ne(PurchasePayment::getStatus, PurchasePayStatusEnum.CLOSED)
                .one();
        if (Objects.nonNull(purchasePayment)) {
            ManufacturerOrder manufacturerOrder = manufacterOrderServiceDomain.lambdaQuery()
                    .eq(ManufacturerOrder::getCode, manufacturerReturn.getManufacturerOrderCode())
                    .one();
            manufacturerReturn.setPayAmount(manufacturerOrder.getAmount());
            manufacturerReturn.setPaymentCode(purchasePayment.getCode());
        }


        List<ManufacturerReturnGoods> manufacturerReturnGoodsList = manufacterReturnGoodsServiceDomain.selectJoinList(ManufacturerReturnGoods.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ManufacturerReturnGoods.class)
                        .selectAs(StorageArticle::getNumberOem, ManufacturerReturnGoods::getOemNumber)
                        .selectAs(StorageArticle::getName, ManufacturerReturnGoods::getArticleName)
                        .selectAs(StorageArticle::getUnit, ManufacturerReturnGoods::getUnit)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ManufacturerReturnGoods::getArticleCode)
                        .eq(ManufacturerReturnGoods::getManufacturerReturnCode, manufacturerReturn.getCode())
        );
        manufacturerReturn.setManufacturerReturnGoods(manufacturerReturnGoodsList);

        List<ManufacturerBackRecord> manufacturerBackRecords = manufacturerBackRecordServiceDomain.lambdaQuery()
                .eq(ManufacturerBackRecord::getManufacturerReturnCode, manufacturerReturn.getCode())
                .list();
        manufacturerReturn.setBackRecordInfos(manufacturerBackRecords);

        List<ManufacturerReturnRecord> returnRecords = manufacterReturnRecordServiceDomain.lambdaQuery()
                .eq(ManufacturerReturnRecord::getManufacturerReturnCode, manufacturerReturn.getCode())
                .orderByAsc(ManufacturerReturnRecord::getCreatedAt)
                .list();
        manufacturerReturn.setReturnRecords(returnRecords);
        return manufacturerReturn;
    }

    /**
     * 获取采购单的退货信息
     *
     * @param id 采购单id
     * @return
     */
    public ManufacterReturnDto returnInfo(Long id) {
        PurchaseOrder purchaseOrder = purchaseOrderServiceDomain.getById(id);
        if (Objects.isNull(purchaseOrder)) {
            throw new MaginaException("采购单不存在");
        }
//        if (purchaseOrder.getOrderStatus().equals(PurchaseOrderStatusEnum.WAIT_CONFIRM)) {
//            throw new MaginaException("待确认采购单可直接关闭");
//        }
        //查找上一次退货信息
        ManufacturerReturn manufacturerReturn = manufacterReturnServiceDomain.lambdaQuery()
                //TODO 添加公司查询条件
//                .eq(ManufacturerReturn::getCompanyCode, purchaseOrder.getCode())
                .orderByDesc(ManufacturerReturn::getCreatedAt)
                .last(" LIMIT 1")
                .one();
        ManufacterReturnDto manufacterReturnDto = new ManufacterReturnDto();
        Warehouse warehouse = warehouseServiceDomain.getById(purchaseOrder.getWarehouseId());
        manufacterReturnDto.setWarehouseName(warehouse.getName());
        if (Objects.isNull(manufacturerReturn)) {
            manufacterReturnDto.setBank("中国建设银行");
            manufacterReturnDto.setBankAccount("51050148855800001792");
            manufacterReturnDto.setBankClient("中国建设银行成都龙潭支行");
            manufacterReturnDto.setAccount("四川至简智印科技有限公司");
        } else {
            BeanUtils.copyProperties(manufacturerReturn, manufacterReturnDto);
            // 明确将退款类型设置为null，避免前端显示默认值
            manufacterReturnDto.setRefundType(null);
        }
        manufacterReturnDto.setPurchaseCode(purchaseOrder.getPurchaseCode());
        List<PurchaseOrderGoods> purchaseOrderGoods = purchaseOrderGoodsServiceDomain.selectJoinList(PurchaseOrderGoods.class, MPJWrappers.lambdaJoin()
                .selectAll(PurchaseOrderGoods.class)
                .selectAs(StorageArticle::getNumberOem, PurchaseOrderGoods::getOemNumber)
                .selectAs(StorageArticle::getName, PurchaseOrderGoods::getArticleName)
                .selectAs(StorageArticle::getUnit, PurchaseOrderGoods::getUnit)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, PurchaseOrderGoods::getArticleCode)
                .eq(PurchaseOrderGoods::getPurchaseOrderCode, purchaseOrder.getPurchaseCode())
        );

        manufacterReturnDto.setReturnParams(purchaseOrderGoods);
        return manufacterReturnDto;
    }

    public DataGrid<ManufacterReturnGoodsVo> supplierPage(ManufacterReturnQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterReturnGoodsServiceDomain.supplierPage(query)
        );
    }

    public DataGrid<PurchaseMechineVo> mechinePage(ManufacterReturnQuery query) {
        return PageHelper.startPage(query, p ->
                manufacterReturnGoodsServiceDomain.mechinePage(query)
        );
    }

}
