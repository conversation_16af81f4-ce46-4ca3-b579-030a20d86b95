package com.hightop.benyin.storage.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 组合商品配置表实体
 *
 * <AUTHOR>
 * @date 2023-11-28 10:06:27
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_assembly_article_config")
@ApiModel
public class AssemblyArticleConfig {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("物品id")
    @TableField("article_id")
    Long articleId;

    @TableField("article_code")
    @ApiModelProperty("物品编号")
    String articleCode;

    @TableField("article_name")
    @ApiModelProperty("物品名称")
    String articleName;


    @TableField("oem_number")
    @ApiModelProperty("oem编号")
    String oemNumber;


    @ApiModelProperty("物品id")
    @TableField("parent_article_id")
    Long parentArticleId;

    @TableField("parent_article_code")
    @ApiModelProperty("物品编号")
    String parentArticleCode;

    @TableField("parent_article_name")
    @ApiModelProperty("物品名称")
    String parentArticleName;

    @TableField("purchase_price")
    @JsonAmount
    @ApiModelProperty("采购价")
    Long purchasePrice;

    @TableField("num")
    @ApiModelProperty("数量")
    Integer num;

    @TableField("price")
    @JsonAmount
    @ApiModelProperty("价格")
    Long price;

    @TableField(exist = false)
    @JsonAmount
    @ApiModelProperty("促销价格")
    Long promotionPrice;

    @TableField(exist = false)
    @ApiModelProperty("活动id")
    Long activityId;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    Integer deleted;
}
