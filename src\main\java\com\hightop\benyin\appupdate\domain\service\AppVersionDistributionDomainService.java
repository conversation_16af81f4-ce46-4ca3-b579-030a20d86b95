package com.hightop.benyin.appupdate.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import com.hightop.benyin.appupdate.infrastructure.mapper.AppVersionDistributionMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用版本分发领域服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionDistributionDomainService extends MPJBaseServiceImpl<AppVersionDistributionMapper, AppVersionDistribution> {
    
    /**
     * 根据目标类型和ID获取激活的分发关系
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 分发关系列表
     */
    public List<AppVersionDistribution> getActiveDistributionsByTarget(String targetType, String targetId) {
        return this.baseMapper.findActiveByTarget(targetType, targetId);
    }
    
    /**
     * 根据版本ID获取所有分发关系
     * @param versionId 版本ID
     * @return 分发关系列表
     */
    public List<AppVersionDistribution> getDistributionsByVersion(Long versionId) {
        return this.baseMapper.findByVersionId(versionId);
    }
    
    /**
     * 批量创建分发关系
     * @param distributions 分发关系列表
     * @return 是否成功
     */
    public boolean batchSave(List<AppVersionDistribution> distributions) {
        if (distributions == null || distributions.isEmpty()) {
            return true;
        }

        // 设置默认值（时间字段由MyBatis-Plus自动填充处理）
        LocalDateTime now = LocalDateTime.now();
        distributions.forEach(distribution -> {
            if (distribution.getAssignTime() == null) {
                distribution.setAssignTime(now);
            }
            if (distribution.getIsActive() == null) {
                distribution.setIsActive(true);
            }
            // createdAt 和 updatedAt 由 MyBatis-Plus 自动填充处理器自动设置
        });

        return this.saveBatch(distributions);
    }
    
    /**
     * 根据版本ID删除所有分发关系（逻辑删除）
     * @param versionId 版本ID
     * @return 是否成功
     */
    public boolean deleteByVersionId(Long versionId) {
        return this.lambdaUpdate()
                .set(AppVersionDistribution::getDeleted, 1)
                .eq(AppVersionDistribution::getVersionId, versionId)
                .update();
    }
    
    /**
     * 检查用户是否有指定版本的访问权限
     * @param versionId 版本ID
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 是否有权限
     */
    public boolean hasAccessPermission(Long versionId, String userId, String deviceId) {
        LambdaQueryWrapper<AppVersionDistribution> wrapper = new LambdaQueryWrapper<AppVersionDistribution>()
                .eq(AppVersionDistribution::getVersionId, versionId)
                .eq(AppVersionDistribution::getIsActive, true);
        
        // 检查用户权限
        if (userId != null) {
            wrapper.and(w -> w.eq(AppVersionDistribution::getTargetType, "USER")
                             .eq(AppVersionDistribution::getTargetId, userId));
        }
        
        // 检查设备权限
        if (deviceId != null) {
            wrapper.or(w -> w.eq(AppVersionDistribution::getTargetType, "DEVICE")
                             .eq(AppVersionDistribution::getTargetId, deviceId));
        }
        
        return this.count(wrapper) > 0;
    }
}
